version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_DB: karmsakha
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  ingestor:
    build: ./services/ingestor
    environment:
      - DATABASE_URL=*************************************/karmsakha
      - REDIS_URL=redis://redis:6379
      - BRIGHT_DATA_PROXY_URL=${BRIGHT_DATA_PROXY_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/ingestor:/app
      - /app/node_modules

  ranker:
    build: ./services/ranker
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=*************************************/karmsakha
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis

  worker:
    build: ./services/worker
    environment:
      - DATABASE_URL=*************************************/karmsakha
      - REDIS_URL=redis://redis:6379
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/worker:/app
      - /app/node_modules

  gateway:
    build: ./gateway
    ports:
      - "4000:4000"
    environment:
      - DATABASE_URL=*************************************/karmsakha
      - REDIS_URL=redis://redis:6379
      - RANKER_SERVICE_URL=http://ranker:8000
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
      - ranker

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_GATEWAY_URL=http://localhost:4000/graphql
    depends_on:
      - gateway

volumes:
  postgres_data:
  redis_data: