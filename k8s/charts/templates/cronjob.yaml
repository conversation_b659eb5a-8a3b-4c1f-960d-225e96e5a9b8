{{- if .Values.services.ingestor.enabled }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "karmsakha.fullname" . }}-job-ingestion
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: ingestor
spec:
  schedule: {{ .Values.services.ingestor.configMap.INGESTION_SCHEDULE | default "0 */4 * * *" | quote }}
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "karmsakha.selectorLabels" . | nindent 12 }}
            app.kubernetes.io/component: ingestor-job
        spec:
          serviceAccountName: {{ include "karmsakha.serviceAccountName" . }}
          securityContext:
            {{- toYaml .Values.podSecurityContext | nindent 12 }}
          restartPolicy: OnFailure
          containers:
            - name: job-ingestor
              {{- include "karmsakha.securityContext" . | nindent 14 }}
              image: {{ include "karmsakha.serviceImage" (dict "service" .Values.services.ingestor "Values" .Values "Chart" .Chart) }}
              imagePullPolicy: {{ .Values.services.ingestor.image.pullPolicy | default .Values.image.pullPolicy }}
              command: ["npm", "run", "ingest:all"]
              env:
                {{- include "karmsakha.commonEnv" . | nindent 16 }}
                {{- range $key, $value := .Values.services.ingestor.env }}
                - name: {{ $key }}
                  value: {{ $value | quote }}
                {{- end }}
                {{- range $secret := .Values.services.ingestor.secrets }}
                - name: {{ $secret.name }}
                  valueFrom:
                    secretKeyRef:
                      name: {{ include "karmsakha.fullname" $ }}-app-secrets
                      key: {{ $secret.key }}
                {{- end }}
              envFrom:
                - configMapRef:
                    name: {{ include "karmsakha.fullname" . }}-ingestor-config
                - configMapRef:
                    name: {{ include "karmsakha.fullname" . }}-app-config
              {{- include "karmsakha.volumeMounts" . | nindent 14 }}
              resources:
                limits:
                  cpu: 1000m
                  memory: 2Gi
                requests:
                  cpu: 500m
                  memory: 1Gi
          {{- include "karmsakha.volumes" . | nindent 10 }}
{{- end }}

{{- if .Values.services.audit.enabled }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "karmsakha.fullname" . }}-audit-cleanup
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: audit
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "karmsakha.selectorLabels" . | nindent 12 }}
            app.kubernetes.io/component: audit-cleanup
        spec:
          serviceAccountName: {{ include "karmsakha.serviceAccountName" . }}
          securityContext:
            {{- toYaml .Values.podSecurityContext | nindent 12 }}
          restartPolicy: OnFailure
          containers:
            - name: audit-cleanup
              {{- include "karmsakha.securityContext" . | nindent 14 }}
              image: {{ include "karmsakha.serviceImage" (dict "service" .Values.services.audit "Values" .Values "Chart" .Chart) }}
              imagePullPolicy: {{ .Values.services.audit.image.pullPolicy | default .Values.image.pullPolicy }}
              command: ["npm", "run", "cleanup:expired"]
              env:
                {{- include "karmsakha.commonEnv" . | nindent 16 }}
                {{- range $key, $value := .Values.services.audit.env }}
                - name: {{ $key }}
                  value: {{ $value | quote }}
                {{- end }}
                {{- range $secret := .Values.services.audit.secrets }}
                - name: {{ $secret.name }}
                  valueFrom:
                    secretKeyRef:
                      name: {{ include "karmsakha.fullname" $ }}-app-secrets
                      key: {{ $secret.key }}
                {{- end }}
              envFrom:
                - configMapRef:
                    name: {{ include "karmsakha.fullname" . }}-audit-config
                - configMapRef:
                    name: {{ include "karmsakha.fullname" . }}-app-config
              {{- include "karmsakha.volumeMounts" . | nindent 14 }}
              resources:
                limits:
                  cpu: 200m
                  memory: 256Mi
                requests:
                  cpu: 100m
                  memory: 128Mi
          {{- include "karmsakha.volumes" . | nindent 10 }}
{{- end }}

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "karmsakha.fullname" . }}-database-maintenance
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: maintenance
spec:
  schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 2
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "karmsakha.selectorLabels" . | nindent 12 }}
            app.kubernetes.io/component: database-maintenance
        spec:
          serviceAccountName: {{ include "karmsakha.serviceAccountName" . }}
          securityContext:
            {{- toYaml .Values.podSecurityContext | nindent 12 }}
          restartPolicy: OnFailure
          containers:
            - name: database-maintenance
              {{- include "karmsakha.securityContext" . | nindent 14 }}
              image: postgres:15-alpine
              command: 
                - /bin/sh
                - -c
                - |
                  psql $DATABASE_URL << 'EOF'
                  -- Update materialized views
                  REFRESH MATERIALIZED VIEW popular_companies;
                  
                  -- Vacuum and analyze tables
                  VACUUM ANALYZE jobs;
                  VACUUM ANALYZE users;
                  VACUUM ANALYZE applications;
                  VACUUM ANALYZE audit_logs;
                  
                  -- Reindex vector indexes for performance
                  REINDEX INDEX idx_jobs_embedding;
                  REINDEX INDEX idx_users_embedding;
                  
                  -- Clean up old audit logs beyond retention
                  DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '90 days';
                  
                  -- Update statistics
                  ANALYZE;
                  EOF
              env:
                - name: DATABASE_URL
                  valueFrom:
                    secretKeyRef:
                      name: {{ include "karmsakha.fullname" . }}-db-secret
                      key: database-url
              resources:
                limits:
                  cpu: 500m
                  memory: 512Mi
                requests:
                  cpu: 200m
                  memory: 256Mi