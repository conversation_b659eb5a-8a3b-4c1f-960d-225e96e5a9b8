{{- range $name, $config := .Values.configMaps }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "karmsakha.fullname" $ }}-{{ $name }}
  labels:
    {{- include "karmsakha.labels" $ | nindent 4 }}
data:
  {{- toYaml $config.data | nindent 2 }}
{{- end }}

{{- if .Values.services.ingestor.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "karmsakha.fullname" . }}-ingestor-config
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: ingestor
data:
  {{- range $key, $value := .Values.services.ingestor.configMap }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}

{{- if .Values.services.ranker.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "karmsakha.fullname" . }}-ranker-config
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: ranker
data:
  {{- range $key, $value := .Values.services.ranker.configMap }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}

{{- if .Values.services.worker.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "karmsakha.fullname" . }}-worker-config
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: worker
data:
  {{- range $key, $value := .Values.services.worker.configMap }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}

{{- if .Values.services.gateway.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "karmsakha.fullname" . }}-gateway-config
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: gateway
data:
  {{- range $key, $value := .Values.services.gateway.configMap }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}

{{- if .Values.services.frontend.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "karmsakha.fullname" . }}-frontend-config
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
data:
  {{- range $key, $value := .Values.services.frontend.configMap }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}

{{- if .Values.services.audit.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "karmsakha.fullname" . }}-audit-config
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
    app.kubernetes.io/component: audit
data:
  {{- range $key, $value := .Values.services.audit.configMap }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}