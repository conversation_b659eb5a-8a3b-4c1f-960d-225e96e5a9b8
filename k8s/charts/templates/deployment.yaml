{{- $root := . -}}
{{- range $serviceName, $service := .Values.services }}
{{- if $service.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "karmsakha.fullname" $root }}-{{ $serviceName }}
  labels:
    {{- include "karmsakha.labels" $root | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
spec:
  {{- if not $root.Values.autoscaling.enabled }}
  replicas: {{ $service.replicaCount | default 1 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "karmsakha.selectorLabels" $root | nindent 6 }}
      app.kubernetes.io/component: {{ $serviceName }}
  template:
    metadata:
      {{- include "karmsakha.monitoringAnnotations" $root | nindent 6 }}
      labels:
        {{- include "karmsakha.selectorLabels" $root | nindent 8 }}
        app.kubernetes.io/component: {{ $serviceName }}
    spec:
      {{- with $root.Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "karmsakha.serviceAccountName" $root }}
      securityContext:
        {{- toYaml $root.Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ $serviceName }}
          {{- include "karmsakha.securityContext" $root | nindent 10 }}
          image: {{ include "karmsakha.serviceImage" (dict "service" $service "Values" $root.Values "Chart" $root.Chart) }}
          imagePullPolicy: {{ $service.image.pullPolicy | default $root.Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ $service.service.targetPort }}
              protocol: TCP
            {{- if $root.Values.monitoring.enabled }}
            - name: metrics
              containerPort: 9090
              protocol: TCP
            {{- end }}
          {{- include "karmsakha.healthProbes" $root | nindent 10 }}
          env:
            {{- include "karmsakha.commonEnv" $root | nindent 12 }}
            {{- range $key, $value := $service.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- range $secret := $service.secrets }}
            - name: {{ $secret.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ include "karmsakha.fullname" $root }}-app-secrets
                  key: {{ $secret.key }}
            {{- end }}
          envFrom:
            - configMapRef:
                name: {{ include "karmsakha.fullname" $root }}-{{ $serviceName }}-config
            - configMapRef:
                name: {{ include "karmsakha.fullname" $root }}-app-config
          {{- include "karmsakha.volumeMounts" $root | nindent 10 }}
          {{- include "karmsakha.resources" $service | nindent 10 }}
      {{- include "karmsakha.volumes" $root | nindent 6 }}
      {{- with $root.Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- include "karmsakha.nodeAffinity" (dict "Values" $root.Values "component" $serviceName) | nindent 6 }}
      affinity:
        {{- include "karmsakha.podAntiAffinity" (dict "component" $serviceName) | nindent 8 }}
      {{- with $root.Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
{{- end }}