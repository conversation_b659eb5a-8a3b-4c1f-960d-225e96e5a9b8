{{/*
Expand the name of the chart.
*/}}
{{- define "karmsakha.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
*/}}
{{- define "karmsakha.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "karmsakha.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "karmsakha.labels" -}}
helm.sh/chart: {{ include "karmsakha.chart" . }}
{{ include "karmsakha.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "karmsakha.selectorLabels" -}}
app.kubernetes.io/name: {{ include "karmsakha.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Service labels for a specific component
*/}}
{{- define "karmsakha.serviceLabels" -}}
{{ include "karmsakha.labels" . }}
app.kubernetes.io/component: {{ .component }}
{{- end }}

{{/*
Service selector labels for a specific component
*/}}
{{- define "karmsakha.serviceSelectorLabels" -}}
{{ include "karmsakha.selectorLabels" . }}
app.kubernetes.io/component: {{ .component }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "karmsakha.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "karmsakha.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create image name
*/}}
{{- define "karmsakha.image" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry -}}
{{- $repository := .Values.image.repository -}}
{{- $tag := .Values.image.tag | default .Chart.AppVersion -}}
{{- if $registry -}}
{{- printf "%s/%s:%s" $registry $repository $tag -}}
{{- else -}}
{{- printf "%s:%s" $repository $tag -}}
{{- end -}}
{{- end }}

{{/*
Create service image name for a specific service
*/}}
{{- define "karmsakha.serviceImage" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry -}}
{{- $repository := .service.image.repository -}}
{{- $tag := .service.image.tag | default .Chart.AppVersion -}}
{{- if $registry -}}
{{- printf "%s/%s:%s" $registry $repository $tag -}}
{{- else -}}
{{- printf "%s:%s" $repository $tag -}}
{{- end -}}
{{- end }}

{{/*
Generate database URL
*/}}
{{- define "karmsakha.databaseUrl" -}}
{{- if .Values.postgresql.enabled -}}
postgresql://{{ .Values.postgresql.auth.username }}:{{ .Values.postgresql.auth.password }}@{{ include "karmsakha.fullname" . }}-postgresql:5432/{{ .Values.postgresql.auth.database }}
{{- else -}}
{{- .Values.externalDatabase.url -}}
{{- end -}}
{{- end }}

{{/*
Generate redis URL
*/}}
{{- define "karmsakha.redisUrl" -}}
{{- if .Values.redis.enabled -}}
redis://{{ include "karmsakha.fullname" . }}-redis-master:6379
{{- else -}}
{{- .Values.externalRedis.url -}}
{{- end -}}
{{- end }}

{{/*
Common environment variables
*/}}
{{- define "karmsakha.commonEnv" -}}
- name: DATABASE_URL
  valueFrom:
    secretKeyRef:
      name: {{ include "karmsakha.fullname" . }}-db-secret
      key: database-url
- name: REDIS_URL
  valueFrom:
    secretKeyRef:
      name: {{ include "karmsakha.fullname" . }}-redis-secret
      key: redis-url
- name: NODE_ENV
  value: "production"
- name: LOG_LEVEL
  value: {{ .Values.logLevel | default "info" | quote }}
- name: METRICS_PORT
  value: "9090"
{{- end }}

{{/*
Security context for containers
*/}}
{{- define "karmsakha.securityContext" -}}
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 2000
{{- end }}

{{/*
Resource limits and requests
*/}}
{{- define "karmsakha.resources" -}}
{{- if .resources }}
resources:
  {{- if .resources.limits }}
  limits:
    {{- if .resources.limits.cpu }}
    cpu: {{ .resources.limits.cpu }}
    {{- end }}
    {{- if .resources.limits.memory }}
    memory: {{ .resources.limits.memory }}
    {{- end }}
  {{- end }}
  {{- if .resources.requests }}
  requests:
    {{- if .resources.requests.cpu }}
    cpu: {{ .resources.requests.cpu }}
    {{- end }}
    {{- if .resources.requests.memory }}
    memory: {{ .resources.requests.memory }}
    {{- end }}
  {{- end }}
{{- end }}
{{- end }}

{{/*
Pod affinity rules for high availability
*/}}
{{- define "karmsakha.podAntiAffinity" -}}
podAntiAffinity:
  preferredDuringSchedulingIgnoredDuringExecution:
  - weight: 100
    podAffinityTerm:
      labelSelector:
        matchExpressions:
        - key: app.kubernetes.io/name
          operator: In
          values:
          - {{ include "karmsakha.name" . }}
        - key: app.kubernetes.io/component
          operator: In
          values:
          - {{ .component }}
      topologyKey: kubernetes.io/hostname
{{- end }}

{{/*
Node affinity for spot instances
*/}}
{{- define "karmsakha.nodeAffinity" -}}
{{- if .Values.costOptimization.nodeAffinity.preferSpotInstances }}
nodeAffinity:
  preferredDuringSchedulingIgnoredDuringExecution:
  - weight: 50
    preference:
      matchExpressions:
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - spot
  - weight: 30
    preference:
      matchExpressions:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - spot
{{- end }}
{{- end }}

{{/*
Health check probes
*/}}
{{- define "karmsakha.healthProbes" -}}
livenessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
readinessProbe:
  httpGet:
    path: /ready
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
{{- end }}

{{/*
Volume mounts for temporary files
*/}}
{{- define "karmsakha.volumeMounts" -}}
volumeMounts:
- name: tmp
  mountPath: /tmp
- name: var-cache
  mountPath: /var/cache
- name: var-log
  mountPath: /var/log
{{- end }}

{{/*
Volumes for temporary files
*/}}
{{- define "karmsakha.volumes" -}}
volumes:
- name: tmp
  emptyDir: {}
- name: var-cache
  emptyDir: {}
- name: var-log
  emptyDir: {}
{{- end }}

{{/*
Monitoring annotations
*/}}
{{- define "karmsakha.monitoringAnnotations" -}}
{{- if .Values.monitoring.enabled }}
annotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "9090"
  prometheus.io/path: "/metrics"
{{- end }}
{{- end }}

{{/*
Network policy selector
*/}}
{{- define "karmsakha.networkPolicySelector" -}}
matchLabels:
  {{ include "karmsakha.selectorLabels" . | nindent 4 }}
  {{- if .component }}
  app.kubernetes.io/component: {{ .component }}
  {{- end }}
{{- end }}