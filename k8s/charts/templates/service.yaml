{{- $root := . -}}
{{- range $serviceName, $service := .Values.services }}
{{- if $service.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "karmsakha.fullname" $root }}-{{ $serviceName }}
  labels:
    {{- include "karmsakha.labels" $root | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
  {{- include "karmsakha.monitoringAnnotations" $root | nindent 2 }}
spec:
  type: {{ $service.service.type | default "ClusterIP" }}
  ports:
    - port: {{ $service.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    {{- if $root.Values.monitoring.enabled }}
    - port: 9090
      targetPort: metrics
      protocol: TCP
      name: metrics
    {{- end }}
  selector:
    {{- include "karmsakha.selectorLabels" $root | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
{{- end }}
{{- end }}

{{- if .Values.service.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "karmsakha.fullname" . }}
  labels:
    {{- include "karmsakha.labels" . | nindent 4 }}
  {{- include "karmsakha.monitoringAnnotations" . | nindent 2 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "karmsakha.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: gateway
{{- end }}