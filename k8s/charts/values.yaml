# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""
  postgresql:
    auth:
      existingSecret: "karmsakha-db-secret"
  redis:
    auth:
      existingSecret: "karmsakha-redis-secret"

# Image configuration
image:
  registry: public.ecr.aws
  repository: karmsakha
  tag: "1.0.0"
  pullPolicy: IfNotPresent

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod security context
podSecurityContext:
  fsGroup: 2000
  runAsNonRoot: true
  runAsUser: 1000

# Security context
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

# Resource limits
resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

# Autoscaling
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Node selector and tolerations
nodeSelector: {}
tolerations: []
affinity: {}

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Network policies
networkPolicy:
  enabled: true
  ingress: []
  egress: []

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: api.karmsakha.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: karmsakha-tls
      hosts:
        - api.karmsakha.com

# Service configuration
service:
  type: ClusterIP
  port: 80
  targetPort: http

# Microservices configuration
services:
  # Job Ingestor Service
  ingestor:
    enabled: true
    replicaCount: 3
    image:
      repository: karmsakha/ingestor
      tag: "1.0.0"
    service:
      port: 3001
      targetPort: 3001
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 200m
        memory: 512Mi
    env:
      NODE_ENV: production
      LOG_LEVEL: info
    secrets:
      - name: BRIGHT_DATA_PROXY_URL
        key: bright-data-proxy-url
      - name: OPENAI_API_KEY
        key: openai-api-key
    configMap:
      INGESTION_SCHEDULE: "0 */4 * * *"  # Every 4 hours
      MAX_CONCURRENT_JOBS: "5"
      RETRY_ATTEMPTS: "3"

  # Vector Ranking API
  ranker:
    enabled: true
    replicaCount: 5
    image:
      repository: karmsakha/ranker
      tag: "1.0.0"
    service:
      port: 8000
      targetPort: 8000
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
    env:
      PYTHONPATH: /app
      LOG_LEVEL: INFO
    secrets:
      - name: OPENAI_API_KEY
        key: openai-api-key
    configMap:
      SIMILARITY_THRESHOLD: "0.85"
      MAX_RESULTS: "50"
      EMBEDDING_MODEL: "text-embedding-3-small"

  # Stagehand Worker
  worker:
    enabled: true
    replicaCount: 2
    image:
      repository: karmsakha/worker
      tag: "1.0.0"
    service:
      port: 3003
      targetPort: 3003
    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi
    env:
      NODE_ENV: production
      LOG_LEVEL: info
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: "true"
    secrets:
      - name: TWILIO_ACCOUNT_SID
        key: twilio-account-sid
      - name: TWILIO_AUTH_TOKEN
        key: twilio-auth-token
    configMap:
      MAX_APPLICATIONS_PER_HOUR: "100"
      HEADLESS_MODE: "true"
      TIMEOUT_MS: "30000"

  # GraphQL Gateway
  gateway:
    enabled: true
    replicaCount: 3
    image:
      repository: karmsakha/gateway
      tag: "1.0.0"
    service:
      port: 4000
      targetPort: 4000
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 200m
        memory: 512Mi
    env:
      NODE_ENV: production
      LOG_LEVEL: info
    secrets:
      - name: JWT_SECRET
        key: jwt-secret
    configMap:
      CORS_ORIGIN: "https://app.karmsakha.com"
      RATE_LIMIT_WINDOW_MS: "900000"  # 15 minutes
      RATE_LIMIT_MAX: "100"

  # React Frontend
  frontend:
    enabled: true
    replicaCount: 2
    image:
      repository: karmsakha/frontend
      tag: "1.0.0"
    service:
      port: 80
      targetPort: 80
    resources:
      limits:
        cpu: 200m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
    configMap:
      REACT_APP_API_URL: "https://api.karmsakha.com/graphql"
      REACT_APP_SENTRY_DSN: ""

  # Audit Service
  audit:
    enabled: true
    replicaCount: 1
    image:
      repository: karmsakha/audit
      tag: "1.0.0"
    service:
      port: 3005
      targetPort: 3005
    resources:
      limits:
        cpu: 300m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
    env:
      NODE_ENV: production
      LOG_LEVEL: info
    secrets:
      - name: AWS_ACCESS_KEY_ID
        key: aws-access-key-id
      - name: AWS_SECRET_ACCESS_KEY
        key: aws-secret-access-key
    configMap:
      AWS_REGION: "ap-south-1"
      S3_BUCKET: "karmsakha-audit-logs"
      RETENTION_DAYS: "90"

# External dependencies
postgresql:
  enabled: false  # Using external RDS
  auth:
    existingSecret: "karmsakha-db-secret"

redis:
  enabled: false  # Using external ElastiCache
  auth:
    existingSecret: "karmsakha-redis-secret"

# Monitoring and observability
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: monitoring
    interval: 30s
    scrapeTimeout: 10s
  grafana:
    dashboards:
      enabled: true
  prometheus:
    rules:
      enabled: true

# Logging
logging:
  enabled: true
  fluentd:
    enabled: true
    elasticsearch:
      host: "elasticsearch.logging.svc.cluster.local"
      port: 9200

# Security scanning
securityPolicy:
  enabled: true
  podSecurityPolicy:
    enabled: true
  networkPolicy:
    enabled: true

# Backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: "30d"

# External secrets
externalSecrets:
  enabled: true
  backendType: secretsManager
  region: ap-south-1
  secrets:
    - name: karmsakha-app-secrets
      keys:
        - openai-api-key
        - jwt-secret
        - bright-data-proxy-url
        - twilio-account-sid
        - twilio-auth-token
        - aws-access-key-id
        - aws-secret-access-key

# ConfigMaps
configMaps:
  app-config:
    data:
      LOG_LEVEL: "info"
      NODE_ENV: "production"
      METRICS_ENABLED: "true"
      HEALTH_CHECK_INTERVAL: "30000"

# Cost optimization
costOptimization:
  enabled: true
  verticalPodAutoscaler:
    enabled: true
    updateMode: "Auto"
  nodeAffinity:
    preferSpotInstances: true