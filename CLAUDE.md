# Claude Code Conventions

## Global Project Standards

### Commit Format
- `feat: description` - New features
- `fix: description` - Bug fixes  
- `docs: description` - Documentation
- `infra: description` - Infrastructure changes
- `test: description` - Test additions/fixes

### Tech Stack Decisions
- **Backend**: Node.js 20 + TypeScript, Python 3.12 + FastAPI
- **Database**: PostgreSQL 15 + pgvector extension
- **Queue**: BullMQ + Redis
- **Frontend**: React 18 + TypeScript + Tailwind CSS
- **Infrastructure**: Terraform + Kubernetes (EKS)
- **CI/CD**: GitHub Actions + ArgoCD

### Testing Commands
- `npm test` - Unit tests for Node.js services
- `pytest` - Python service tests
- `npm run e2e` - End-to-end tests
- `terraform plan` - Infrastructure validation
- `helm template` - Kubernetes manifest validation

### Code Quality
- `npm run lint` - ESLint + Prettier
- `npm run type-check` - TypeScript validation
- `ruff check` - Python linting
- `mypy` - Python type checking

### Development Workflow
1. Write failing tests first (TDD)
2. Implement minimal code to pass tests
3. Refactor with confidence
4. Run full test suite before commit
5. Use conventional commits

### Security Guidelines  
- No secrets in code - use environment variables
- Rate limit all public APIs  
- Encrypt PII data at rest
- Audit all user data access
- DPDP Act 2023 compliance for Indian users