# Core FastAPI and async dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and vector operations
asyncpg==0.29.0
psycopg2-binary==2.9.9
pgvector==0.2.4
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1

# Redis for caching and session management
redis[hiredis]==5.0.1
aioredis==2.0.1

# OpenAI for embeddings
openai==1.6.1
tiktoken==0.5.2

# HTTP client and utilities
httpx==0.25.2
aiohttp==3.9.1

# Data processing and scientific computing
numpy==1.24.4
pandas==2.1.4
scikit-learn==1.3.2

# Monitoring and observability
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.39.2

# Security and authentication
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# Configuration and environment
python-dotenv==1.0.0
typer==0.9.0

# Testing and development
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Performance and optimization
orjson==3.9.10
msgpack==1.0.7

# Validation and serialization
marshmallow==3.20.2
email-validator==2.1.0

# Background tasks and scheduling
celery[redis]==5.3.4
flower==2.0.1

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0