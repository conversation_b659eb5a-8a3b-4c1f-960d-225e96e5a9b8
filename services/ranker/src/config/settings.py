"""
Configuration settings for the Vector Ranking API
"""

import os
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Application settings
    app_name: str = "karmsakha-vector-ranking-api"
    version: str = "1.0.0"
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    port: int = Field(default=8000, env="PORT")
    host: str = Field(default="0.0.0.0", env="HOST")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Database settings
    database_url: str = Field(..., env="DATABASE_URL")
    database_pool_size: int = Field(default=20, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    database_pool_timeout: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    
    # Redis settings
    redis_url: str = Field(..., env="REDIS_URL")
    redis_pool_size: int = Field(default=20, env="REDIS_POOL_SIZE")
    redis_timeout: int = Field(default=5, env="REDIS_TIMEOUT")
    
    # OpenAI settings
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="text-embedding-3-small", env="OPENAI_MODEL")
    openai_timeout: int = Field(default=30, env="OPENAI_TIMEOUT")
    openai_max_retries: int = Field(default=3, env="OPENAI_MAX_RETRIES")
    
    # Vector search settings
    similarity_threshold: float = Field(default=0.85, env="SIMILARITY_THRESHOLD")
    max_results: int = Field(default=50, env="MAX_RESULTS")
    embedding_dimensions: int = Field(default=1536, env="EMBEDDING_DIMENSIONS")
    
    # Ranking algorithm settings
    skill_match_weight: float = Field(default=0.4, env="SKILL_MATCH_WEIGHT")
    experience_weight: float = Field(default=0.25, env="EXPERIENCE_WEIGHT")
    location_weight: float = Field(default=0.2, env="LOCATION_WEIGHT")
    salary_weight: float = Field(default=0.15, env="SALARY_WEIGHT")
    
    # Caching settings
    cache_ttl_seconds: int = Field(default=3600, env="CACHE_TTL_SECONDS")  # 1 hour
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    
    # Rate limiting
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=3600, env="RATE_LIMIT_WINDOW")  # 1 hour
    
    # Authentication
    auth_enabled: bool = Field(default=True, env="AUTH_ENABLED")
    jwt_secret: str = Field(default="dev-secret", env="JWT_SECRET")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    jwt_expire_minutes: int = Field(default=30, env="JWT_EXPIRE_MINUTES")
    
    # CORS settings
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "https://app.karmsakha.com"],
        env="CORS_ORIGINS"
    )
    
    # Monitoring
    sentry_dsn: Optional[str] = Field(default=None, env="SENTRY_DSN")
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    
    # Performance tuning
    worker_connections: int = Field(default=1000, env="WORKER_CONNECTIONS")
    max_concurrent_requests: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    
    # Background tasks
    background_tasks_enabled: bool = Field(default=True, env="BACKGROUND_TASKS_ENABLED")
    periodic_cleanup_interval: int = Field(default=3600, env="PERIODIC_CLEANUP_INTERVAL")  # 1 hour
    
    # Feature flags
    experimental_features: bool = Field(default=False, env="EXPERIMENTAL_FEATURES")
    ml_ranking_enabled: bool = Field(default=False, env="ML_RANKING_ENABLED")
    real_time_updates: bool = Field(default=True, env="REAL_TIME_UPDATES")
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("similarity_threshold")
    def validate_similarity_threshold(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")
        return v
    
    @validator("max_results")
    def validate_max_results(cls, v):
        if v <= 0 or v > 1000:
            raise ValueError("Max results must be between 1 and 1000")
        return v
    
    @validator("environment")
    def validate_environment(cls, v):
        if v not in ["development", "staging", "production"]:
            raise ValueError("Environment must be development, staging, or production")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        if v.upper() not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError("Invalid log level")
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment == "development"
    
    def get_database_config(self) -> dict:
        """Get database configuration"""
        return {
            "url": self.database_url,
            "pool_size": self.database_pool_size,
            "max_overflow": self.database_max_overflow,
            "pool_timeout": self.database_pool_timeout,
        }
    
    def get_redis_config(self) -> dict:
        """Get Redis configuration"""
        return {
            "url": self.redis_url,
            "pool_size": self.redis_pool_size,
            "timeout": self.redis_timeout,
        }
    
    def get_openai_config(self) -> dict:
        """Get OpenAI configuration"""
        return {
            "api_key": self.openai_api_key,
            "model": self.openai_model,
            "timeout": self.openai_timeout,
            "max_retries": self.openai_max_retries,
        }
    
    def get_ranking_weights(self) -> dict:
        """Get ranking algorithm weights"""
        return {
            "skill_match": self.skill_match_weight,
            "experience": self.experience_weight,
            "location": self.location_weight,
            "salary": self.salary_weight,
        }


# Create global settings instance
settings = Settings()

# Validate settings on import
if __name__ == "__main__":
    print("Settings validation passed!")
    print(f"Environment: {settings.environment}")
    print(f"Database URL: {settings.database_url[:20]}...")
    print(f"Redis URL: {settings.redis_url[:20]}...")
    print(f"OpenAI Model: {settings.openai_model}")
    print(f"Similarity Threshold: {settings.similarity_threshold}")
    print(f"Max Results: {settings.max_results}")