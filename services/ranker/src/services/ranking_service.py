"""
Core ranking service for AI-powered job matching
"""

import asyncio
import hashlib
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import structlog
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from ..config.settings import settings
from ..database.connection import JobRepository, UserRepository, ApplicationRepository
from ..services.cache_service import CacheService
from ..services.embedding_service import EmbeddingService

logger = structlog.get_logger()


class RankingService:
    """AI-powered job ranking service using vector similarity and weighted scoring"""
    
    def __init__(self):
        self.cache_service = CacheService()
        self.embedding_service = EmbeddingService()
        self.initialized = False
        
        # Ranking weights from settings
        self.weights = settings.get_ranking_weights()
        
        # Performance tracking
        self.ranking_stats = {
            "total_rankings": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "average_response_time": 0,
        }
    
    async def initialize(self):
        """Initialize ranking service"""
        try:
            await self.cache_service.initialize()
            await self.embedding_service.initialize()
            self.initialized = True
            logger.info("Ranking service initialized successfully")
        except Exception as e:
            logger.error("Failed to initialize ranking service", error=str(e))
            raise
    
    async def rank_jobs_for_user(
        self,
        user_id: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Rank jobs for a specific user based on their profile and preferences
        
        Args:
            user_id: User identifier
            filters: Additional filtering criteria
            limit: Maximum number of jobs to return
            use_cache: Whether to use cached results
            
        Returns:
            Dictionary containing ranked jobs and metadata
        """
        start_time = datetime.now()
        
        try:
            # Get limit from settings if not provided
            if limit is None:
                limit = settings.max_results
            
            # Generate cache key
            cache_key = self._generate_cache_key(user_id, filters, limit) if use_cache else None
            
            # Check cache first
            if cache_key and use_cache:
                cached_result = await self.cache_service.get(cache_key)
                if cached_result:
                    self.ranking_stats["cache_hits"] += 1
                    logger.info("Returning cached ranking results", user_id=user_id)
                    return cached_result
            
            self.ranking_stats["cache_misses"] += 1
            
            # Get user profile and embedding
            user = await UserRepository.get_user_by_id(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")
            
            if not user.resume_embedding:
                # Generate embedding if not exists
                if user.resume_text:
                    embedding = await self.embedding_service.generate_embedding(
                        self._prepare_user_text_for_embedding(user)
                    )
                    await UserRepository.update_user_embedding(user_id, embedding)
                    user.resume_embedding = embedding
                else:
                    raise ValueError(f"User {user_id} has no resume text or embedding")
            
            # Get user preferences and apply to filters
            enhanced_filters = await self._enhance_filters_with_user_preferences(user, filters)
            
            # Get applied jobs to exclude them
            if enhanced_filters.get('exclude_applied_jobs', True):
                applied_jobs = await ApplicationRepository.get_user_applications(user_id)
                enhanced_filters['exclude_applied_jobs'] = applied_jobs
            
            # Find similar jobs using vector search
            similar_jobs = await JobRepository.find_similar_jobs(
                query_embedding=list(user.resume_embedding),
                threshold=settings.similarity_threshold,
                limit=limit * 2,  # Get more to allow for additional filtering
                filters=enhanced_filters
            )
            
            # Apply advanced ranking algorithm
            ranked_jobs = await self._apply_advanced_ranking(user, similar_jobs)
            
            # Limit results
            final_jobs = ranked_jobs[:limit]
            
            # Prepare response
            result = {
                "user_id": user_id,
                "total_jobs": len(final_jobs),
                "jobs": final_jobs,
                "filters_applied": enhanced_filters,
                "ranking_metadata": {
                    "similarity_threshold": settings.similarity_threshold,
                    "weights": self.weights,
                    "timestamp": datetime.now().isoformat(),
                    "processing_time_ms": (datetime.now() - start_time).total_seconds() * 1000,
                },
            }
            
            # Cache the result
            if cache_key and use_cache:
                await self.cache_service.set(
                    cache_key, 
                    result, 
                    ttl=settings.cache_ttl_seconds
                )
            
            # Update stats
            self.ranking_stats["total_rankings"] += 1
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self._update_average_response_time(processing_time)
            
            logger.info(
                "Job ranking completed",
                user_id=user_id,
                jobs_returned=len(final_jobs),
                processing_time_ms=processing_time
            )
            
            return result
            
        except Exception as e:
            logger.error("Error ranking jobs for user", user_id=user_id, error=str(e))
            raise
    
    async def rank_jobs_by_query(
        self,
        query_text: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Rank jobs based on a text query
        
        Args:
            query_text: Text query describing desired job
            filters: Additional filtering criteria
            limit: Maximum number of jobs to return
            use_cache: Whether to use cached results
            
        Returns:
            Dictionary containing ranked jobs and metadata
        """
        start_time = datetime.now()
        
        try:
            if limit is None:
                limit = settings.max_results
            
            # Generate cache key
            cache_key = self._generate_cache_key(query_text, filters, limit) if use_cache else None
            
            # Check cache
            if cache_key and use_cache:
                cached_result = await self.cache_service.get(cache_key)
                if cached_result:
                    self.ranking_stats["cache_hits"] += 1
                    return cached_result
            
            self.ranking_stats["cache_misses"] += 1
            
            # Generate embedding for query
            query_embedding = await self.embedding_service.generate_embedding(query_text)
            
            # Find similar jobs
            similar_jobs = await JobRepository.find_similar_jobs(
                query_embedding=query_embedding,
                threshold=settings.similarity_threshold,
                limit=limit,
                filters=filters
            )
            
            # Simple ranking by similarity for text queries
            ranked_jobs = sorted(similar_jobs, key=lambda x: x['similarity'], reverse=True)
            
            result = {
                "query": query_text,
                "total_jobs": len(ranked_jobs),
                "jobs": ranked_jobs,
                "filters_applied": filters or {},
                "ranking_metadata": {
                    "similarity_threshold": settings.similarity_threshold,
                    "timestamp": datetime.now().isoformat(),
                    "processing_time_ms": (datetime.now() - start_time).total_seconds() * 1000,
                },
            }
            
            # Cache the result
            if cache_key and use_cache:
                await self.cache_service.set(
                    cache_key, 
                    result, 
                    ttl=settings.cache_ttl_seconds
                )
            
            self.ranking_stats["total_rankings"] += 1
            
            return result
            
        except Exception as e:
            logger.error("Error ranking jobs by query", query=query_text, error=str(e))
            raise
    
    async def _apply_advanced_ranking(
        self,
        user: Any,
        similar_jobs: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Apply advanced ranking algorithm combining multiple factors
        """
        ranked_jobs = []
        
        for job in similar_jobs:
            # Base similarity score
            similarity_score = job['similarity']
            
            # Skills match score
            skills_score = self._calculate_skills_match(user.skills or [], job.get('skills', []))
            
            # Experience level match score
            experience_score = self._calculate_experience_match(
                user.experience_years,
                job.get('experience_level')
            )
            
            # Location preference score
            location_score = self._calculate_location_preference(
                user.preferred_locations or [],
                user.remote_preference,
                job.get('location'),
                job.get('is_remote', False)
            )
            
            # Salary match score
            salary_score = self._calculate_salary_match(
                user.preferred_salary_min,
                user.preferred_salary_max,
                job.get('salary_min'),
                job.get('salary_max')
            )
            
            # Calculate weighted final score
            final_score = (
                similarity_score * self.weights['skill_match'] +
                skills_score * self.weights['skill_match'] * 0.5 +  # Additional skills boost
                experience_score * self.weights['experience'] +
                location_score * self.weights['location'] +
                salary_score * self.weights['salary']
            )
            
            # Add ranking metadata to job
            job_with_ranking = {
                **job,
                'ranking_score': final_score,
                'ranking_breakdown': {
                    'similarity_score': similarity_score,
                    'skills_score': skills_score,
                    'experience_score': experience_score,
                    'location_score': location_score,
                    'salary_score': salary_score,
                }
            }
            
            ranked_jobs.append(job_with_ranking)
        
        # Sort by final ranking score
        return sorted(ranked_jobs, key=lambda x: x['ranking_score'], reverse=True)
    
    def _calculate_skills_match(self, user_skills: List[str], job_skills: List[str]) -> float:
        """Calculate skills match score (0.0 to 1.0)"""
        if not user_skills or not job_skills:
            return 0.0
        
        user_skills_lower = [skill.lower() for skill in user_skills]
        job_skills_lower = [skill.lower() for skill in job_skills]
        
        # Calculate Jaccard similarity
        intersection = len(set(user_skills_lower) & set(job_skills_lower))
        union = len(set(user_skills_lower) | set(job_skills_lower))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_experience_match(self, user_experience: Optional[int], job_experience_level: Optional[str]) -> float:
        """Calculate experience level match score (0.0 to 1.0)"""
        if not user_experience or not job_experience_level:
            return 0.5  # Neutral score if data is missing
        
        # Define experience level ranges
        experience_ranges = {
            'entry': (0, 2),
            'mid': (2, 7),
            'senior': (7, 15),
            'executive': (15, 50),
        }
        
        if job_experience_level.lower() not in experience_ranges:
            return 0.5
        
        min_exp, max_exp = experience_ranges[job_experience_level.lower()]
        
        # Perfect match if user experience is in range
        if min_exp <= user_experience <= max_exp:
            return 1.0
        
        # Partial match based on distance from range
        if user_experience < min_exp:
            distance = min_exp - user_experience
            return max(0.0, 1.0 - (distance / 5.0))  # Penalize by 0.2 per year below
        else:
            distance = user_experience - max_exp
            return max(0.0, 1.0 - (distance / 10.0))  # Penalize by 0.1 per year above
    
    def _calculate_location_preference(
        self,
        user_preferred_locations: List[str],
        user_remote_preference: bool,
        job_location: Optional[str],
        job_is_remote: bool
    ) -> float:
        """Calculate location preference match score (0.0 to 1.0)"""
        # Perfect match for remote jobs if user prefers remote
        if job_is_remote and user_remote_preference:
            return 1.0
        
        # No location preferences means neutral score
        if not user_preferred_locations and not user_remote_preference:
            return 0.5
        
        # Check if job location matches user preferences
        if job_location and user_preferred_locations:
            job_location_lower = job_location.lower()
            for preferred_location in user_preferred_locations:
                if preferred_location.lower() in job_location_lower:
                    return 1.0
        
        # Lower score if no match but user has preferences
        return 0.2 if user_preferred_locations else 0.5
    
    def _calculate_salary_match(
        self,
        user_min_salary: Optional[int],
        user_max_salary: Optional[int],
        job_min_salary: Optional[int],
        job_max_salary: Optional[int]
    ) -> float:
        """Calculate salary match score (0.0 to 1.0)"""
        if not any([user_min_salary, user_max_salary, job_min_salary, job_max_salary]):
            return 0.5  # Neutral if no salary data
        
        # Use middle values for comparison
        user_salary = None
        if user_min_salary and user_max_salary:
            user_salary = (user_min_salary + user_max_salary) / 2
        elif user_min_salary:
            user_salary = user_min_salary
        elif user_max_salary:
            user_salary = user_max_salary
        
        job_salary = None
        if job_min_salary and job_max_salary:
            job_salary = (job_min_salary + job_max_salary) / 2
        elif job_min_salary:
            job_salary = job_min_salary
        elif job_max_salary:
            job_salary = job_max_salary
        
        if not user_salary or not job_salary:
            return 0.5
        
        # Calculate salary match based on relative difference
        ratio = min(user_salary, job_salary) / max(user_salary, job_salary)
        return ratio  # Closer salaries get higher scores
    
    async def _enhance_filters_with_user_preferences(
        self,
        user: Any,
        base_filters: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Enhance filters with user preferences"""
        filters = base_filters or {}
        
        # Add user country preference if not specified
        if not filters.get('country') and user.country:
            filters['country'] = user.country
        
        # Add remote preference
        if user.remote_preference and not filters.get('is_remote'):
            filters['is_remote'] = True
        
        # Add experience level if user has sufficient experience
        if not filters.get('experience_level') and user.experience_years:
            if user.experience_years <= 2:
                filters['experience_level'] = 'entry'
            elif user.experience_years <= 7:
                filters['experience_level'] = 'mid'
            else:
                filters['experience_level'] = 'senior'
        
        return filters
    
    def _prepare_user_text_for_embedding(self, user: Any) -> str:
        """Prepare user text for embedding generation"""
        text_parts = []
        
        if user.resume_text:
            text_parts.append(user.resume_text)
        
        if user.current_title:
            text_parts.append(f"Current title: {user.current_title}")
        
        if user.skills:
            text_parts.append(f"Skills: {', '.join(user.skills)}")
        
        if user.experience_years:
            text_parts.append(f"Experience: {user.experience_years} years")
        
        return " ".join(text_parts)
    
    def _generate_cache_key(self, *args) -> str:
        """Generate cache key from arguments"""
        key_data = json.dumps(args, sort_keys=True, default=str)
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _update_average_response_time(self, response_time: float):
        """Update running average response time"""
        count = self.ranking_stats["total_rankings"]
        if count == 1:
            self.ranking_stats["average_response_time"] = response_time
        else:
            current_avg = self.ranking_stats["average_response_time"]
            self.ranking_stats["average_response_time"] = (
                (current_avg * (count - 1) + response_time) / count
            )
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """Get ranking service statistics"""
        return {
            "ranking_stats": self.ranking_stats,
            "cache_stats": await self.cache_service.get_stats(),
            "weights": self.weights,
            "settings": {
                "similarity_threshold": settings.similarity_threshold,
                "max_results": settings.max_results,
                "cache_ttl": settings.cache_ttl_seconds,
            },
        }
    
    async def clear_cache(self) -> bool:
        """Clear ranking cache"""
        try:
            await self.cache_service.clear_all()
            logger.info("Ranking cache cleared")
            return True
        except Exception as e:
            logger.error("Failed to clear ranking cache", error=str(e))
            return False