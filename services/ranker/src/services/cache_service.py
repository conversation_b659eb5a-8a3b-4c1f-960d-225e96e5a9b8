"""
Cache service for storing ranking results and improving performance
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List
import structlog
import redis.asyncio as aioredis
from redis.exceptions import ConnectionError, TimeoutError

from ..config.settings import settings

logger = structlog.get_logger()


class CacheService:
    """Redis-based caching service for ranking results"""
    
    def __init__(self):
        self.redis_client = None
        self.initialized = False
        
        # Cache key prefixes
        self.RANKING_PREFIX = "ranking:"
        self.USER_PROFILE_PREFIX = "user_profile:"
        self.JOB_EMBEDDING_PREFIX = "job_embedding:"
        self.STATS_PREFIX = "stats:"
        
        # Performance tracking
        self.stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "cache_sets": 0,
            "cache_deletes": 0,
            "total_requests": 0,
            "hit_rate": 0.0,
        }
    
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            # Create Redis connection pool
            self.redis_client = aioredis.from_url(
                settings.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=settings.redis_timeout,
                socket_connect_timeout=settings.redis_timeout,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            
            # Test the connection
            await self._test_connection()
            
            self.initialized = True
            logger.info("Cache service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize cache service", error=str(e))
            raise
    
    async def get(self, key: str, prefix: str = None) -> Optional[Any]:
        """
        Get value from cache
        
        Args:
            key: Cache key
            prefix: Optional prefix for the key
            
        Returns:
            Cached value or None if not found
        """
        if not self.initialized:
            return None
        
        try:
            full_key = self._build_key(key, prefix or self.RANKING_PREFIX)
            value = await self.redis_client.get(full_key)
            
            self.stats["total_requests"] += 1
            
            if value is None:
                self.stats["cache_misses"] += 1
                self._update_hit_rate()
                return None
            
            self.stats["cache_hits"] += 1
            self._update_hit_rate()
            
            # Deserialize JSON
            return json.loads(value)
            
        except (ConnectionError, TimeoutError) as e:
            logger.warning("Cache connection error", error=str(e))
            return None
        except json.JSONDecodeError as e:
            logger.warning("Cache value deserialization failed", key=key, error=str(e))
            # Remove corrupted cache entry
            await self.delete(key, prefix)
            return None
        except Exception as e:
            logger.error("Unexpected cache get error", key=key, error=str(e))
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        prefix: str = None
    ) -> bool:
        """
        Set value in cache
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            prefix: Optional prefix for the key
            
        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            return False
        
        try:
            full_key = self._build_key(key, prefix or self.RANKING_PREFIX)
            serialized_value = json.dumps(value, default=str)
            
            if ttl:
                await self.redis_client.setex(full_key, ttl, serialized_value)
            else:
                await self.redis_client.set(full_key, serialized_value)
            
            self.stats["cache_sets"] += 1
            return True
            
        except (ConnectionError, TimeoutError) as e:
            logger.warning("Cache connection error during set", error=str(e))
            return False
        except Exception as e:
            logger.error("Unexpected cache set error", key=key, error=str(e))
            return False
    
    async def delete(self, key: str, prefix: str = None) -> bool:
        """
        Delete key from cache
        
        Args:
            key: Cache key to delete
            prefix: Optional prefix for the key
            
        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            return False
        
        try:
            full_key = self._build_key(key, prefix or self.RANKING_PREFIX)
            result = await self.redis_client.delete(full_key)
            
            if result > 0:
                self.stats["cache_deletes"] += 1
                return True
            
            return False
            
        except (ConnectionError, TimeoutError) as e:
            logger.warning("Cache connection error during delete", error=str(e))
            return False
        except Exception as e:
            logger.error("Unexpected cache delete error", key=key, error=str(e))
            return False
    
    async def exists(self, key: str, prefix: str = None) -> bool:
        """
        Check if key exists in cache
        
        Args:
            key: Cache key to check
            prefix: Optional prefix for the key
            
        Returns:
            True if key exists, False otherwise
        """
        if not self.initialized:
            return False
        
        try:
            full_key = self._build_key(key, prefix or self.RANKING_PREFIX)
            result = await self.redis_client.exists(full_key)
            return result > 0
            
        except Exception as e:
            logger.error("Error checking cache key existence", key=key, error=str(e))
            return False
    
    async def expire(self, key: str, ttl: int, prefix: str = None) -> bool:
        """
        Set expiration time for a key
        
        Args:
            key: Cache key
            ttl: Time to live in seconds
            prefix: Optional prefix for the key
            
        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            return False
        
        try:
            full_key = self._build_key(key, prefix or self.RANKING_PREFIX)
            result = await self.redis_client.expire(full_key, ttl)
            return result
            
        except Exception as e:
            logger.error("Error setting cache key expiration", key=key, error=str(e))
            return False
    
    async def get_keys(self, pattern: str, prefix: str = None) -> List[str]:
        """
        Get keys matching a pattern
        
        Args:
            pattern: Pattern to match (supports wildcards)
            prefix: Optional prefix for the pattern
            
        Returns:
            List of matching keys
        """
        if not self.initialized:
            return []
        
        try:
            full_pattern = self._build_key(pattern, prefix or self.RANKING_PREFIX)
            keys = await self.redis_client.keys(full_pattern)
            
            # Remove prefix from returned keys
            prefix_len = len(prefix or self.RANKING_PREFIX)
            return [key[prefix_len:] for key in keys]
            
        except Exception as e:
            logger.error("Error getting cache keys", pattern=pattern, error=str(e))
            return []
    
    async def clear_prefix(self, prefix: str) -> int:
        """
        Clear all keys with a specific prefix
        
        Args:
            prefix: Prefix to clear
            
        Returns:
            Number of keys deleted
        """
        if not self.initialized:
            return 0
        
        try:
            pattern = f"{prefix}*"
            keys = await self.redis_client.keys(pattern)
            
            if not keys:
                return 0
            
            deleted = await self.redis_client.delete(*keys)
            self.stats["cache_deletes"] += deleted
            
            logger.info("Cleared cache prefix", prefix=prefix, deleted_count=deleted)
            return deleted
            
        except Exception as e:
            logger.error("Error clearing cache prefix", prefix=prefix, error=str(e))
            return 0
    
    async def clear_all(self) -> bool:
        """
        Clear all cache entries (use with caution)
        
        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            return False
        
        try:
            await self.redis_client.flushdb()
            logger.warning("All cache entries cleared")
            return True
            
        except Exception as e:
            logger.error("Error clearing all cache entries", error=str(e))
            return False
    
    async def get_ttl(self, key: str, prefix: str = None) -> int:
        """
        Get time to live for a key
        
        Args:
            key: Cache key
            prefix: Optional prefix for the key
            
        Returns:
            TTL in seconds, -1 if no expiration, -2 if key doesn't exist
        """
        if not self.initialized:
            return -2
        
        try:
            full_key = self._build_key(key, prefix or self.RANKING_PREFIX)
            ttl = await self.redis_client.ttl(full_key)
            return ttl
            
        except Exception as e:
            logger.error("Error getting cache key TTL", key=key, error=str(e))
            return -2
    
    async def increment(self, key: str, amount: int = 1, prefix: str = None) -> Optional[int]:
        """
        Increment a numeric value in cache
        
        Args:
            key: Cache key
            amount: Amount to increment by
            prefix: Optional prefix for the key
            
        Returns:
            New value after increment, or None if error
        """
        if not self.initialized:
            return None
        
        try:
            full_key = self._build_key(key, prefix or self.STATS_PREFIX)
            result = await self.redis_client.incrby(full_key, amount)
            return result
            
        except Exception as e:
            logger.error("Error incrementing cache value", key=key, error=str(e))
            return None
    
    async def cache_ranking_result(
        self,
        user_id: str,
        filters: Dict[str, Any],
        result: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """
        Cache a ranking result with a structured key
        
        Args:
            user_id: User ID
            filters: Filters applied
            result: Ranking result to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        cache_key = self._generate_ranking_cache_key(user_id, filters)
        return await self.set(
            cache_key,
            result,
            ttl or settings.cache_ttl_seconds,
            self.RANKING_PREFIX
        )
    
    async def get_cached_ranking_result(
        self,
        user_id: str,
        filters: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached ranking result
        
        Args:
            user_id: User ID
            filters: Filters applied
            
        Returns:
            Cached ranking result or None
        """
        cache_key = self._generate_ranking_cache_key(user_id, filters)
        return await self.get(cache_key, self.RANKING_PREFIX)
    
    async def invalidate_user_cache(self, user_id: str) -> int:
        """
        Invalidate all cache entries for a specific user
        
        Args:
            user_id: User ID
            
        Returns:
            Number of keys deleted
        """
        pattern = f"*user:{user_id}*"
        keys = await self.get_keys(pattern, self.RANKING_PREFIX)
        
        if not keys:
            return 0
        
        deleted = 0
        for key in keys:
            if await self.delete(key, self.RANKING_PREFIX):
                deleted += 1
        
        logger.info("Invalidated user cache", user_id=user_id, deleted_count=deleted)
        return deleted
    
    def _build_key(self, key: str, prefix: str) -> str:
        """Build full cache key with prefix"""
        return f"{prefix}{key}"
    
    def _generate_ranking_cache_key(self, user_id: str, filters: Dict[str, Any]) -> str:
        """Generate cache key for ranking results"""
        # Sort filters for consistent key generation
        sorted_filters = json.dumps(filters, sort_keys=True, default=str)
        return f"user:{user_id}:filters:{hash(sorted_filters)}"
    
    def _update_hit_rate(self):
        """Update cache hit rate"""
        total = self.stats["cache_hits"] + self.stats["cache_misses"]
        if total > 0:
            self.stats["hit_rate"] = self.stats["cache_hits"] / total
    
    async def _test_connection(self):
        """Test Redis connection"""
        try:
            pong = await self.redis_client.ping()
            if pong:
                logger.info("Redis connection test successful")
            else:
                raise RuntimeError("Redis ping failed")
        except Exception as e:
            logger.error("Redis connection test failed", error=str(e))
            raise
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache service statistics"""
        cache_info = {}
        
        if self.initialized:
            try:
                info = await self.redis_client.info()
                cache_info = {
                    "redis_version": info.get("redis_version"),
                    "used_memory_human": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "total_commands_processed": info.get("total_commands_processed"),
                    "keyspace_hits": info.get("keyspace_hits"),
                    "keyspace_misses": info.get("keyspace_misses"),
                }
            except Exception as e:
                logger.warning("Could not get Redis info", error=str(e))
        
        return {
            "initialized": self.initialized,
            "service_stats": self.stats.copy(),
            "redis_info": cache_info,
        }
    
    async def health_check(self) -> bool:
        """Check cache service health"""
        if not self.initialized:
            return False
        
        try:
            pong = await self.redis_client.ping()
            return pong
        except Exception as e:
            logger.error("Cache health check failed", error=str(e))
            return False
    
    async def cleanup(self):
        """Cleanup cache service resources"""
        if self.redis_client:
            await self.redis_client.close()
            self.initialized = False
            logger.info("Cache service cleaned up")