"""
Embedding service for generating vector embeddings using OpenAI
"""

import asyncio
from typing import List, Optional
import structlog
import openai
import tiktoken

from ..config.settings import settings

logger = structlog.get_logger()


class EmbeddingService:
    """Service for generating and managing vector embeddings"""
    
    def __init__(self):
        self.client = None
        self.tokenizer = None
        self.initialized = False
        
        # Performance tracking
        self.stats = {
            "total_embeddings_generated": 0,
            "total_tokens_processed": 0,
            "average_response_time": 0,
            "error_count": 0,
        }
    
    async def initialize(self):
        """Initialize the embedding service"""
        try:
            # Initialize OpenAI client
            self.client = openai.AsyncOpenAI(
                api_key=settings.openai_api_key,
                timeout=settings.openai_timeout,
                max_retries=settings.openai_max_retries,
            )
            
            # Initialize tokenizer for the embedding model
            self.tokenizer = tiktoken.encoding_for_model("text-embedding-3-small")
            
            # Test the connection
            await self._test_connection()
            
            self.initialized = True
            logger.info("Embedding service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize embedding service", error=str(e))
            raise
    
    async def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """
        Generate embedding for a single text
        
        Args:
            text: Text to generate embedding for
            model: OpenAI model to use (defaults to settings.openai_model)
            
        Returns:
            List of float values representing the embedding
        """
        if not self.initialized:
            raise RuntimeError("Embedding service not initialized")
        
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")
        
        try:
            # Clean and prepare text
            cleaned_text = self._prepare_text(text)
            
            # Check token count
            token_count = len(self.tokenizer.encode(cleaned_text))
            if token_count > 8191:  # OpenAI's token limit for text-embedding-3-small
                cleaned_text = self._truncate_text(cleaned_text, 8191)
                logger.warning(
                    "Text truncated due to token limit",
                    original_tokens=token_count,
                    truncated_tokens=8191
                )
            
            # Generate embedding
            response = await self.client.embeddings.create(
                model=model or settings.openai_model,
                input=cleaned_text,
                encoding_format="float"
            )
            
            embedding = response.data[0].embedding
            
            # Update stats
            self.stats["total_embeddings_generated"] += 1
            self.stats["total_tokens_processed"] += token_count
            
            logger.debug(
                "Generated embedding",
                text_length=len(text),
                tokens=token_count,
                embedding_dimensions=len(embedding)
            )
            
            return embedding
            
        except Exception as e:
            self.stats["error_count"] += 1
            logger.error("Failed to generate embedding", error=str(e), text_length=len(text))
            raise
    
    async def generate_embeddings_batch(
        self,
        texts: List[str],
        model: Optional[str] = None,
        batch_size: int = 100
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches
        
        Args:
            texts: List of texts to generate embeddings for
            model: OpenAI model to use
            batch_size: Number of texts to process in each batch
            
        Returns:
            List of embeddings corresponding to input texts
        """
        if not self.initialized:
            raise RuntimeError("Embedding service not initialized")
        
        if not texts:
            return []
        
        all_embeddings = []
        
        # Process in batches
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            try:
                # Clean and prepare texts
                cleaned_batch = [self._prepare_text(text) for text in batch]
                
                # Check token counts and truncate if necessary
                processed_batch = []
                for text in cleaned_batch:
                    token_count = len(self.tokenizer.encode(text))
                    if token_count > 8191:
                        text = self._truncate_text(text, 8191)
                    processed_batch.append(text)
                
                # Generate embeddings for batch
                response = await self.client.embeddings.create(
                    model=model or settings.openai_model,
                    input=processed_batch,
                    encoding_format="float"
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
                
                # Update stats
                self.stats["total_embeddings_generated"] += len(batch)
                total_tokens = sum(len(self.tokenizer.encode(text)) for text in processed_batch)
                self.stats["total_tokens_processed"] += total_tokens
                
                logger.info(
                    "Generated batch embeddings",
                    batch_size=len(batch),
                    total_processed=len(all_embeddings),
                    remaining=len(texts) - len(all_embeddings)
                )
                
                # Rate limiting to avoid hitting OpenAI limits
                if i + batch_size < len(texts):
                    await asyncio.sleep(0.1)
                
            except Exception as e:
                self.stats["error_count"] += 1
                logger.error(
                    "Failed to generate batch embeddings",
                    error=str(e),
                    batch_start=i,
                    batch_size=len(batch)
                )
                # Add empty embeddings for failed batch
                empty_embeddings = [[0.0] * settings.embedding_dimensions] * len(batch)
                all_embeddings.extend(empty_embeddings)
        
        return all_embeddings
    
    async def calculate_similarity(
        self,
        embedding1: List[float],
        embedding2: List[float]
    ) -> float:
        """
        Calculate cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            
        Returns:
            Similarity score between 0 and 1
        """
        if len(embedding1) != len(embedding2):
            raise ValueError("Embeddings must have the same dimensions")
        
        # Calculate cosine similarity
        dot_product = sum(a * b for a, b in zip(embedding1, embedding2))
        norm1 = sum(a * a for a in embedding1) ** 0.5
        norm2 = sum(b * b for b in embedding2) ** 0.5
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        similarity = dot_product / (norm1 * norm2)
        
        # Clamp to [0, 1] range
        return max(0.0, min(1.0, similarity))
    
    async def find_most_similar(
        self,
        query_embedding: List[float],
        candidate_embeddings: List[List[float]],
        threshold: float = 0.7,
        top_k: int = 10
    ) -> List[tuple]:
        """
        Find most similar embeddings to a query embedding
        
        Args:
            query_embedding: Query embedding to compare against
            candidate_embeddings: List of candidate embeddings
            threshold: Minimum similarity threshold
            top_k: Maximum number of results to return
            
        Returns:
            List of (index, similarity_score) tuples sorted by similarity
        """
        similarities = []
        
        for i, candidate in enumerate(candidate_embeddings):
            try:
                similarity = await self.calculate_similarity(query_embedding, candidate)
                if similarity >= threshold:
                    similarities.append((i, similarity))
            except Exception as e:
                logger.warning(f"Error calculating similarity for index {i}", error=str(e))
                continue
        
        # Sort by similarity (highest first) and return top_k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def _prepare_text(self, text: str) -> str:
        """Prepare text for embedding generation"""
        if not text:
            return ""
        
        # Clean the text
        cleaned = text.strip()
        
        # Replace multiple whitespaces with single space
        cleaned = " ".join(cleaned.split())
        
        # Remove control characters
        cleaned = "".join(char for char in cleaned if ord(char) >= 32 or char in '\n\t')
        
        return cleaned
    
    def _truncate_text(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit"""
        tokens = self.tokenizer.encode(text)
        
        if len(tokens) <= max_tokens:
            return text
        
        # Truncate tokens and decode back to text
        truncated_tokens = tokens[:max_tokens]
        truncated_text = self.tokenizer.decode(truncated_tokens)
        
        # Try to end at a sentence boundary
        sentences = truncated_text.split('.')
        if len(sentences) > 1:
            # Remove the last incomplete sentence
            truncated_text = '.'.join(sentences[:-1]) + '.'
        
        return truncated_text
    
    async def _test_connection(self):
        """Test the OpenAI connection"""
        try:
            response = await self.client.embeddings.create(
                model=settings.openai_model,
                input="test connection",
                encoding_format="float"
            )
            
            if not response.data or len(response.data[0].embedding) == 0:
                raise RuntimeError("Invalid response from OpenAI API")
            
            logger.info("OpenAI connection test successful")
            
        except Exception as e:
            logger.error("OpenAI connection test failed", error=str(e))
            raise
    
    def get_stats(self) -> dict:
        """Get embedding service statistics"""
        return {
            "initialized": self.initialized,
            "model": settings.openai_model,
            "embedding_dimensions": settings.embedding_dimensions,
            "stats": self.stats.copy(),
        }
    
    def is_healthy(self) -> bool:
        """Check if the service is healthy"""
        return self.initialized and self.client is not None