"""
Ranking API endpoints for job matching and recommendations
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, Request, Query, Depends
from pydantic import BaseModel, Field, validator

from ...config.settings import settings
from ...services.ranking_service import RankingService

router = APIRouter()


class RankingFilters(BaseModel):
    """Filters for job ranking requests"""
    
    country: Optional[str] = Field(None, description="Country code (e.g., 'IN', 'US')")
    is_remote: Optional[bool] = Field(None, description="Filter for remote jobs only")
    experience_level: Optional[str] = Field(None, description="Experience level: entry, mid, senior, executive")
    min_salary: Optional[int] = Field(None, description="Minimum salary requirement")
    max_salary: Optional[int] = Field(None, description="Maximum salary requirement")
    skills: Optional[List[str]] = Field(None, description="Required skills")
    location: Optional[str] = Field(None, description="Preferred location")
    exclude_applied_jobs: Optional[bool] = Field(True, description="Exclude jobs user has already applied to")
    
    @validator('experience_level')
    def validate_experience_level(cls, v):
        if v and v not in ['entry', 'mid', 'senior', 'executive']:
            raise ValueError('Experience level must be one of: entry, mid, senior, executive')
        return v


class RankingRequest(BaseModel):
    """Request model for job ranking"""
    
    user_id: str = Field(..., description="User ID for personalized ranking")
    filters: Optional[RankingFilters] = Field(None, description="Additional filtering criteria")
    limit: Optional[int] = Field(50, description="Maximum number of jobs to return", ge=1, le=1000)
    use_cache: Optional[bool] = Field(True, description="Whether to use cached results")


class QueryRankingRequest(BaseModel):
    """Request model for text query-based ranking"""
    
    query: str = Field(..., description="Text query describing desired job", min_length=3, max_length=1000)
    filters: Optional[RankingFilters] = Field(None, description="Additional filtering criteria")
    limit: Optional[int] = Field(50, description="Maximum number of jobs to return", ge=1, le=1000)
    use_cache: Optional[bool] = Field(True, description="Whether to use cached results")


class JobSummary(BaseModel):
    """Summary model for ranked job"""
    
    id: str
    title: str
    company: str
    location: Optional[str]
    country: str
    is_remote: bool
    salary_min: Optional[int]
    salary_max: Optional[int]
    currency: Optional[str]
    experience_level: Optional[str]
    skills: Optional[List[str]]
    posted_at: str
    url: str
    similarity: float
    ranking_score: Optional[float] = None
    ranking_breakdown: Optional[Dict[str, float]] = None


class RankingResponse(BaseModel):
    """Response model for job ranking"""
    
    user_id: Optional[str]
    query: Optional[str]
    total_jobs: int
    jobs: List[JobSummary]
    filters_applied: Dict[str, Any]
    ranking_metadata: Dict[str, Any]


def get_ranking_service(request: Request) -> RankingService:
    """Dependency to get ranking service from app state"""
    if not hasattr(request.app.state, 'ranking_service'):
        raise HTTPException(status_code=503, detail="Ranking service not available")
    return request.app.state.ranking_service


@router.post("/user", response_model=RankingResponse)
async def rank_jobs_for_user(
    request: RankingRequest,
    ranking_service: RankingService = Depends(get_ranking_service)
):
    """
    Rank jobs for a specific user based on their profile and preferences
    
    This endpoint provides personalized job recommendations using:
    - User's resume and skills embedding
    - User preferences and job history
    - Advanced multi-factor ranking algorithm
    - Vector similarity search with pgvector
    
    The ranking considers:
    - Skill match (40% weight)
    - Experience level (25% weight)  
    - Location preference (20% weight)
    - Salary compatibility (15% weight)
    """
    try:
        # Validate request
        if not request.user_id:
            raise HTTPException(status_code=400, detail="User ID is required")
        
        # Convert filters to dict
        filters_dict = request.filters.dict(exclude_none=True) if request.filters else {}
        
        # Call ranking service
        result = await ranking_service.rank_jobs_for_user(
            user_id=request.user_id,
            filters=filters_dict,
            limit=request.limit,
            use_cache=request.use_cache
        )
        
        # Convert to response format
        jobs = [
            JobSummary(
                id=job["id"],
                title=job["title"],
                company=job["company"],
                location=job.get("location"),
                country=job["country"],
                is_remote=job["is_remote"],
                salary_min=job.get("salary_min"),
                salary_max=job.get("salary_max"),
                currency=job.get("currency"),
                experience_level=job.get("experience_level"),
                skills=job.get("skills"),
                posted_at=job["posted_at"],
                url=job["url"],
                similarity=job["similarity"],
                ranking_score=job.get("ranking_score"),
                ranking_breakdown=job.get("ranking_breakdown")
            )
            for job in result["jobs"]
        ]
        
        return RankingResponse(
            user_id=request.user_id,
            query=None,
            total_jobs=result["total_jobs"],
            jobs=jobs,
            filters_applied=result["filters_applied"],
            ranking_metadata=result["ranking_metadata"]
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/query", response_model=RankingResponse)
async def rank_jobs_by_query(
    request: QueryRankingRequest,
    ranking_service: RankingService = Depends(get_ranking_service)
):
    """
    Rank jobs based on a text query
    
    This endpoint allows searching for jobs using natural language queries.
    Examples:
    - "Senior Python developer with machine learning experience"
    - "Remote data scientist job in fintech"
    - "Frontend engineer position in Bangalore"
    
    The system will:
    1. Generate embeddings for the query text
    2. Find similar jobs using vector search
    3. Return ranked results by similarity score
    """
    try:
        # Validate request
        if not request.query or len(request.query.strip()) < 3:
            raise HTTPException(status_code=400, detail="Query must be at least 3 characters long")
        
        # Convert filters to dict
        filters_dict = request.filters.dict(exclude_none=True) if request.filters else {}
        
        # Call ranking service
        result = await ranking_service.rank_jobs_by_query(
            query_text=request.query,
            filters=filters_dict,
            limit=request.limit,
            use_cache=request.use_cache
        )
        
        # Convert to response format
        jobs = [
            JobSummary(
                id=job["id"],
                title=job["title"],
                company=job["company"],
                location=job.get("location"),
                country=job["country"],
                is_remote=job["is_remote"],
                salary_min=job.get("salary_min"),
                salary_max=job.get("salary_max"),
                currency=job.get("currency"),
                experience_level=job.get("experience_level"),
                skills=job.get("skills"),
                posted_at=job["posted_at"],
                url=job["url"],
                similarity=job["similarity"],
                ranking_score=None,  # Query-based ranking doesn't use advanced scoring
                ranking_breakdown=None
            )
            for job in result["jobs"]
        ]
        
        return RankingResponse(
            user_id=None,
            query=request.query,
            total_jobs=result["total_jobs"],
            jobs=jobs,
            filters_applied=result["filters_applied"],
            ranking_metadata=result["ranking_metadata"]
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/stats")
async def get_ranking_stats(
    ranking_service: RankingService = Depends(get_ranking_service)
):
    """
    Get ranking service statistics and performance metrics
    
    Returns information about:
    - Total rankings performed
    - Cache hit/miss rates
    - Average response times
    - Service configuration
    """
    try:
        stats = await ranking_service.get_service_stats()
        return {
            "timestamp": datetime.now().isoformat(),
            "service": "vector-ranking-api",
            "version": settings.version,
            **stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")


@router.delete("/cache")
async def clear_ranking_cache(
    ranking_service: RankingService = Depends(get_ranking_service)
):
    """
    Clear ranking cache
    
    This endpoint clears all cached ranking results.
    Use with caution as it will impact performance until cache is rebuilt.
    """
    try:
        success = await ranking_service.clear_cache()
        
        if success:
            return {
                "message": "Ranking cache cleared successfully",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to clear cache")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")


@router.delete("/cache/user/{user_id}")
async def clear_user_cache(
    user_id: str,
    ranking_service: RankingService = Depends(get_ranking_service)
):
    """
    Clear cache for a specific user
    
    This endpoint clears all cached ranking results for a specific user.
    Useful when user profile is updated and fresh rankings are needed.
    """
    try:
        cache_service = ranking_service.cache_service
        deleted_count = await cache_service.invalidate_user_cache(user_id)
        
        return {
            "message": f"Cleared {deleted_count} cache entries for user {user_id}",
            "user_id": user_id,
            "deleted_count": deleted_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear user cache: {str(e)}")


@router.get("/config")
async def get_ranking_config():
    """
    Get ranking service configuration
    
    Returns current configuration parameters for the ranking algorithm
    """
    return {
        "similarity_threshold": settings.similarity_threshold,
        "max_results": settings.max_results,
        "embedding_model": settings.openai_model,
        "embedding_dimensions": settings.embedding_dimensions,
        "cache_ttl_seconds": settings.cache_ttl_seconds,
        "ranking_weights": settings.get_ranking_weights(),
        "environment": settings.environment,
        "version": settings.version
    }