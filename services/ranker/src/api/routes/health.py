"""
Health check endpoints for the Vector Ranking API
"""

from datetime import datetime
from fastapi import APIRouter, Request
from pydantic import BaseModel
from typing import Dict, Any

from ...database.connection import DatabaseManager
from ...config.settings import settings

router = APIRouter()


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    timestamp: str
    service: str
    version: str
    uptime: float
    environment: str


class ReadinessResponse(BaseModel):
    """Readiness check response model"""
    status: str
    timestamp: str
    checks: Dict[str, Any]


@router.get("/", response_model=HealthResponse)
async def health_check():
    """
    Basic health check endpoint
    
    Returns service status and basic information
    """
    import time
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        service="karmsakha-vector-ranking-api",
        version=settings.version,
        uptime=time.time(),
        environment=settings.environment
    )


@router.get("/ready", response_model=ReadinessResponse)
async def readiness_check(request: Request):
    """
    Readiness check endpoint with dependency validation
    
    Checks if all required services are available and ready
    """
    timestamp = datetime.now().isoformat()
    checks = {}
    
    # Database check
    try:
        async with DatabaseManager.get_session() as session:
            await session.execute("SELECT 1")
        checks["database"] = {
            "status": "healthy",
            "message": "Database connection successful",
            "timestamp": timestamp
        }
    except Exception as e:
        checks["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}",
            "timestamp": timestamp
        }
    
    # Cache service check
    if hasattr(request.app.state, 'ranking_service'):
        try:
            ranking_service = request.app.state.ranking_service
            cache_healthy = await ranking_service.cache_service.health_check()
            checks["cache"] = {
                "status": "healthy" if cache_healthy else "unhealthy",
                "message": "Cache service operational" if cache_healthy else "Cache service unavailable",
                "timestamp": timestamp
            }
        except Exception as e:
            checks["cache"] = {
                "status": "unhealthy",
                "message": f"Cache check failed: {str(e)}",
                "timestamp": timestamp
            }
    else:
        checks["cache"] = {
            "status": "unhealthy",
            "message": "Ranking service not initialized",
            "timestamp": timestamp
        }
    
    # Embedding service check
    if hasattr(request.app.state, 'embedding_service'):
        try:
            embedding_service = request.app.state.embedding_service
            embedding_healthy = embedding_service.is_healthy()
            checks["embedding_service"] = {
                "status": "healthy" if embedding_healthy else "unhealthy",
                "message": "Embedding service operational" if embedding_healthy else "Embedding service unavailable",
                "timestamp": timestamp
            }
        except Exception as e:
            checks["embedding_service"] = {
                "status": "unhealthy",
                "message": f"Embedding service check failed: {str(e)}",
                "timestamp": timestamp
            }
    else:
        checks["embedding_service"] = {
            "status": "unhealthy",
            "message": "Embedding service not initialized",
            "timestamp": timestamp
        }
    
    # Overall status
    all_healthy = all(check["status"] == "healthy" for check in checks.values())
    status = "ready" if all_healthy else "not ready"
    
    return ReadinessResponse(
        status=status,
        timestamp=timestamp,
        checks=checks
    )


@router.get("/live")
async def liveness_check():
    """
    Liveness check endpoint
    
    Simple endpoint to verify the service is running
    """
    return {
        "status": "alive",
        "timestamp": datetime.now().isoformat()
    }