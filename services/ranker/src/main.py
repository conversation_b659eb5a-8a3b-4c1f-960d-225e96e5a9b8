"""
KarmSakha Vector Ranking API
FastAPI service for AI-powered job matching using vector similarity search
"""

import asyncio
import time
from contextlib import asynccontextmanager

import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from starlette.middleware.base import BaseHTTPMiddleware

from .config.settings import settings
from .database.connection import DatabaseManager
from .api.routes import health, ranking, jobs, users
from .services.ranking_service import RankingService
from .services.embedding_service import EmbeddingService
from .middleware.auth import AuthMiddleware
from .middleware.rate_limit import RateLimitMiddleware
from .utils.logging import setup_logging

# Setup structured logging
logger = structlog.get_logger()

# Prometheus metrics
REQUEST_COUNT = Counter('ranker_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('ranker_request_duration_seconds', 'Request duration')

class MetricsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        response = await call_next(request)
        
        duration = time.time() - start_time
        REQUEST_DURATION.observe(duration)
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status=response.status_code
        ).inc()
        
        return response

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting Vector Ranking API", version=settings.version)
    
    # Initialize services
    try:
        # Setup database connection
        await DatabaseManager.initialize()
        logger.info("Database connection established")
        
        # Initialize ranking service
        ranking_service = RankingService()
        await ranking_service.initialize()
        app.state.ranking_service = ranking_service
        logger.info("Ranking service initialized")
        
        # Initialize embedding service
        embedding_service = EmbeddingService()
        await embedding_service.initialize()
        app.state.embedding_service = embedding_service
        logger.info("Embedding service initialized")
        
    except Exception as e:
        logger.error("Failed to initialize services", error=str(e))
        raise
    
    yield
    
    # Cleanup
    logger.info("Shutting down Vector Ranking API")
    try:
        await DatabaseManager.close()
        logger.info("Database connection closed")
    except Exception as e:
        logger.error("Error during shutdown", error=str(e))

# Create FastAPI application
app = FastAPI(
    title="KarmSakha Vector Ranking API",
    description="AI-powered job matching using vector similarity search",
    version=settings.version,
    docs_url="/docs" if settings.environment == "development" else None,
    redoc_url="/redoc" if settings.environment == "development" else None,
    openapi_url="/openapi.json" if settings.environment != "production" else None,
    lifespan=lifespan,
)

# Setup logging
setup_logging(settings.log_level, settings.environment)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(MetricsMiddleware)
app.add_middleware(RateLimitMiddleware)

if settings.auth_enabled:
    app.add_middleware(AuthMiddleware)

# Include routers
app.include_router(health.router, prefix="/health", tags=["Health"])
app.include_router(ranking.router, prefix="/api/v1/ranking", tags=["Ranking"])
app.include_router(jobs.router, prefix="/api/v1/jobs", tags=["Jobs"])
app.include_router(users.router, prefix="/api/v1/users", tags=["Users"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "karmsakha-vector-ranking-api",
        "version": settings.version,
        "status": "healthy",
        "timestamp": time.time(),
    }

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(
        "Unhandled exception",
        path=request.url.path,
        method=request.method,
        error=str(exc),
        exc_info=True,
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "timestamp": time.time(),
        },
    )

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.environment == "development",
        workers=1 if settings.environment == "development" else 4,
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "root": {
                "level": settings.log_level,
                "handlers": ["default"],
            },
        },
    )