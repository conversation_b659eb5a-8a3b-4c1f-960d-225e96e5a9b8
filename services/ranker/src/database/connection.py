"""
Database connection and management for Vector Ranking API
"""

import asyncio
from typing import List, Optional, Dict, Any
import structlog
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import NullPool
from sqlalchemy import text, select, func
from contextlib import asynccontextmanager

from ..config.settings import settings
from .models import Job, User, Application

logger = structlog.get_logger()


class DatabaseManager:
    """Database connection manager"""
    
    _engine = None
    _session_factory = None
    _initialized = False
    
    @classmethod
    async def initialize(cls):
        """Initialize database connection"""
        if cls._initialized:
            return
        
        try:
            # Create async engine
            cls._engine = create_async_engine(
                settings.database_url,
                pool_size=settings.database_pool_size,
                max_overflow=settings.database_max_overflow,
                pool_timeout=settings.database_pool_timeout,
                pool_pre_ping=True,
                echo=settings.is_development(),
            )
            
            # Create session factory
            cls._session_factory = async_sessionmaker(
                cls._engine,
                class_=AsyncSession,
                expire_on_commit=False,
            )
            
            # Test connection and pgvector extension
            await cls._test_connection()
            await cls._ensure_pgvector_extension()
            
            cls._initialized = True
            logger.info("Database connection initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize database connection", error=str(e))
            raise
    
    @classmethod
    async def close(cls):
        """Close database connection"""
        if cls._engine:
            await cls._engine.dispose()
            cls._initialized = False
            logger.info("Database connection closed")
    
    @classmethod
    @asynccontextmanager
    async def get_session(cls):
        """Get database session context manager"""
        if not cls._initialized:
            await cls.initialize()
        
        async with cls._session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    @classmethod
    async def _test_connection(cls):
        """Test database connection"""
        async with cls._session_factory() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
            logger.info("Database connection test successful")
    
    @classmethod
    async def _ensure_pgvector_extension(cls):
        """Ensure pgvector extension is available"""
        async with cls._session_factory() as session:
            # Check if pgvector extension exists
            result = await session.execute(
                text("SELECT 1 FROM pg_extension WHERE extname = 'vector'")
            )
            
            if not result.scalar():
                logger.warning("pgvector extension not found, attempting to create")
                try:
                    await session.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
                    await session.commit()
                    logger.info("pgvector extension created successfully")
                except Exception as e:
                    logger.error("Failed to create pgvector extension", error=str(e))
                    raise
            else:
                logger.info("pgvector extension is available")


class JobRepository:
    """Repository for job-related database operations"""
    
    @staticmethod
    async def get_jobs_with_embeddings(
        limit: int = 1000,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Job]:
        """Get jobs with embeddings for ranking"""
        async with DatabaseManager.get_session() as session:
            query = select(Job).where(
                Job.embedding.isnot(None),
                Job.is_active == True
            )
            
            # Apply filters
            if filters:
                if filters.get('country'):
                    query = query.where(Job.country == filters['country'])
                
                if filters.get('is_remote') is not None:
                    query = query.where(Job.is_remote == filters['is_remote'])
                
                if filters.get('experience_level'):
                    query = query.where(Job.experience_level == filters['experience_level'])
                
                if filters.get('min_salary'):
                    query = query.where(Job.salary_min >= filters['min_salary'])
                
                if filters.get('max_salary'):
                    query = query.where(Job.salary_max <= filters['max_salary'])
                
                if filters.get('skills'):
                    skills = filters['skills']
                    if isinstance(skills, list):
                        # Check if any of the required skills are in job skills
                        query = query.where(Job.skills.op('&&')(skills))
            
            query = query.order_by(Job.posted_at.desc()).limit(limit).offset(offset)
            
            result = await session.execute(query)
            return result.scalars().all()
    
    @staticmethod
    async def find_similar_jobs(
        query_embedding: List[float],
        threshold: float = 0.8,
        limit: int = 50,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Find similar jobs using vector similarity"""
        async with DatabaseManager.get_session() as session:
            # Convert list to pgvector format
            embedding_str = "[" + ",".join(map(str, query_embedding)) + "]"
            
            # Build base query with similarity calculation
            similarity_expr = text(f"1 - (embedding <=> '{embedding_str}'::vector)")
            
            query = select(
                Job.id,
                Job.title,
                Job.company,
                Job.location,
                Job.country,
                Job.is_remote,
                Job.salary_min,
                Job.salary_max,
                Job.currency,
                Job.experience_level,
                Job.employment_type,
                Job.skills,
                Job.posted_at,
                Job.url,
                similarity_expr.label('similarity')
            ).where(
                Job.embedding.isnot(None),
                Job.is_active == True,
                similarity_expr >= threshold
            )
            
            # Apply filters
            if filters:
                if filters.get('country'):
                    query = query.where(Job.country == filters['country'])
                
                if filters.get('is_remote') is not None:
                    query = query.where(Job.is_remote == filters['is_remote'])
                
                if filters.get('experience_level'):
                    query = query.where(Job.experience_level == filters['experience_level'])
                
                if filters.get('exclude_applied_jobs') and filters.get('user_id'):
                    # Exclude jobs user has already applied to
                    applied_subquery = select(Application.job_id).where(
                        Application.user_id == filters['user_id']
                    )
                    query = query.where(~Job.id.in_(applied_subquery))
            
            # Order by similarity (highest first) and limit
            query = query.order_by(text('similarity DESC')).limit(limit)
            
            result = await session.execute(query)
            return [dict(row._mapping) for row in result]
    
    @staticmethod
    async def get_job_by_id(job_id: str) -> Optional[Job]:
        """Get job by ID"""
        async with DatabaseManager.get_session() as session:
            result = await session.execute(
                select(Job).where(Job.id == job_id)
            )
            return result.scalar_one_or_none()
    
    @staticmethod
    async def update_job_embedding(job_id: str, embedding: List[float]) -> bool:
        """Update job embedding"""
        async with DatabaseManager.get_session() as session:
            embedding_str = "[" + ",".join(map(str, embedding)) + "]"
            
            result = await session.execute(
                text("""
                    UPDATE jobs 
                    SET embedding = :embedding::vector, updated_at = NOW() 
                    WHERE id = :job_id
                """),
                {"embedding": embedding_str, "job_id": job_id}
            )
            
            return result.rowcount > 0


class UserRepository:
    """Repository for user-related database operations"""
    
    @staticmethod
    async def get_user_by_id(user_id: str) -> Optional[User]:
        """Get user by ID"""
        async with DatabaseManager.get_session() as session:
            result = await session.execute(
                select(User).where(User.id == user_id)
            )
            return result.scalar_one_or_none()
    
    @staticmethod
    async def update_user_embedding(user_id: str, embedding: List[float]) -> bool:
        """Update user resume embedding"""
        async with DatabaseManager.get_session() as session:
            embedding_str = "[" + ",".join(map(str, embedding)) + "]"
            
            result = await session.execute(
                text("""
                    UPDATE users 
                    SET resume_embedding = :embedding::vector, updated_at = NOW() 
                    WHERE id = :user_id
                """),
                {"embedding": embedding_str, "user_id": user_id}
            )
            
            return result.rowcount > 0
    
    @staticmethod
    async def get_user_preferences(user_id: str) -> Dict[str, Any]:
        """Get user job preferences"""
        async with DatabaseManager.get_session() as session:
            result = await session.execute(
                select(User.preferences, User.preferred_locations, User.remote_preference)
                .where(User.id == user_id)
            )
            row = result.first()
            
            if row:
                return {
                    "preferences": row.preferences or {},
                    "preferred_locations": row.preferred_locations or [],
                    "remote_preference": row.remote_preference or False,
                }
            
            return {}


class ApplicationRepository:
    """Repository for application-related database operations"""
    
    @staticmethod
    async def get_user_applications(user_id: str) -> List[str]:
        """Get list of job IDs user has applied to"""
        async with DatabaseManager.get_session() as session:
            result = await session.execute(
                select(Application.job_id).where(Application.user_id == user_id)
            )
            return [row.job_id for row in result]
    
    @staticmethod
    async def create_application(user_id: str, job_id: str, similarity_score: float) -> str:
        """Create new application record"""
        async with DatabaseManager.get_session() as session:
            application = Application(
                user_id=user_id,
                job_id=job_id,
                similarity_score=similarity_score,
                status="queued"
            )
            
            session.add(application)
            await session.flush()
            
            return application.id


class AnalyticsRepository:
    """Repository for analytics and reporting"""
    
    @staticmethod
    async def get_ranking_stats() -> Dict[str, Any]:
        """Get ranking service statistics"""
        async with DatabaseManager.get_session() as session:
            # Total jobs with embeddings
            jobs_with_embeddings = await session.execute(
                select(func.count(Job.id)).where(
                    Job.embedding.isnot(None),
                    Job.is_active == True
                )
            )
            
            # Total users with embeddings
            users_with_embeddings = await session.execute(
                select(func.count(User.id)).where(
                    User.resume_embedding.isnot(None),
                    User.is_active == True
                )
            )
            
            # Recent applications
            recent_applications = await session.execute(
                select(func.count(Application.id)).where(
                    Application.created_at >= func.now() - text("INTERVAL '24 hours'")
                )
            )
            
            return {
                "jobs_with_embeddings": jobs_with_embeddings.scalar() or 0,
                "users_with_embeddings": users_with_embeddings.scalar() or 0,
                "recent_applications": recent_applications.scalar() or 0,
                "timestamp": func.now(),
            }