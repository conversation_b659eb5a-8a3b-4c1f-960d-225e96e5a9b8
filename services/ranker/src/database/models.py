"""
SQLAlchemy models for the Vector Ranking API
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import uuid4
from sqlalchemy import (
    Column, String, Integer, Float, Boolean, DateTime, Text, 
    JSON, ARRAY, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from pgvector.sqlalchemy import Vector

Base = declarative_base()


class Job(Base):
    """Job model with vector embeddings"""
    
    __tablename__ = "jobs"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Source information
    source = Column(String(50), nullable=False, index=True)
    external_id = Column(String(255), nullable=False)
    url = Column(Text, nullable=False)
    
    # Job details
    title = Column(String(200), nullable=False, index=True)
    company = Column(String(100), nullable=False, index=True)
    location = Column(String(100), index=True)
    country = Column(String(10), default="IN", index=True)
    is_remote = Column(Boolean, default=False, index=True)
    
    # Employment details
    employment_type = Column(String(50), default="full-time")  # full-time, part-time, contract, internship
    experience_level = Column(String(50), index=True)  # entry, mid, senior, executive
    
    # Salary information
    salary_min = Column(Integer)
    salary_max = Column(Integer)
    currency = Column(String(10), default="INR")
    
    # Job content
    description = Column(Text)
    requirements = Column(Text)
    skills = Column(ARRAY(String), index=True)  # Array of required skills
    
    # Dates
    posted_at = Column(DateTime, nullable=False, index=True)
    expires_at = Column(DateTime)
    
    # Vector embedding for similarity search
    embedding = Column(Vector(1536), index=True)  # OpenAI text-embedding-3-small dimension
    
    # Metadata
    raw_data = Column(JSON)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    applications = relationship("Application", back_populates="job")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('source', 'external_id', name='uq_job_source_external_id'),
        Index('idx_jobs_embedding_cosine', 'embedding', postgresql_using='ivfflat', 
              postgresql_ops={'embedding': 'vector_cosine_ops'}),
        Index('idx_jobs_location_remote', 'country', 'is_remote'),
        Index('idx_jobs_salary', 'salary_min', 'salary_max'),
        Index('idx_jobs_posted_at_desc', 'posted_at', postgresql_using='btree'),
    )
    
    def to_dict(self, include_embedding: bool = False) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {
            "id": str(self.id),
            "source": self.source,
            "external_id": self.external_id,
            "url": self.url,
            "title": self.title,
            "company": self.company,
            "location": self.location,
            "country": self.country,
            "is_remote": self.is_remote,
            "employment_type": self.employment_type,
            "experience_level": self.experience_level,
            "salary_min": self.salary_min,
            "salary_max": self.salary_max,
            "currency": self.currency,
            "description": self.description,
            "requirements": self.requirements,
            "skills": self.skills,
            "posted_at": self.posted_at.isoformat() if self.posted_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
        
        if include_embedding and self.embedding:
            data["embedding"] = list(self.embedding)
        
        return data


class User(Base):
    """User model with resume embeddings"""
    
    __tablename__ = "users"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # User information
    email = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20))
    
    # Location preferences
    location = Column(String(100))
    country = Column(String(10), default="IN")
    preferred_locations = Column(ARRAY(String))
    remote_preference = Column(Boolean, default=False)
    
    # Resume and skills
    resume_text = Column(Text)
    resume_embedding = Column(Vector(1536), index=True)
    skills = Column(ARRAY(String), index=True)
    experience_years = Column(Integer)
    current_title = Column(String(100))
    
    # Salary preferences
    preferred_salary_min = Column(Integer)
    preferred_salary_max = Column(Integer)
    preferred_currency = Column(String(10), default="INR")
    
    # Additional preferences
    preferences = Column(JSON)  # Additional job preferences as JSON
    
    # Status
    is_active = Column(Boolean, default=True, index=True)
    email_verified = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    applications = relationship("Application", back_populates="user")
    preferences_settings = relationship("UserPreference", back_populates="user")
    
    # Indexes
    __table_args__ = (
        Index('idx_users_resume_embedding_cosine', 'resume_embedding', 
              postgresql_using='ivfflat', postgresql_ops={'resume_embedding': 'vector_cosine_ops'}),
        Index('idx_users_skills', 'skills', postgresql_using='gin'),
        Index('idx_users_location', 'country', 'location'),
    )
    
    def to_dict(self, include_embedding: bool = False, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {
            "id": str(self.id),
            "name": self.name,
            "location": self.location,
            "country": self.country,
            "preferred_locations": self.preferred_locations,
            "remote_preference": self.remote_preference,
            "skills": self.skills,
            "experience_years": self.experience_years,
            "current_title": self.current_title,
            "preferred_salary_min": self.preferred_salary_min,
            "preferred_salary_max": self.preferred_salary_max,
            "preferred_currency": self.preferred_currency,
            "preferences": self.preferences,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
        
        if include_sensitive:
            data.update({
                "email": self.email,
                "phone": self.phone,
                "email_verified": self.email_verified,
                "resume_text": self.resume_text,
            })
        
        if include_embedding and self.resume_embedding:
            data["resume_embedding"] = list(self.resume_embedding)
        
        return data


class Application(Base):
    """Job application tracking"""
    
    __tablename__ = "applications"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id"), nullable=False, index=True)
    
    # Application details
    status = Column(String(50), default="queued", index=True)  # queued, processing, applied, failed, rejected, interview, hired
    similarity_score = Column(Float)  # Cosine similarity score
    applied_at = Column(DateTime)
    
    # Response data from job portal
    response_data = Column(JSON)
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    
    # Additional metadata
    metadata = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="applications")
    job = relationship("Job", back_populates="applications")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'job_id', name='uq_application_user_job'),
        Index('idx_applications_user_status', 'user_id', 'status'),
        Index('idx_applications_job_status', 'job_id', 'status'),
        Index('idx_applications_similarity_score', 'similarity_score', postgresql_using='btree'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "job_id": str(self.job_id),
            "status": self.status,
            "similarity_score": self.similarity_score,
            "applied_at": self.applied_at.isoformat() if self.applied_at else None,
            "response_data": self.response_data,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class UserPreference(Base):
    """User preferences for detailed job matching"""
    
    __tablename__ = "user_preferences"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Foreign key
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Preference details
    preference_type = Column(String(50), nullable=False)  # notification, job_alert, privacy
    preference_key = Column(String(100), nullable=False)
    preference_value = Column(String(255), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="preferences_settings")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'preference_type', 'preference_key', 
                        name='uq_user_preference_type_key'),
    )


class Company(Base):
    """Company information for enhanced job data"""
    
    __tablename__ = "companies"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Company details
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text)
    website = Column(String(255))
    logo_url = Column(String(500))
    
    # Company metadata
    size_category = Column(String(50))  # startup, small, medium, large, enterprise
    industry = Column(String(100), index=True)
    headquarters = Column(String(100))
    glassdoor_rating = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "website": self.website,
            "logo_url": self.logo_url,
            "size_category": self.size_category,
            "industry": self.industry,
            "headquarters": self.headquarters,
            "glassdoor_rating": self.glassdoor_rating,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class RankingCache(Base):
    """Cache for ranking results to improve performance"""
    
    __tablename__ = "ranking_cache"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Cache key (hash of query parameters)
    cache_key = Column(String(64), unique=True, nullable=False, index=True)
    
    # Cached data
    query_params = Column(JSON)  # Original query parameters
    results = Column(JSON)  # Cached ranking results
    
    # Cache metadata
    expires_at = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_ranking_cache_expires_at', 'expires_at'),
    )