{"name": "@karmsakha/ingestor", "version": "1.0.0", "description": "Multi-source job ingestion service for KarmSakha platform", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit", "ingest:all": "tsx src/cli/ingest-all.ts", "ingest:shine": "tsx src/cli/ingest-shine.ts", "ingest:naukri": "tsx src/cli/ingest-naukri.ts", "ingest:linkedin": "tsx src/cli/ingest-linkedin.ts", "ingest:remote": "tsx src/cli/ingest-remote.ts"}, "dependencies": {"@bull-board/api": "^5.0.0", "@bull-board/express": "^5.0.0", "bullmq": "^4.0.0", "express": "^4.18.0", "helmet": "^7.0.0", "ioredis": "^5.3.0", "pg": "^8.11.0", "playwright": "^1.40.0", "openai": "^4.20.0", "axios": "^1.6.0", "cheerio": "^1.0.0", "rss-parser": "^3.13.0", "date-fns": "^2.30.0", "zod": "^3.22.0", "winston": "^3.11.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.0", "@types/pg": "^8.10.0", "@types/compression": "^1.7.0", "@types/cors": "^2.8.0", "@types/uuid": "^9.0.0", "@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=20.0.0"}, "keywords": ["jobs", "scraping", "ingestion", "ai", "vector-search"], "author": "KarmSakha Team", "license": "UNLICENSED"}