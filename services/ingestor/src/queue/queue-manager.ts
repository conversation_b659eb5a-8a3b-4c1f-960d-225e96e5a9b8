import { Queue, Worker, Job } from 'bullmq';
import IORedis from 'ioredis';
import { logger } from '../utils/logger';
import { config } from '../config/config';

export interface IngestionJobData {
  source: string;
  timestamp: Date;
  priority: 'low' | 'normal' | 'high';
  maxPages?: number;
  filters?: Record<string, any>;
}

export interface EmbeddingJobData {
  jobId: string;
  text: string;
  type: 'job' | 'resume';
}

export class QueueManager {
  private redis: IORedis;
  private jobQueue: Queue<IngestionJobData>;
  private embeddingQueue: Queue<EmbeddingJobData>;

  constructor() {
    // Initialize Redis connection
    this.redis = new IORedis(config.redisUrl, {
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      lazyConnect: true,
    });

    // Initialize queues
    this.jobQueue = new Queue<IngestionJobData>('job-ingestion', {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: config.ingestion.retryAttempts,
        backoff: {
          type: 'exponential',
          delay: config.ingestion.retryDelay,
        },
      },
    });

    this.embeddingQueue = new Queue<EmbeddingJobData>('embedding-generation', {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 200,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.setupEventListeners();
  }

  public async initialize(): Promise<void> {
    try {
      await this.redis.ping();
      logger.info('Redis connection established');
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  private setupEventListeners(): void {
    // Job queue events
    this.jobQueue.on('completed', (job) => {
      logger.info(`Job ${job.id} completed successfully`, {
        source: job.data.source,
        duration: Date.now() - job.timestamp,
      });
    });

    this.jobQueue.on('failed', (job, err) => {
      logger.error(`Job ${job?.id} failed:`, {
        source: job?.data.source,
        error: err.message,
        attempts: job?.attemptsMade,
      });
    });

    this.jobQueue.on('stalled', (jobId) => {
      logger.warn(`Job ${jobId} stalled`);
    });

    // Embedding queue events
    this.embeddingQueue.on('completed', (job) => {
      logger.debug(`Embedding job ${job.id} completed`, {
        jobId: job.data.jobId,
        type: job.data.type,
      });
    });

    this.embeddingQueue.on('failed', (job, err) => {
      logger.error(`Embedding job ${job?.id} failed:`, {
        jobId: job?.data.jobId,
        error: err.message,
      });
    });

    // Redis connection events
    this.redis.on('connect', () => {
      logger.info('Redis connected');
    });

    this.redis.on('error', (error) => {
      logger.error('Redis connection error:', error);
    });

    this.redis.on('close', () => {
      logger.warn('Redis connection closed');
    });
  }

  public async addIngestionJob(data: IngestionJobData): Promise<Job<IngestionJobData>> {
    const priority = this.getPriority(data.priority);
    
    const job = await this.jobQueue.add(
      `ingest-${data.source}`,
      data,
      {
        priority,
        delay: 0,
        jobId: `${data.source}-${Date.now()}`,
      }
    );

    logger.info(`Added ingestion job for ${data.source}`, {
      jobId: job.id,
      priority: data.priority,
    });

    return job;
  }

  public async addEmbeddingJob(data: EmbeddingJobData): Promise<Job<EmbeddingJobData>> {
    const job = await this.embeddingQueue.add(
      'generate-embedding',
      data,
      {
        priority: 5,
        jobId: `embedding-${data.jobId}-${Date.now()}`,
      }
    );

    logger.debug(`Added embedding job for ${data.type}`, {
      jobId: job.id,
      targetId: data.jobId,
    });

    return job;
  }

  public async addBulkEmbeddingJobs(jobs: EmbeddingJobData[]): Promise<Job<EmbeddingJobData>[]> {
    const bulkJobs = jobs.map(data => ({
      name: 'generate-embedding',
      data,
      opts: {
        priority: 5,
        jobId: `embedding-${data.jobId}-${Date.now()}`,
      },
    }));

    const addedJobs = await this.embeddingQueue.addBulk(bulkJobs);
    
    logger.info(`Added ${addedJobs.length} embedding jobs in bulk`);
    
    return addedJobs;
  }

  public async scheduleRecurringIngestion(): Promise<void> {
    const sources = Object.entries(config.sources).filter(([_, config]) => config.enabled);

    for (const [source, sourceConfig] of sources) {
      try {
        // Schedule daily ingestion for each source
        await this.jobQueue.add(
          `scheduled-ingest-${source}`,
          {
            source,
            timestamp: new Date(),
            priority: 'normal' as const,
            maxPages: sourceConfig.maxPages,
          },
          {
            repeat: {
              pattern: '0 2 * * *', // Daily at 2 AM
            },
            jobId: `scheduled-${source}`,
          }
        );

        logger.info(`Scheduled recurring ingestion for ${source}`);
      } catch (error) {
        logger.error(`Failed to schedule ingestion for ${source}:`, error);
      }
    }
  }

  public async getQueueStats(): Promise<{
    jobQueue: any;
    embeddingQueue: any;
  }> {
    const [jobWaiting, jobActive, jobCompleted, jobFailed] = await Promise.all([
      this.jobQueue.getWaiting(),
      this.jobQueue.getActive(),
      this.jobQueue.getCompleted(),
      this.jobQueue.getFailed(),
    ]);

    const [embeddingWaiting, embeddingActive, embeddingCompleted, embeddingFailed] = await Promise.all([
      this.embeddingQueue.getWaiting(),
      this.embeddingQueue.getActive(),
      this.embeddingQueue.getCompleted(),
      this.embeddingQueue.getFailed(),
    ]);

    return {
      jobQueue: {
        waiting: jobWaiting.length,
        active: jobActive.length,
        completed: jobCompleted.length,
        failed: jobFailed.length,
      },
      embeddingQueue: {
        waiting: embeddingWaiting.length,
        active: embeddingActive.length,
        completed: embeddingCompleted.length,
        failed: embeddingFailed.length,
      },
    };
  }

  public async retryFailedJobs(): Promise<number> {
    let retryCount = 0;

    try {
      const failedJobs = await this.jobQueue.getFailed();
      
      for (const job of failedJobs) {
        if (job.attemptsMade < config.ingestion.retryAttempts) {
          await job.retry();
          retryCount++;
        }
      }

      const failedEmbeddingJobs = await this.embeddingQueue.getFailed();
      
      for (const job of failedEmbeddingJobs) {
        if (job.attemptsMade < 3) {
          await job.retry();
          retryCount++;
        }
      }

      logger.info(`Retried ${retryCount} failed jobs`);
    } catch (error) {
      logger.error('Error retrying failed jobs:', error);
    }

    return retryCount;
  }

  public async cleanOldJobs(olderThanDays = 7): Promise<void> {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);

    try {
      await this.jobQueue.clean(cutoffTime, 100, 'completed');
      await this.jobQueue.clean(cutoffTime, 50, 'failed');
      
      await this.embeddingQueue.clean(cutoffTime, 200, 'completed');
      await this.embeddingQueue.clean(cutoffTime, 50, 'failed');

      logger.info(`Cleaned jobs older than ${olderThanDays} days`);
    } catch (error) {
      logger.error('Error cleaning old jobs:', error);
    }
  }

  public async pauseQueue(queueName: 'job' | 'embedding'): Promise<void> {
    const queue = queueName === 'job' ? this.jobQueue : this.embeddingQueue;
    await queue.pause();
    logger.info(`${queueName} queue paused`);
  }

  public async resumeQueue(queueName: 'job' | 'embedding'): Promise<void> {
    const queue = queueName === 'job' ? this.jobQueue : this.embeddingQueue;
    await queue.resume();
    logger.info(`${queueName} queue resumed`);
  }

  public async isHealthy(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    } catch (error) {
      logger.error('Queue health check failed:', error);
      return false;
    }
  }

  public getJobQueue(): Queue<IngestionJobData> {
    return this.jobQueue;
  }

  public getEmbeddingQueue(): Queue<EmbeddingJobData> {
    return this.embeddingQueue;
  }

  public async close(): Promise<void> {
    try {
      await this.jobQueue.close();
      await this.embeddingQueue.close();
      await this.redis.quit();
      logger.info('Queue manager closed successfully');
    } catch (error) {
      logger.error('Error closing queue manager:', error);
      throw error;
    }
  }

  private getPriority(priority: 'low' | 'normal' | 'high'): number {
    switch (priority) {
      case 'high': return 1;
      case 'normal': return 5;
      case 'low': return 10;
      default: return 5;
    }
  }
}