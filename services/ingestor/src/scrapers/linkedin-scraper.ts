import axios from 'axios';
import { logger } from '../utils/logger';
import { config } from '../config/config';
import { JobData } from '../database/connection';
import { BaseScraper } from './base-scraper';

interface LinkedInJob {
  id: string;
  title: string;
  company: string;
  location: string;
  description: string;
  posted_date: string;
  apply_url: string;
  experience_level?: string;
  employment_type?: string;
  salary?: string;
}

export class LinkedInScraper extends BaseScraper {
  protected sourceName = 'linkedin';
  private apiClient: any;
  private proxyUrl?: string;

  public async initialize(): Promise<void> {
    this.proxyUrl = config.brightDataProxyUrl;
    
    // Initialize axios client with proxy if available
    this.apiClient = axios.create({
      timeout: config.ingestion.requestTimeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
      },
      ...(this.proxyUrl && {
        proxy: {
          host: new URL(this.proxyUrl).hostname,
          port: parseInt(new URL(this.proxyUrl).port) || 80,
          protocol: new URL(this.proxyUrl).protocol.replace(':', ''),
        },
      }),
    });

    logger.info('LinkedIn scraper initialized');
  }

  public async cleanup(): Promise<void> {
    // No cleanup needed for API-based scraper
    logger.info('LinkedIn scraper cleaned up');
  }

  public async scrapeJobs(maxPages = config.sources.linkedin.maxPages): Promise<JobData[]> {
    const allJobs: JobData[] = [];
    const searchQueries = this.getSearchQueries();

    for (const query of searchQueries) {
      try {
        logger.info(`Scraping LinkedIn jobs for query: ${query.keywords} in ${query.location}`);
        const jobs = await this.scrapeJobsForQuery(query, maxPages);
        allJobs.push(...jobs);
        
        // Rate limiting
        await this.delay(config.ingestion.rateLimitDelay * 2); // LinkedIn requires more careful rate limiting
      } catch (error) {
        logger.error(`Error scraping LinkedIn for query ${query.keywords}:`, error);
      }
    }

    logger.info(`Total jobs scraped from LinkedIn: ${allJobs.length}`);
    return allJobs;
  }

  private getSearchQueries(): Array<{ keywords: string; location: string }> {
    const keywords = [
      'software engineer',
      'data scientist', 
      'product manager',
      'business analyst',
      'marketing manager',
      'sales representative',
      'finance analyst',
      'hr specialist',
      'devops engineer',
      'frontend developer',
      'backend developer',
      'full stack developer',
    ];

    const locations = [
      'India',
      'Bangalore, India',
      'Mumbai, India',
      'Delhi, India',
      'Hyderabad, India',
      'Pune, India',
      'Chennai, India',
    ];

    const queries: Array<{ keywords: string; location: string }> = [];
    
    for (const keyword of keywords) {
      for (const location of locations) {
        queries.push({ keywords: keyword, location });
      }
    }

    return queries;
  }

  private async scrapeJobsForQuery(
    query: { keywords: string; location: string }, 
    maxPages: number
  ): Promise<JobData[]> {
    const jobs: JobData[] = [];
    let start = 0;
    const count = 25; // LinkedIn typically returns 25 jobs per page

    for (let page = 0; page < maxPages; page++) {
      try {
        logger.debug(`Scraping page ${page + 1} for ${query.keywords} in ${query.location}`);
        
        const pageJobs = await this.fetchJobsPage(query, start, count);
        
        if (pageJobs.length === 0) {
          logger.info(`No more jobs found on page ${page + 1}, stopping pagination`);
          break;
        }

        jobs.push(...pageJobs);
        logger.info(`Found ${pageJobs.length} jobs on page ${page + 1}`);

        start += count;
        
        // Rate limiting between pages
        await this.delay(config.ingestion.rateLimitDelay);

      } catch (error) {
        logger.error(`Error scraping page ${page + 1}:`, error);
        
        // If we hit rate limits or get blocked, wait longer
        if (error.response?.status === 429 || error.response?.status === 403) {
          logger.warn('Rate limited or blocked, waiting longer...');
          await this.delay(10000);
        }
        
        continue;
      }
    }

    return jobs;
  }

  private async fetchJobsPage(
    query: { keywords: string; location: string },
    start: number,
    count: number
  ): Promise<JobData[]> {
    try {
      // Use multiple strategies to fetch LinkedIn jobs
      
      // Strategy 1: Try unofficial LinkedIn Jobs API
      try {
        return await this.fetchViaJobsAPI(query, start, count);
      } catch (error) {
        logger.debug('Jobs API failed, trying alternative method');
      }

      // Strategy 2: Try scraping search results
      try {
        return await this.fetchViaSearchScraping(query, start, count);
      } catch (error) {
        logger.debug('Search scraping failed, trying RSS feeds');
      }

      // Strategy 3: Try RSS feeds for tech jobs
      if (query.keywords.toLowerCase().includes('engineer') || 
          query.keywords.toLowerCase().includes('developer')) {
        return await this.fetchViaRSSFeeds(query);
      }

      return [];

    } catch (error) {
      logger.error('All LinkedIn fetching strategies failed:', error);
      return [];
    }
  }

  private async fetchViaJobsAPI(
    query: { keywords: string; location: string },
    start: number,
    count: number
  ): Promise<JobData[]> {
    // This would use an unofficial LinkedIn Jobs API or a third-party service
    // For production, you'd want to use a legitimate API service
    
    const url = `https://linkedin-jobs-search.p.rapidapi.com/jobs`;
    
    try {
      const response = await this.apiClient.get(url, {
        params: {
          keywords: query.keywords,
          location: query.location,
          start,
          count,
        },
        headers: {
          'X-RapidAPI-Key': process.env.RAPIDAPI_KEY, // You'd need this
          'X-RapidAPI-Host': 'linkedin-jobs-search.p.rapidapi.com',
        },
      });

      return this.parseLinkedInJobs(response.data.jobs || []);
    } catch (error) {
      logger.debug('RapidAPI LinkedIn jobs failed:', error);
      throw error;
    }
  }

  private async fetchViaSearchScraping(
    query: { keywords: string; location: string },
    start: number,
    count: number
  ): Promise<JobData[]> {
    // This would scrape LinkedIn search results
    // Note: LinkedIn actively blocks scrapers, so this is for educational purposes
    
    const searchUrl = `https://www.linkedin.com/jobs/search/`;
    const params = new URLSearchParams({
      keywords: query.keywords,
      location: query.location,
      start: start.toString(),
    });

    try {
      const response = await this.apiClient.get(`${searchUrl}?${params}`);
      
      // Parse HTML response to extract job data
      // This is a simplified example - you'd need proper HTML parsing
      const jobs = this.parseHTMLJobs(response.data);
      
      return jobs;
    } catch (error) {
      logger.debug('LinkedIn search scraping failed:', error);
      throw error;
    }
  }

  private async fetchViaRSSFeeds(query: { keywords: string; location: string }): Promise<JobData[]> {
    // Some companies publish their LinkedIn jobs via RSS
    // This is a fallback method with limited coverage
    
    try {
      const rssFeeds = [
        'https://www.linkedin.com/jobs/rss/jobs?keywords=software%20engineer&location=india',
        'https://www.linkedin.com/jobs/rss/jobs?keywords=data%20scientist&location=india',
      ];

      const jobs: JobData[] = [];
      
      for (const feedUrl of rssFeeds) {
        try {
          const response = await this.apiClient.get(feedUrl);
          const feedJobs = this.parseRSSFeed(response.data);
          jobs.push(...feedJobs);
        } catch (error) {
          logger.debug(`RSS feed failed for ${feedUrl}:`, error);
        }
      }

      return jobs;
    } catch (error) {
      logger.debug('RSS feed fetching failed:', error);
      return [];
    }
  }

  private parseLinkedInJobs(rawJobs: any[]): JobData[] {
    return rawJobs.map((rawJob, index) => {
      try {
        const externalId = `linkedin_${rawJob.id || Date.now()}_${index}`;
        
        return this.normalizeJobData({
          external_id: externalId,
          title: rawJob.title || rawJob.job_title,
          company: rawJob.company || rawJob.company_name,
          location: rawJob.location,
          description: rawJob.description,
          url: rawJob.apply_url || rawJob.job_url,
          posted_at: rawJob.posted_date || rawJob.publication_date,
          employment_type: rawJob.employment_type,
          experience_level: rawJob.experience_level,
          salary: rawJob.salary,
          skills: rawJob.skills || [],
          raw_data: rawJob,
        });
      } catch (error) {
        logger.warn('Error parsing LinkedIn job:', error);
        return null;
      }
    }).filter(Boolean) as JobData[];
  }

  private parseHTMLJobs(html: string): JobData[] {
    // This would use a proper HTML parser like Cheerio
    // Simplified example for demonstration
    
    try {
      const jobs: JobData[] = [];
      
      // Extract job data from HTML using regex (not recommended for production)
      const jobMatches = html.match(/<div[^>]+data-entity-urn="urn:li:fsu_jobPosting:[^"]*"[^>]*>/g) || [];
      
      jobMatches.forEach((match, index) => {
        try {
          // Extract job ID, title, company, etc. from HTML
          // This is a very simplified example
          const jobId = match.match(/data-entity-urn="urn:li:fsu_jobPosting:([^"]*)"/) || ['', `html_${Date.now()}_${index}`];
          
          jobs.push(this.normalizeJobData({
            external_id: `linkedin_html_${jobId[1]}`,
            title: 'Extracted from HTML', // You'd extract this properly
            company: 'Unknown Company',
            location: 'India',
            description: 'Description extracted from HTML',
            url: `https://www.linkedin.com/jobs/view/${jobId[1]}`,
            posted_at: new Date(),
            raw_data: { htmlMatch: match },
          }));
        } catch (error) {
          logger.debug('Error parsing HTML job match:', error);
        }
      });

      return jobs;
    } catch (error) {
      logger.error('Error parsing HTML jobs:', error);
      return [];
    }
  }

  private parseRSSFeed(rssData: string): JobData[] {
    // This would use a proper RSS parser
    // Simplified example for demonstration
    
    try {
      const jobs: JobData[] = [];
      
      // Extract items from RSS feed
      const itemMatches = rssData.match(/<item[^>]*>[\s\S]*?<\/item>/g) || [];
      
      itemMatches.forEach((item, index) => {
        try {
          const title = item.match(/<title[^>]*>([\s\S]*?)<\/title>/)?.[1] || '';
          const link = item.match(/<link[^>]*>([\s\S]*?)<\/link>/)?.[1] || '';
          const description = item.match(/<description[^>]*>([\s\S]*?)<\/description>/)?.[1] || '';
          const pubDate = item.match(/<pubDate[^>]*>([\s\S]*?)<\/pubDate>/)?.[1] || '';

          if (title && link) {
            jobs.push(this.normalizeJobData({
              external_id: `linkedin_rss_${Date.now()}_${index}`,
              title,
              company: 'RSS Company', // Extract from title or description
              location: 'India',
              description,
              url: link,
              posted_at: pubDate ? new Date(pubDate) : new Date(),
              raw_data: { rssItem: item },
            }));
          }
        } catch (error) {
          logger.debug('Error parsing RSS item:', error);
        }
      });

      return jobs;
    } catch (error) {
      logger.error('Error parsing RSS feed:', error);
      return [];
    }
  }

  protected async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}