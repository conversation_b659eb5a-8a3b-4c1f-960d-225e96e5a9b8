import { JobData } from '../database/connection';
import { logger } from '../utils/logger';

export abstract class BaseScraper {
  protected abstract sourceName: string;

  public abstract initialize(): Promise<void>;
  public abstract cleanup(): Promise<void>;
  public abstract scrapeJobs(maxPages?: number): Promise<JobData[]>;

  protected normalizeJobData(rawJob: any): JobData {
    // Normalize the job data to match our database schema
    const normalizedJob: JobData = {
      source: this.sourceName,
      external_id: rawJob.external_id || this.generateExternalId(rawJob),
      url: this.normalizeUrl(rawJob.url),
      title: this.normalizeTitle(rawJob.title),
      company: this.normalizeCompany(rawJob.company),
      location: rawJob.location?.trim(),
      country: rawJob.country || this.inferCountry(rawJob.location),
      is_remote: this.isRemoteJob(rawJob),
      employment_type: this.normalizeEmploymentType(rawJob.employment_type),
      experience_level: this.normalizeExperienceLevel(rawJob.experience_level),
      salary_min: this.parseSalary(rawJob.salary_min),
      salary_max: this.parseSalary(rawJob.salary_max),
      currency: rawJob.currency || 'INR',
      description: this.cleanText(rawJob.description),
      requirements: this.cleanText(rawJob.requirements),
      skills: this.normalizeSkills(rawJob.skills),
      posted_at: this.parseDate(rawJob.posted_at || rawJob.postedDate) || new Date(),
      expires_at: this.parseDate(rawJob.expires_at || rawJob.expiresAt),
      raw_data: rawJob,
    };

    return normalizedJob;
  }

  protected generateExternalId(rawJob: any): string {
    const title = rawJob.title?.replace(/\s+/g, '_').toLowerCase() || 'unknown';
    const company = rawJob.company?.replace(/\s+/g, '_').toLowerCase() || 'unknown';
    const timestamp = Date.now();
    return `${this.sourceName}_${title}_${company}_${timestamp}`;
  }

  protected normalizeUrl(url: string): string {
    if (!url) return '';
    
    // Ensure URL is absolute
    if (url.startsWith('/')) {
      const baseUrls: Record<string, string> = {
        shine: 'https://www.shine.com',
        naukri: 'https://www.naukri.com',
        linkedin: 'https://www.linkedin.com',
      };
      return `${baseUrls[this.sourceName] || ''}${url}`;
    }
    
    return url;
  }

  protected normalizeTitle(title: string): string {
    if (!title) return '';
    
    return title
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s\-\(\)]/g, '')
      .substring(0, 200); // Limit length
  }

  protected normalizeCompany(company: string): string {
    if (!company) return '';
    
    return company
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/\b(Pvt\.?\s*Ltd\.?|Private\s*Limited|Inc\.?|LLC|Corporation|Corp\.?)\b/gi, '')
      .trim()
      .substring(0, 100); // Limit length
  }

  protected inferCountry(location: string): string {
    if (!location) return 'IN';
    
    const locationLower = location.toLowerCase();
    
    // Indian cities
    const indianCities = [
      'mumbai', 'delhi', 'bangalore', 'hyderabad', 'pune', 'chennai', 'kolkata',
      'ahmedabad', 'surat', 'jaipur', 'lucknow', 'kanpur', 'nagpur', 'indore',
      'thane', 'bhopal', 'visakhapatnam', 'pimpri', 'patna', 'vadodara', 'ghaziabad',
      'ludhiana', 'agra', 'nashik', 'faridabad', 'meerut', 'rajkot', 'kalyan',
      'vasai', 'varanasi', 'srinagar', 'aurangabad', 'dhanbad', 'amritsar',
      'navi mumbai', 'allahabad', 'ranchi', 'howrah', 'coimbatore', 'jabalpur',
      'gwalior', 'vijayawada', 'jodhpur', 'madurai', 'raipur', 'kota'
    ];

    if (indianCities.some(city => locationLower.includes(city))) {
      return 'IN';
    }

    // Check for remote indicators
    if (locationLower.includes('remote') || locationLower.includes('work from home')) {
      return 'REMOTE';
    }

    // Default to India for this platform
    return 'IN';
  }

  protected isRemoteJob(rawJob: any): boolean {
    const indicators = [
      rawJob.is_remote,
      rawJob.remote,
      rawJob.location?.toLowerCase().includes('remote'),
      rawJob.location?.toLowerCase().includes('work from home'),
      rawJob.title?.toLowerCase().includes('remote'),
      rawJob.description?.toLowerCase().includes('remote'),
    ];

    return indicators.some(indicator => indicator === true || indicator);
  }

  protected normalizeEmploymentType(type: string): string | undefined {
    if (!type) return undefined;
    
    const typeLower = type.toLowerCase();
    
    if (typeLower.includes('full') || typeLower.includes('permanent')) return 'full-time';
    if (typeLower.includes('part')) return 'part-time';
    if (typeLower.includes('contract') || typeLower.includes('freelance')) return 'contract';
    if (typeLower.includes('intern')) return 'internship';
    if (typeLower.includes('temp')) return 'temporary';
    
    return 'full-time'; // Default
  }

  protected normalizeExperienceLevel(level: string): string | undefined {
    if (!level) return undefined;
    
    const levelLower = level.toLowerCase();
    
    if (levelLower.includes('entry') || levelLower.includes('junior') || levelLower.includes('fresher')) {
      return 'entry';
    }
    if (levelLower.includes('senior') || levelLower.includes('lead')) {
      return 'senior';
    }
    if (levelLower.includes('mid') || levelLower.includes('intermediate')) {
      return 'mid';
    }
    if (levelLower.includes('executive') || levelLower.includes('director') || levelLower.includes('vp')) {
      return 'executive';
    }
    
    return 'mid'; // Default
  }

  protected parseSalary(salary: any): number | undefined {
    if (typeof salary === 'number') return salary;
    if (!salary) return undefined;
    
    const salaryStr = salary.toString().toLowerCase();
    
    // Extract numbers from salary string
    const numbers = salaryStr.match(/[\d,]+(?:\.\d+)?/g);
    if (!numbers) return undefined;
    
    let amount = parseFloat(numbers[0].replace(/,/g, ''));
    
    // Convert based on units
    if (salaryStr.includes('lakh') || salaryStr.includes('l ')) {
      amount *= 100000;
    } else if (salaryStr.includes('crore') || salaryStr.includes('cr')) {
      amount *= 10000000;
    } else if (salaryStr.includes('k') && amount < 1000) {
      amount *= 1000;
    }
    
    return Math.round(amount);
  }

  protected cleanText(text: string): string | undefined {
    if (!text) return undefined;
    
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s\.\,\;\:\!\?\-\(\)]/g, '')
      .substring(0, 5000); // Limit length
  }

  protected normalizeSkills(skills: any): string[] | undefined {
    if (!skills) return undefined;
    
    let skillArray: string[] = [];
    
    if (Array.isArray(skills)) {
      skillArray = skills;
    } else if (typeof skills === 'string') {
      // Split by common delimiters
      skillArray = skills.split(/[,;|\n]/).map(s => s.trim());
    }
    
    // Clean and filter skills
    return skillArray
      .filter(skill => skill.length > 1 && skill.length < 50)
      .map(skill => skill.toLowerCase().trim())
      .filter((skill, index, arr) => arr.indexOf(skill) === index) // Remove duplicates
      .slice(0, 20); // Limit number of skills
  }

  protected parseDate(dateStr: any): Date | undefined {
    if (!dateStr) return undefined;
    
    if (dateStr instanceof Date) return dateStr;
    
    try {
      // Try direct parsing first
      const parsed = new Date(dateStr);
      if (!isNaN(parsed.getTime())) return parsed;
      
      // Handle relative dates like "2 days ago"
      const relativeMatch = dateStr.match(/(\d+)\s+(day|week|month|year)s?\s+ago/i);
      if (relativeMatch) {
        const amount = parseInt(relativeMatch[1]);
        const unit = relativeMatch[2].toLowerCase();
        const now = new Date();
        
        switch (unit) {
          case 'day':
            return new Date(now.getTime() - amount * 24 * 60 * 60 * 1000);
          case 'week':
            return new Date(now.getTime() - amount * 7 * 24 * 60 * 60 * 1000);
          case 'month':
            return new Date(now.getTime() - amount * 30 * 24 * 60 * 60 * 1000);
          case 'year':
            return new Date(now.getTime() - amount * 365 * 24 * 60 * 60 * 1000);
        }
      }
      
      return undefined;
    } catch (error) {
      logger.warn(`Failed to parse date: ${dateStr}`, error);
      return undefined;
    }
  }

  protected async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  protected logProgress(current: number, total: number, operation: string): void {
    if (current % 10 === 0 || current === total) {
      logger.info(`${operation}: ${current}/${total} (${Math.round((current/total) * 100)}%)`);
    }
  }
}