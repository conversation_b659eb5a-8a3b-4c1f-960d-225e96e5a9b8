import axios from 'axios';
import Parser from 'rss-parser';
import { logger } from '../utils/logger';
import { config } from '../config/config';
import { JobData } from '../database/connection';
import { BaseScraper } from './base-scraper';

interface RemoteOKJob {
  id: string;
  slug: string;
  company: string;
  company_logo?: string;
  position: string;
  tags: string[];
  description: string;
  location: string;
  original: boolean;
  url: string;
  apply_url: string;
  date: string;
  salary?: string;
}

interface WeWorkRemotelyItem {
  title: string;
  link: string;
  pubDate: string;
  content: string;
  contentSnippet: string;
  guid: string;
  categories: string[];
}

export class RemoteScraper extends BaseScraper {
  protected sourceName = 'remote';
  private apiClient: any;
  private rssParser: Parser;

  public async initialize(): Promise<void> {
    this.apiClient = axios.create({
      timeout: config.ingestion.requestTimeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
      },
    });

    this.rssParser = new Parser({
      timeout: config.ingestion.requestTimeout,
    });

    logger.info('Remote jobs scraper initialized');
  }

  public async cleanup(): Promise<void> {
    // No cleanup needed for API-based scraper
    logger.info('Remote jobs scraper cleaned up');
  }

  public async scrapeJobs(maxJobs = 1000): Promise<JobData[]> {
    const allJobs: JobData[] = [];

    try {
      // Scrape from RemoteOK
      if (config.sources.remoteOk.enabled) {
        logger.info('Scraping jobs from RemoteOK');
        const remoteOKJobs = await this.scrapeRemoteOK(maxJobs / 2);
        allJobs.push(...remoteOKJobs);
        await this.delay(config.ingestion.rateLimitDelay);
      }

      // Scrape from WeWorkRemotely
      if (config.sources.weWorkRemotely.enabled) {
        logger.info('Scraping jobs from WeWorkRemotely');
        const weworkJobs = await this.scrapeWeWorkRemotely(maxJobs / 2);
        allJobs.push(...weworkJobs);
        await this.delay(config.ingestion.rateLimitDelay);
      }

      // Scrape from AngelList (if available)
      try {
        logger.info('Scraping jobs from AngelList');
        const angelListJobs = await this.scrapeAngelList(200);
        allJobs.push(...angelListJobs);
      } catch (error) {
        logger.warn('AngelList scraping failed:', error);
      }

    } catch (error) {
      logger.error('Error scraping remote jobs:', error);
    }

    logger.info(`Total remote jobs scraped: ${allJobs.length}`);
    return allJobs;
  }

  private async scrapeRemoteOK(maxJobs: number): Promise<JobData[]> {
    try {
      const response = await this.apiClient.get(`${config.sources.remoteOk.apiUrl}`, {
        timeout: 15000,
      });

      if (!response.data || !Array.isArray(response.data)) {
        logger.warn('Invalid response from RemoteOK API');
        return [];
      }

      const jobs = response.data
        .slice(0, maxJobs)
        .map((job: RemoteOKJob, index: number) => this.parseRemoteOKJob(job, index))
        .filter(Boolean) as JobData[];

      logger.info(`Scraped ${jobs.length} jobs from RemoteOK`);
      return jobs;

    } catch (error) {
      logger.error('Error scraping RemoteOK:', error);
      return [];
    }
  }

  private parseRemoteOKJob(job: RemoteOKJob, index: number): JobData | null {
    try {
      const externalId = `remoteok_${job.id || job.slug || Date.now()}_${index}`;
      
      // Parse salary from tags or description
      let salaryMin: number | undefined;
      let salaryMax: number | undefined;
      let currency = 'USD';

      const salaryTag = job.tags?.find(tag => 
        tag.includes('$') || tag.includes('k') || tag.includes('USD')
      );

      if (salaryTag) {
        const salaryMatch = salaryTag.match(/\$?(\d+)k?\s*-\s*\$?(\d+)k?/i);
        if (salaryMatch) {
          salaryMin = parseInt(salaryMatch[1]) * (salaryTag.includes('k') ? 1000 : 1);
          salaryMax = parseInt(salaryMatch[2]) * (salaryTag.includes('k') ? 1000 : 1);
        }
      }

      // Determine experience level from position title or tags
      let experienceLevel: string | undefined;
      const positionLower = job.position.toLowerCase();
      const tagText = job.tags?.join(' ').toLowerCase() || '';

      if (positionLower.includes('senior') || positionLower.includes('lead') || tagText.includes('senior')) {
        experienceLevel = 'senior';
      } else if (positionLower.includes('junior') || positionLower.includes('entry') || tagText.includes('junior')) {
        experienceLevel = 'entry';
      } else {
        experienceLevel = 'mid';
      }

      return this.normalizeJobData({
        external_id: externalId,
        title: job.position,
        company: job.company,
        location: job.location || 'Remote',
        is_remote: true,
        country: 'REMOTE',
        description: job.description,
        salary_min: salaryMin,
        salary_max: salaryMax,
        currency,
        experience_level: experienceLevel,
        employment_type: 'full-time',
        skills: job.tags,
        url: job.url || job.apply_url,
        posted_at: job.date ? new Date(job.date) : new Date(),
        raw_data: job,
      });

    } catch (error) {
      logger.warn('Error parsing RemoteOK job:', error);
      return null;
    }
  }

  private async scrapeWeWorkRemotely(maxJobs: number): Promise<JobData[]> {
    try {
      const feed = await this.rssParser.parseURL(config.sources.weWorkRemotely.rssUrl);
      
      if (!feed.items || feed.items.length === 0) {
        logger.warn('No items found in WeWorkRemotely RSS feed');
        return [];
      }

      const jobs = feed.items
        .slice(0, maxJobs)
        .map((item, index) => this.parseWeWorkRemotelyJob(item as WeWorkRemotelyItem, index))
        .filter(Boolean) as JobData[];

      logger.info(`Scraped ${jobs.length} jobs from WeWorkRemotely`);
      return jobs;

    } catch (error) {
      logger.error('Error scraping WeWorkRemotely:', error);
      return [];
    }
  }

  private parseWeWorkRemotelyJob(item: WeWorkRemotelyItem, index: number): JobData | null {
    try {
      const externalId = `weworkremotely_${item.guid || Date.now()}_${index}`;
      
      // Parse title to extract company and position
      const titleParts = item.title.split(' : ');
      const company = titleParts[0] || 'Unknown Company';
      const position = titleParts[1] || item.title;

      // Extract location from content (if available)
      let location = 'Remote';
      const locationMatch = item.contentSnippet?.match(/Location[:\s]+([^\n]+)/i);
      if (locationMatch) {
        location = locationMatch[1].trim();
      }

      // Determine experience level
      let experienceLevel: string | undefined;
      const titleLower = item.title.toLowerCase();
      if (titleLower.includes('senior') || titleLower.includes('lead')) {
        experienceLevel = 'senior';
      } else if (titleLower.includes('junior') || titleLower.includes('entry')) {
        experienceLevel = 'entry';
      } else {
        experienceLevel = 'mid';
      }

      // Extract skills from categories and content
      const skills = [
        ...(item.categories || []),
        ...this.extractSkillsFromText(item.contentSnippet || ''),
      ].filter(skill => skill.length > 2 && skill.length < 30);

      return this.normalizeJobData({
        external_id: externalId,
        title: position,
        company,
        location,
        is_remote: true,
        country: 'REMOTE',
        description: item.contentSnippet || item.content,
        experience_level: experienceLevel,
        employment_type: 'full-time',
        skills,
        url: item.link,
        posted_at: item.pubDate ? new Date(item.pubDate) : new Date(),
        raw_data: item,
      });

    } catch (error) {
      logger.warn('Error parsing WeWorkRemotely job:', error);
      return null;
    }
  }

  private async scrapeAngelList(maxJobs: number): Promise<JobData[]> {
    try {
      // AngelList requires authentication and has strict rate limits
      // This is a simplified example - you'd need proper API access
      
      const searchQueries = [
        'software engineer',
        'data scientist',
        'product manager',
        'frontend developer',
        'backend developer',
      ];

      const allJobs: JobData[] = [];

      for (const query of searchQueries) {
        try {
          // This would be the actual AngelList API call
          const jobs = await this.fetchAngelListJobs(query, maxJobs / searchQueries.length);
          allJobs.push(...jobs);
          
          await this.delay(2000); // Rate limiting
        } catch (error) {
          logger.debug(`Error fetching AngelList jobs for ${query}:`, error);
        }
      }

      logger.info(`Scraped ${allJobs.length} jobs from AngelList`);
      return allJobs;

    } catch (error) {
      logger.error('Error scraping AngelList:', error);
      return [];
    }
  }

  private async fetchAngelListJobs(query: string, limit: number): Promise<JobData[]> {
    // This would implement actual AngelList API integration
    // For now, return empty array as AngelList requires special access
    
    try {
      // Example API call (would need proper authentication)
      const response = await this.apiClient.get('https://api.angel.co/1/jobs', {
        params: {
          query,
          limit,
          remote: true,
        },
        headers: {
          'Authorization': `Bearer ${process.env.ANGELLIST_API_KEY}`,
        },
      });

      return (response.data.jobs || []).map((job: any, index: number) => {
        return this.normalizeJobData({
          external_id: `angellist_${job.id}_${index}`,
          title: job.title,
          company: job.startup?.name || 'Unknown',
          location: 'Remote',
          is_remote: true,
          country: 'REMOTE',
          description: job.description,
          salary_min: job.salary_min,
          salary_max: job.salary_max,
          currency: job.currency || 'USD',
          experience_level: job.experience_level,
          employment_type: job.job_type,
          skills: job.skills || [],
          url: job.angellist_url,
          posted_at: job.created_at ? new Date(job.created_at) : new Date(),
          raw_data: job,
        });
      });

    } catch (error) {
      // AngelList API not available or authentication failed
      logger.debug('AngelList API not available');
      return [];
    }
  }

  private extractSkillsFromText(text: string): string[] {
    const commonSkills = [
      'javascript', 'typescript', 'python', 'java', 'react', 'node.js', 'angular', 'vue.js',
      'aws', 'docker', 'kubernetes', 'sql', 'mongodb', 'postgresql', 'redis', 'graphql',
      'rest api', 'microservices', 'devops', 'ci/cd', 'git', 'agile', 'scrum',
      'machine learning', 'data science', 'artificial intelligence', 'blockchain',
      'go', 'rust', 'php', 'ruby', 'c++', 'c#', '.net', 'scala', 'kotlin',
    ];

    const skills: string[] = [];
    const textLower = text.toLowerCase();

    for (const skill of commonSkills) {
      if (textLower.includes(skill.toLowerCase())) {
        skills.push(skill);
      }
    }

    return skills;
  }

  protected async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}