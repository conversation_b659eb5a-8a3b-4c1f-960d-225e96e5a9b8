import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { logger } from '../utils/logger';
import { config } from '../config/config';
import { JobData } from '../database/connection';
import { BaseScraper } from './base-scraper';

export class <PERSON><PERSON><PERSON>Scraper extends BaseScraper {
  protected sourceName = 'naukri';
  private browser: Browser | null = null;

  public async initialize(): Promise<void> {
    this.browser = await chromium.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
      ],
    });
    logger.info('Naukri scraper initialized');
  }

  public async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
    logger.info('Naukri scraper cleaned up');
  }

  public async scrapeJobs(maxPages = config.sources.naukri.maxPages): Promise<JobData[]> {
    if (!this.browser) {
      throw new Error('Scraper not initialized');
    }

    const allJobs: JobData[] = [];
    const searchUrls = this.getSearchUrls();

    for (const searchUrl of searchUrls) {
      try {
        logger.info(`Scraping Naukri jobs from: ${searchUrl}`);
        const jobs = await this.scrapeJobsFromUrl(searchUrl, maxPages);
        allJobs.push(...jobs);
        
        // Rate limiting
        await this.delay(config.ingestion.rateLimitDelay);
      } catch (error) {
        logger.error(`Error scraping ${searchUrl}:`, error);
      }
    }

    logger.info(`Total jobs scraped from Naukri: ${allJobs.length}`);
    return allJobs;
  }

  private getSearchUrls(): string[] {
    const baseUrl = config.sources.naukri.baseUrl;
    const searchQueries = [
      'software-engineer-jobs',
      'data-scientist-jobs',
      'product-manager-jobs',
      'business-analyst-jobs',
      'marketing-jobs',
      'sales-jobs',
      'finance-jobs',
      'hr-jobs',
    ];

    const locations = [
      'bangalore',
      'mumbai',
      'delhi-ncr',
      'hyderabad',
      'pune',
      'chennai',
      'kolkata',
      'ahmedabad',
    ];

    const urls: string[] = [];
    
    for (const query of searchQueries) {
      for (const location of locations) {
        urls.push(`${baseUrl}/${query}-in-${location}`);
      }
    }

    return urls;
  }

  private async scrapeJobsFromUrl(searchUrl: string, maxPages: number): Promise<JobData[]> {
    const page = await this.browser!.newPage();
    const jobs: JobData[] = [];

    try {
      // Set user agent and viewport
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      await page.setViewportSize({ width: 1366, height: 768 });

      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        const pageUrl = pageNum === 1 ? searchUrl : `${searchUrl}?k=${pageNum}`;
        
        try {
          logger.debug(`Scraping page ${pageNum}: ${pageUrl}`);
          
          await page.goto(pageUrl, { 
            waitUntil: 'networkidle',
            timeout: config.ingestion.requestTimeout,
          });

          // Wait for job listings to load
          await page.waitForSelector('.jobTuple, .job-tuple, [data-job-id]', { 
            timeout: 10000,
          }).catch(() => {
            logger.warn(`No job cards found on page ${pageNum}`);
          });

          // Extract job listings
          const pageJobs = await this.extractJobsFromPage(page);
          
          if (pageJobs.length === 0) {
            logger.info(`No more jobs found on page ${pageNum}, stopping pagination`);
            break;
          }

          jobs.push(...pageJobs);
          logger.info(`Found ${pageJobs.length} jobs on page ${pageNum}`);

          // Rate limiting between pages
          await this.delay(config.ingestion.rateLimitDelay);

        } catch (error) {
          logger.error(`Error scraping page ${pageNum}:`, error);
          continue;
        }
      }

    } finally {
      await page.close();
    }

    return jobs;
  }

  private async extractJobsFromPage(page: Page): Promise<JobData[]> {
    return await page.evaluate(() => {
      const jobCards = document.querySelectorAll('.jobTuple, .job-tuple, [data-job-id]');
      const jobs: any[] = [];

      jobCards.forEach((card, index) => {
        try {
          // Extract basic job information
          const titleElement = card.querySelector('.title a, .job-title a, [data-testid="job-title"] a, .jobTitle a');
          const companyElement = card.querySelector('.companyInfo .subTitle, .company-name, [data-testid="company-name"], .companyName');
          const locationElement = card.querySelector('.locationsContainer .location, .job-location, [data-testid="location"], .location');
          const experienceElement = card.querySelector('.experience .expwdth, .experience, [data-testid="experience"], .exp');
          const salaryElement = card.querySelector('.salary .rupee, .salary, [data-testid="salary"], .package');
          const skillsElements = card.querySelectorAll('.tags .tag, .skill-tag, [data-testid="skill"], .skill');

          if (!titleElement || !companyElement) {
            return; // Skip invalid job cards
          }

          const title = titleElement.textContent?.trim() || '';
          const company = companyElement.textContent?.trim() || '';
          const location = locationElement?.textContent?.trim() || '';
          const experience = experienceElement?.textContent?.trim() || '';
          const salary = salaryElement?.textContent?.trim() || '';
          const jobUrl = titleElement.getAttribute('href') || '';
          
          // Extract skills
          const skills: string[] = [];
          skillsElements.forEach(skill => {
            const skillText = skill.textContent?.trim();
            if (skillText && !skillText.includes('+')) { // Filter out "+X more" type text
              skills.push(skillText);
            }
          });

          // Generate unique external ID
          const externalId = `naukri_${title.replace(/\s+/g, '_').toLowerCase()}_${company.replace(/\s+/g, '_').toLowerCase()}_${index}`;

          // Parse salary
          let salaryMin: number | undefined;
          let salaryMax: number | undefined;
          let currency = 'INR';

          if (salary) {
            const salaryMatch = salary.match(/(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)\s*(lakh|L|LPA|lakhs?)/i);
            if (salaryMatch) {
              salaryMin = parseFloat(salaryMatch[1]) * 100000;
              salaryMax = parseFloat(salaryMatch[2]) * 100000;
            } else {
              // Try to extract single salary value
              const singleMatch = salary.match(/(\d+(?:\.\d+)?)\s*(lakh|L|LPA)/i);
              if (singleMatch) {
                salaryMin = parseFloat(singleMatch[1]) * 100000;
              }
            }
          }

          // Determine experience level
          let experienceLevel: string | undefined;
          if (experience) {
            const expMatch = experience.match(/(\d+)/);
            if (expMatch) {
              const years = parseInt(expMatch[1]);
              if (years <= 2) experienceLevel = 'entry';
              else if (years <= 5) experienceLevel = 'mid';
              else if (years <= 10) experienceLevel = 'senior';
              else experienceLevel = 'executive';
            }
          }

          jobs.push({
            external_id: externalId,
            title,
            company,
            location,
            experience_level: experienceLevel,
            salary_min: salaryMin,
            salary_max: salaryMax,
            currency,
            url: jobUrl.startsWith('http') ? jobUrl : `https://www.naukri.com${jobUrl}`,
            skills,
            raw_data: {
              originalSalary: salary,
              originalExperience: experience,
              extractedAt: new Date().toISOString(),
            },
          });
        } catch (error) {
          console.error('Error extracting job from card:', error);
        }
      });

      return jobs;
    });
  }

  public async scrapeJobDetails(jobUrl: string): Promise<Partial<JobData>> {
    if (!this.browser) {
      throw new Error('Scraper not initialized');
    }

    const page = await this.browser.newPage();
    
    try {
      await page.goto(jobUrl, { 
        waitUntil: 'networkidle',
        timeout: config.ingestion.requestTimeout,
      });

      // Wait for job details to load
      await page.waitForSelector('.jobdescription, .job-description, [data-testid="job-description"]', { 
        timeout: 10000,
      }).catch(() => {
        logger.warn(`Job details not found for URL: ${jobUrl}`);
      });

      const jobDetails = await page.evaluate(() => {
        const descriptionElement = document.querySelector('.jobdescription .desc, .job-description, [data-testid="job-description"]');
        const keySkillsElement = document.querySelector('.keySkills, .key-skills, [data-testid="key-skills"]');
        const postedDateElement = document.querySelector('.postedOn, .posted-date, [data-testid="posted-date"]');

        // Extract key skills
        const skillElements = keySkillsElement?.querySelectorAll('.tag, .skill') || [];
        const skills: string[] = [];
        skillElements.forEach(skill => {
          const skillText = skill.textContent?.trim();
          if (skillText) skills.push(skillText);
        });

        return {
          description: descriptionElement?.textContent?.trim() || '',
          skills: skills,
          postedDate: postedDateElement?.textContent?.trim() || '',
        };
      });

      // Parse posted date
      let postedAt = new Date();
      if (jobDetails.postedDate) {
        const dateMatch = jobDetails.postedDate.match(/(\d+)\s+(day|week|month)s?\s+ago/i);
        if (dateMatch) {
          const amount = parseInt(dateMatch[1]);
          const unit = dateMatch[2].toLowerCase();
          
          switch (unit) {
            case 'day':
              postedAt = new Date(Date.now() - amount * 24 * 60 * 60 * 1000);
              break;
            case 'week':
              postedAt = new Date(Date.now() - amount * 7 * 24 * 60 * 60 * 1000);
              break;
            case 'month':
              postedAt = new Date(Date.now() - amount * 30 * 24 * 60 * 60 * 1000);
              break;
          }
        }
      }

      return {
        description: jobDetails.description,
        skills: jobDetails.skills,
        posted_at: postedAt,
      };

    } finally {
      await page.close();
    }
  }
}