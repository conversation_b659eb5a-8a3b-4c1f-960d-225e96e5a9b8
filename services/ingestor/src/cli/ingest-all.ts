#!/usr/bin/env node

import { logger } from '../utils/logger';
import { Database } from '../database/connection';
import { QueueManager } from '../queue/queue-manager';
import { JobProcessor } from '../processors/job-processor';
import { config } from '../config/config';

async function main() {
  logger.info('Starting batch ingestion for all sources');
  
  const database = new Database();
  const queueManager = new QueueManager();
  const jobProcessor = new JobProcessor(database, queueManager);

  try {
    // Initialize connections
    await database.connect();
    await queueManager.initialize();
    await jobProcessor.start();

    // Get enabled sources
    const enabledSources = Object.entries(config.sources)
      .filter(([_, sourceConfig]) => sourceConfig.enabled)
      .map(([source, _]) => source);

    if (enabledSources.length === 0) {
      logger.warn('No sources enabled for ingestion');
      process.exit(0);
    }

    logger.info(`Starting ingestion for sources: ${enabledSources.join(', ')}`);

    const results = [];

    // Trigger ingestion for each source
    for (const source of enabledSources) {
      try {
        logger.info(`Starting ingestion for ${source}`);
        const jobId = await jobProcessor.triggerIngestion(source, 'high');
        logger.info(`Ingestion job ${jobId} queued for ${source}`);
        results.push({ source, jobId, status: 'queued' });
      } catch (error) {
        logger.error(`Failed to queue ingestion for ${source}:`, error);
        results.push({ source, jobId: null, status: 'failed', error: error.message });
      }
    }

    // Wait a bit for jobs to start processing
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Check queue status
    const queueStats = await queueManager.getQueueStats();
    logger.info('Queue status after triggering ingestion:', queueStats);

    // Trigger embedding generation for jobs without embeddings
    const embeddingJobsCount = await jobProcessor.triggerEmbeddingGeneration();
    logger.info(`Queued ${embeddingJobsCount} embedding generation jobs`);

    // Print summary
    logger.info('Ingestion summary:', {
      totalSources: enabledSources.length,
      queued: results.filter(r => r.status === 'queued').length,
      failed: results.filter(r => r.status === 'failed').length,
      embeddingJobs: embeddingJobsCount,
      results,
    });

    logger.info('Batch ingestion completed successfully');

  } catch (error) {
    logger.error('Batch ingestion failed:', error);
    process.exit(1);
  } finally {
    // Cleanup
    await jobProcessor.stop();
    await queueManager.close();
    await database.disconnect();
  }
}

// Handle CLI arguments
const args = process.argv.slice(2);
const options = {
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  sources: args.find(arg => arg.startsWith('--sources='))?.split('=')[1]?.split(','),
};

if (options.verbose) {
  logger.level = 'debug';
}

if (options.dryRun) {
  logger.info('Dry run mode - no actual ingestion will occur');
}

if (options.sources) {
  logger.info(`Limiting ingestion to sources: ${options.sources.join(', ')}`);
  // Override config to only enable specified sources
  Object.keys(config.sources).forEach(source => {
    config.sources[source as keyof typeof config.sources].enabled = 
      options.sources!.includes(source);
  });
}

// Run the main function
main().catch((error) => {
  logger.error('Fatal error in batch ingestion:', error);
  process.exit(1);
});