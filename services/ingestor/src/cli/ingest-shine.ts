#!/usr/bin/env node

import { logger } from '../utils/logger';
import { Database } from '../database/connection';
import { QueueManager } from '../queue/queue-manager';
import { JobProcessor } from '../processors/job-processor';

async function main() {
  logger.info('Starting Shine.com job ingestion');
  
  const database = new Database();
  const queueManager = new QueueManager();
  const jobProcessor = new JobProcessor(database, queueManager);

  try {
    // Initialize connections
    await database.connect();
    await queueManager.initialize();
    await jobProcessor.start();

    // Trigger Shine ingestion
    const jobId = await jobProcessor.triggerIngestion('shine', 'high');
    logger.info(`Shine ingestion job ${jobId} queued successfully`);

    // Wait for job to complete or timeout
    const timeout = 10 * 60 * 1000; // 10 minutes
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const stats = await queueManager.getQueueStats();
      
      if (stats.jobQueue.active === 0 && stats.jobQueue.waiting === 0) {
        logger.info('Shine ingestion completed');
        break;
      }
      
      logger.info(`Job queue status: ${stats.jobQueue.active} active, ${stats.jobQueue.waiting} waiting`);
      await new Promise(resolve => setTimeout(resolve, 30000)); // Check every 30 seconds
    }

    // Get final stats
    const finalStats = await database.getIngestionStats();
    logger.info('Final ingestion stats:', finalStats);

  } catch (error) {
    logger.error('Shine ingestion failed:', error);
    process.exit(1);
  } finally {
    await jobProcessor.stop();
    await queueManager.close();
    await database.disconnect();
  }
}

main().catch((error) => {
  logger.error('Fatal error in Shine ingestion:', error);
  process.exit(1);
});