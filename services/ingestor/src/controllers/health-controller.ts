import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { Database } from '../database/connection';
import { QueueManager } from '../queue/queue-manager';

export class HealthController {
  constructor(
    private database: Database,
    private queueManager: QueueManager
  ) {}

  public async health(req: Request, res: Response): Promise<void> {
    try {
      const healthStatus = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'ingestor',
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
      };

      res.status(200).json(healthStatus);
    } catch (error) {
      logger.error('Health check failed:', error);
      res.status(500).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Internal server error',
      });
    }
  }

  public async readiness(req: Request, res: Response): Promise<void> {
    try {
      const checks = await this.performReadinessChecks();
      const isReady = Object.values(checks).every(check => check.status === 'healthy');

      res.status(isReady ? 200 : 503).json({
        status: isReady ? 'ready' : 'not ready',
        timestamp: new Date().toISOString(),
        checks,
      });
    } catch (error) {
      logger.error('Readiness check failed:', error);
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: 'Readiness check failed',
      });
    }
  }

  private async performReadinessChecks(): Promise<Record<string, any>> {
    const checks: Record<string, any> = {};

    // Database check
    try {
      const dbHealthy = await this.database.healthCheck();
      checks.database = {
        status: dbHealthy ? 'healthy' : 'unhealthy',
        message: dbHealthy ? 'Database connection successful' : 'Database connection failed',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      checks.database = {
        status: 'unhealthy',
        message: 'Database check failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }

    // Queue manager check
    try {
      const queueHealthy = await this.queueManager.isHealthy();
      checks.queue = {
        status: queueHealthy ? 'healthy' : 'unhealthy',
        message: queueHealthy ? 'Queue connection successful' : 'Queue connection failed',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      checks.queue = {
        status: 'unhealthy',
        message: 'Queue check failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }

    // Memory check
    const memUsage = process.memoryUsage();
    const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    checks.memory = {
      status: memUsagePercent < 90 ? 'healthy' : 'unhealthy',
      message: `Memory usage: ${memUsagePercent.toFixed(2)}%`,
      details: {
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
        rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
      },
      timestamp: new Date().toISOString(),
    };

    // Disk space check (simplified)
    try {
      const stats = require('fs').statSync('/tmp');
      checks.disk = {
        status: 'healthy',
        message: 'Disk space check passed',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      checks.disk = {
        status: 'unhealthy',
        message: 'Disk space check failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      };
    }

    // Environment variables check
    const requiredEnvVars = [
      'DATABASE_URL',
      'REDIS_URL',
      'OPENAI_API_KEY',
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    checks.environment = {
      status: missingEnvVars.length === 0 ? 'healthy' : 'unhealthy',
      message: missingEnvVars.length === 0 
        ? 'All required environment variables are set'
        : `Missing environment variables: ${missingEnvVars.join(', ')}`,
      timestamp: new Date().toISOString(),
    };

    return checks;
  }
}