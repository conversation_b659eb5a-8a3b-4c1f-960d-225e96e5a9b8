import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { Database } from '../database/connection';
import { QueueManager } from '../queue/queue-manager';

export class MetricsController {
  constructor(
    private database: Database,
    private queueManager: QueueManager
  ) {}

  public async getMetrics(req: Request, res: Response): Promise<void> {
    try {
      const metrics = await this.collectMetrics();
      
      // Return metrics in Prometheus format if requested
      const format = req.headers.accept?.includes('text/plain') ? 'prometheus' : 'json';
      
      if (format === 'prometheus') {
        res.setHeader('Content-Type', 'text/plain; charset=utf-8');
        res.send(this.formatPrometheusMetrics(metrics));
      } else {
        res.json(metrics);
      }
    } catch (error) {
      logger.error('Error collecting metrics:', error);
      res.status(500).json({
        error: 'Failed to collect metrics',
        timestamp: new Date().toISOString(),
      });
    }
  }

  private async collectMetrics(): Promise<any> {
    const timestamp = Date.now();
    
    // Collect various metrics
    const [
      ingestionStats,
      queueStats,
      systemMetrics,
      performanceMetrics,
    ] = await Promise.all([
      this.getIngestionMetrics(),
      this.getQueueMetrics(),
      this.getSystemMetrics(),
      this.getPerformanceMetrics(),
    ]);

    return {
      timestamp,
      service: 'ingestor',
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      ingestion: ingestionStats,
      queues: queueStats,
      system: systemMetrics,
      performance: performanceMetrics,
    };
  }

  private async getIngestionMetrics(): Promise<any> {
    try {
      const stats = await this.database.getIngestionStats();
      
      return {
        total_jobs: stats.totalJobs,
        jobs_today: stats.jobsToday,
        average_jobs_per_hour: stats.averageJobsPerHour,
        last_ingestion: stats.lastIngestion,
        jobs_by_source: stats.jobsBySource,
        sources_enabled: Object.keys(stats.jobsBySource).length,
      };
    } catch (error) {
      logger.error('Error collecting ingestion metrics:', error);
      return {
        total_jobs: 0,
        jobs_today: 0,
        average_jobs_per_hour: 0,
        last_ingestion: null,
        jobs_by_source: {},
        sources_enabled: 0,
      };
    }
  }

  private async getQueueMetrics(): Promise<any> {
    try {
      const stats = await this.queueManager.getQueueStats();
      
      return {
        job_queue: {
          waiting: stats.jobQueue.waiting,
          active: stats.jobQueue.active,
          completed: stats.jobQueue.completed,
          failed: stats.jobQueue.failed,
          total: stats.jobQueue.waiting + stats.jobQueue.active + stats.jobQueue.completed + stats.jobQueue.failed,
        },
        embedding_queue: {
          waiting: stats.embeddingQueue.waiting,
          active: stats.embeddingQueue.active,
          completed: stats.embeddingQueue.completed,
          failed: stats.embeddingQueue.failed,
          total: stats.embeddingQueue.waiting + stats.embeddingQueue.active + stats.embeddingQueue.completed + stats.embeddingQueue.failed,
        },
      };
    } catch (error) {
      logger.error('Error collecting queue metrics:', error);
      return {
        job_queue: { waiting: 0, active: 0, completed: 0, failed: 0, total: 0 },
        embedding_queue: { waiting: 0, active: 0, completed: 0, failed: 0, total: 0 },
      };
    }
  }

  private getSystemMetrics(): any {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      memory: {
        heap_used: memUsage.heapUsed,
        heap_total: memUsage.heapTotal,
        heap_used_mb: Math.round(memUsage.heapUsed / 1024 / 1024),
        heap_total_mb: Math.round(memUsage.heapTotal / 1024 / 1024),
        heap_usage_percent: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
        external: memUsage.external,
        rss: memUsage.rss,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        total: cpuUsage.user + cpuUsage.system,
      },
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        node_version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };
  }

  private async getPerformanceMetrics(): Promise<any> {
    try {
      // Get jobs without embeddings as a performance indicator
      const jobsWithoutEmbeddings = await this.database.getJobsWithoutEmbeddings(1);
      const hasJobsWithoutEmbeddings = jobsWithoutEmbeddings.length > 0;

      return {
        embedding_backlog: hasJobsWithoutEmbeddings ? '100+' : '0',
        database_connection_healthy: this.database.isHealthy(),
        queue_connection_healthy: await this.queueManager.isHealthy(),
        last_check: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Error collecting performance metrics:', error);
      return {
        embedding_backlog: 'unknown',
        database_connection_healthy: false,
        queue_connection_healthy: false,
        last_check: new Date().toISOString(),
      };
    }
  }

  private formatPrometheusMetrics(metrics: any): string {
    const lines: string[] = [];
    
    // Helper function to add metric
    const addMetric = (name: string, value: number | string, labels: Record<string, string> = {}, help?: string) => {
      if (help) {
        lines.push(`# HELP ${name} ${help}`);
        lines.push(`# TYPE ${name} gauge`);
      }
      
      const labelStr = Object.entries(labels)
        .map(([key, val]) => `${key}="${val}"`)
        .join(',');
      
      const labelsFormatted = labelStr ? `{${labelStr}}` : '';
      lines.push(`${name}${labelsFormatted} ${value}`);
    };

    // Service info
    addMetric('ingestor_info', 1, {
      version: metrics.version,
      service: metrics.service,
    }, 'Information about the ingestor service');

    // Uptime
    addMetric('ingestor_uptime_seconds', metrics.uptime, {}, 'Service uptime in seconds');

    // Ingestion metrics
    addMetric('ingestor_total_jobs', metrics.ingestion.total_jobs, {}, 'Total number of jobs in database');
    addMetric('ingestor_jobs_today', metrics.ingestion.jobs_today, {}, 'Number of jobs ingested today');
    addMetric('ingestor_jobs_per_hour_avg', metrics.ingestion.average_jobs_per_hour, {}, 'Average jobs ingested per hour');

    // Jobs by source
    Object.entries(metrics.ingestion.jobs_by_source).forEach(([source, count]) => {
      addMetric('ingestor_jobs_by_source', count as number, { source }, 'Number of jobs by source');
    });

    // Queue metrics
    addMetric('ingestor_queue_waiting', metrics.queues.job_queue.waiting, { queue: 'job' }, 'Number of waiting jobs in queue');
    addMetric('ingestor_queue_active', metrics.queues.job_queue.active, { queue: 'job' }, 'Number of active jobs in queue');
    addMetric('ingestor_queue_completed', metrics.queues.job_queue.completed, { queue: 'job' }, 'Number of completed jobs in queue');
    addMetric('ingestor_queue_failed', metrics.queues.job_queue.failed, { queue: 'job' }, 'Number of failed jobs in queue');

    addMetric('ingestor_queue_waiting', metrics.queues.embedding_queue.waiting, { queue: 'embedding' });
    addMetric('ingestor_queue_active', metrics.queues.embedding_queue.active, { queue: 'embedding' });
    addMetric('ingestor_queue_completed', metrics.queues.embedding_queue.completed, { queue: 'embedding' });
    addMetric('ingestor_queue_failed', metrics.queues.embedding_queue.failed, { queue: 'embedding' });

    // Memory metrics
    addMetric('ingestor_memory_heap_used_bytes', metrics.system.memory.heap_used, {}, 'Heap memory used in bytes');
    addMetric('ingestor_memory_heap_total_bytes', metrics.system.memory.heap_total, {}, 'Total heap memory in bytes');
    addMetric('ingestor_memory_heap_usage_percent', metrics.system.memory.heap_usage_percent, {}, 'Heap memory usage percentage');
    addMetric('ingestor_memory_rss_bytes', metrics.system.memory.rss, {}, 'Resident Set Size in bytes');
    addMetric('ingestor_memory_external_bytes', metrics.system.memory.external, {}, 'External memory in bytes');

    // CPU metrics
    addMetric('ingestor_cpu_user_microseconds', metrics.system.cpu.user, {}, 'CPU user time in microseconds');
    addMetric('ingestor_cpu_system_microseconds', metrics.system.cpu.system, {}, 'CPU system time in microseconds');

    // Performance metrics
    addMetric('ingestor_database_healthy', metrics.performance.database_connection_healthy ? 1 : 0, {}, 'Database connection health (1=healthy, 0=unhealthy)');
    addMetric('ingestor_queue_healthy', metrics.performance.queue_connection_healthy ? 1 : 0, {}, 'Queue connection health (1=healthy, 0=unhealthy)');

    return lines.join('\n') + '\n';
  }
}