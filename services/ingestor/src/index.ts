import express from 'express';
import helmet from 'helmet';
import compression from 'compression';
import cors from 'cors';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';

import { config } from './config/config';
import { logger } from './utils/logger';
import { Database } from './database/connection';
import { QueueManager } from './queue/queue-manager';
import { JobProcessor } from './processors/job-processor';
import { HealthController } from './controllers/health-controller';
import { MetricsController } from './controllers/metrics-controller';
import { setupGracefulShutdown } from './utils/graceful-shutdown';

class IngestorService {
  private app: express.Application;
  private database: Database;
  private queueManager: QueueManager;
  private jobProcessor: JobProcessor;

  constructor() {
    this.app = express();
    this.database = new Database();
    this.queueManager = new QueueManager();
    this.jobProcessor = new JobProcessor(this.database, this.queueManager);
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // Compression and CORS
    this.app.use(compression());
    this.app.use(cors({
      origin: config.cors.allowedOrigins,
      credentials: true,
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      next();
    });
  }

  private setupRoutes(): void {
    const healthController = new HealthController(this.database, this.queueManager);
    const metricsController = new MetricsController(this.database, this.queueManager);

    // Health endpoints
    this.app.get('/health', healthController.health.bind(healthController));
    this.app.get('/ready', healthController.readiness.bind(healthController));

    // Metrics endpoint
    this.app.get('/metrics', metricsController.getMetrics.bind(metricsController));

    // Queue dashboard
    const serverAdapter = new ExpressAdapter();
    serverAdapter.setBasePath('/admin/queues');

    createBullBoard({
      queues: [
        new BullMQAdapter(this.queueManager.getJobQueue()),
        new BullMQAdapter(this.queueManager.getEmbeddingQueue()),
      ],
      serverAdapter,
    });

    this.app.use('/admin/queues', serverAdapter.getRouter());

    // API routes
    this.app.post('/api/ingest/trigger', this.triggerIngestion.bind(this));
    this.app.get('/api/stats', this.getStats.bind(this));

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({ error: 'Not found' });
    });

    // Error handler
    this.app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error:', err);
      res.status(500).json({ error: 'Internal server error' });
    });
  }

  private async triggerIngestion(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { source, priority = 'normal' } = req.body;

      if (!source || !['shine', 'naukri', 'linkedin', 'remote'].includes(source)) {
        res.status(400).json({ error: 'Invalid source' });
        return;
      }

      const job = await this.queueManager.addIngestionJob({
        source,
        timestamp: new Date(),
        priority: priority as 'low' | 'normal' | 'high',
      });

      res.json({
        success: true,
        jobId: job.id,
        message: `Ingestion job for ${source} queued successfully`,
      });
    } catch (error) {
      logger.error('Error triggering ingestion:', error);
      res.status(500).json({ error: 'Failed to trigger ingestion' });
    }
  }

  private async getStats(req: express.Request, res: express.Response): Promise<void> {
    try {
      const stats = await this.database.getIngestionStats();
      res.json(stats);
    } catch (error) {
      logger.error('Error getting stats:', error);
      res.status(500).json({ error: 'Failed to get stats' });
    }
  }

  public async start(): Promise<void> {
    try {
      // Initialize database connection
      await this.database.connect();
      logger.info('Database connected successfully');

      // Initialize queue manager
      await this.queueManager.initialize();
      logger.info('Queue manager initialized');

      // Start job processor
      await this.jobProcessor.start();
      logger.info('Job processor started');

      // Setup Express middleware and routes
      this.setupMiddleware();
      this.setupRoutes();

      // Start HTTP server
      const server = this.app.listen(config.port, () => {
        logger.info(`Ingestor service started on port ${config.port}`);
      });

      // Setup graceful shutdown
      setupGracefulShutdown(server, async () => {
        logger.info('Shutting down ingestor service...');
        
        await this.jobProcessor.stop();
        await this.queueManager.close();
        await this.database.disconnect();
        
        logger.info('Ingestor service stopped gracefully');
      });

    } catch (error) {
      logger.error('Failed to start ingestor service:', error);
      process.exit(1);
    }
  }
}

// Start the service
const service = new IngestorService();
service.start().catch((error) => {
  logger.error('Fatal error starting service:', error);
  process.exit(1);
});