import { Worker, Job } from 'bullmq';
import { logger } from '../utils/logger';
import { config } from '../config/config';
import { Database } from '../database/connection';
import { QueueManager, IngestionJobData, EmbeddingJobData } from '../queue/queue-manager';
import { ShineScraper } from '../scrapers/shine-scraper';
import { NaukriScraper } from '../scrapers/naukri-scraper';
import { LinkedInScraper } from '../scrapers/linkedin-scraper';
import { RemoteScraper } from '../scrapers/remote-scraper';
import { EmbeddingService } from '../services/embedding-service';

export class JobProcessor {
  private database: Database;
  private queueManager: QueueManager;
  private embeddingService: EmbeddingService;
  private jobWorker: Worker<IngestionJobData>;
  private embeddingWorker: Worker<EmbeddingJobData>;
  private scrapers: Map<string, any> = new Map();

  constructor(database: Database, queueManager: QueueManager) {
    this.database = database;
    this.queueManager = queueManager;
    this.embeddingService = new EmbeddingService();

    // Initialize scrapers
    this.initializeScrapers();

    // Initialize workers
    this.jobWorker = new Worker<IngestionJobData>(
      'job-ingestion',
      this.processIngestionJob.bind(this),
      {
        connection: queueManager['redis'],
        concurrency: config.ingestion.maxConcurrentJobs,
        removeOnComplete: 100,
        removeOnFail: 50,
      }
    );

    this.embeddingWorker = new Worker<EmbeddingJobData>(
      'embedding-generation',
      this.processEmbeddingJob.bind(this),
      {
        connection: queueManager['redis'],
        concurrency: 5, // Limit concurrent embedding requests
        removeOnComplete: 200,
        removeOnFail: 50,
      }
    );

    this.setupWorkerEventListeners();
  }

  private initializeScrapers(): void {
    this.scrapers.set('shine', new ShineScraper());
    this.scrapers.set('naukri', new NaukriScraper());
    this.scrapers.set('linkedin', new LinkedInScraper());
    this.scrapers.set('remote', new RemoteScraper());
  }

  private setupWorkerEventListeners(): void {
    // Job worker events
    this.jobWorker.on('completed', (job) => {
      logger.info(`Ingestion job completed: ${job.id}`, {
        source: job.data.source,
        duration: Date.now() - job.timestamp,
      });
    });

    this.jobWorker.on('failed', (job, err) => {
      logger.error(`Ingestion job failed: ${job?.id}`, {
        source: job?.data.source,
        error: err.message,
        stack: err.stack,
      });
    });

    this.jobWorker.on('error', (err) => {
      logger.error('Job worker error:', err);
    });

    // Embedding worker events
    this.embeddingWorker.on('completed', (job) => {
      logger.debug(`Embedding job completed: ${job.id}`, {
        jobId: job.data.jobId,
        type: job.data.type,
      });
    });

    this.embeddingWorker.on('failed', (job, err) => {
      logger.error(`Embedding job failed: ${job?.id}`, {
        jobId: job?.data.jobId,
        error: err.message,
      });
    });

    this.embeddingWorker.on('error', (err) => {
      logger.error('Embedding worker error:', err);
    });
  }

  public async start(): Promise<void> {
    try {
      // Initialize all scrapers
      for (const [source, scraper] of this.scrapers) {
        try {
          await scraper.initialize();
          logger.info(`${source} scraper initialized`);
        } catch (error) {
          logger.error(`Failed to initialize ${source} scraper:`, error);
        }
      }

      // Initialize embedding service
      await this.embeddingService.initialize();
      logger.info('Embedding service initialized');

      // Schedule recurring ingestion
      await this.queueManager.scheduleRecurringIngestion();

      logger.info('Job processor started successfully');
    } catch (error) {
      logger.error('Failed to start job processor:', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      // Close workers
      await this.jobWorker.close();
      await this.embeddingWorker.close();

      // Cleanup scrapers
      for (const [source, scraper] of this.scrapers) {
        try {
          await scraper.cleanup();
          logger.info(`${source} scraper cleaned up`);
        } catch (error) {
          logger.error(`Error cleaning up ${source} scraper:`, error);
        }
      }

      logger.info('Job processor stopped successfully');
    } catch (error) {
      logger.error('Error stopping job processor:', error);
      throw error;
    }
  }

  private async processIngestionJob(job: Job<IngestionJobData>): Promise<void> {
    const { source, maxPages, filters } = job.data;
    const scraper = this.scrapers.get(source);

    if (!scraper) {
      throw new Error(`No scraper found for source: ${source}`);
    }

    try {
      logger.info(`Starting ingestion job for ${source}`, {
        jobId: job.id,
        maxPages,
        filters,
      });

      // Update job progress
      await job.updateProgress(10);

      // Scrape jobs from the source
      const scrapedJobs = await scraper.scrapeJobs(maxPages);
      logger.info(`Scraped ${scrapedJobs.length} jobs from ${source}`);

      await job.updateProgress(50);

      // Process jobs in batches
      const batchSize = config.ingestion.batchSize;
      const batches = this.createBatches(scrapedJobs, batchSize);
      let totalInserted = 0;

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        
        try {
          // Normalize job data
          const normalizedJobs = batch.map(rawJob => scraper.normalizeJobData(rawJob));

          // Insert jobs into database
          const insertedIds = await this.database.insertJobsBatch(normalizedJobs);
          totalInserted += insertedIds.length;

          // Queue embedding generation for new jobs
          if (insertedIds.length > 0) {
            await this.queueEmbeddingJobs(insertedIds, normalizedJobs);
          }

          // Update progress
          const progress = 50 + ((i + 1) / batches.length) * 50;
          await job.updateProgress(Math.round(progress));

          logger.info(`Processed batch ${i + 1}/${batches.length} for ${source}`, {
            batchSize: batch.length,
            inserted: insertedIds.length,
            totalInserted,
          });

        } catch (error) {
          logger.error(`Error processing batch ${i + 1} for ${source}:`, error);
          // Continue with next batch
        }
      }

      // Final job completion
      await job.updateProgress(100);
      
      logger.info(`Ingestion job completed for ${source}`, {
        jobId: job.id,
        totalScraped: scrapedJobs.length,
        totalInserted,
        duration: Date.now() - job.timestamp,
      });

      // Return summary for job result
      job.returnvalue = {
        source,
        scraped: scrapedJobs.length,
        inserted: totalInserted,
        duration: Date.now() - job.timestamp,
      };

    } catch (error) {
      logger.error(`Ingestion job failed for ${source}:`, error);
      throw error;
    }
  }

  private async processEmbeddingJob(job: Job<EmbeddingJobData>): Promise<void> {
    const { jobId, text, type } = job.data;

    try {
      logger.debug(`Generating embedding for ${type}`, {
        jobId,
        textLength: text.length,
      });

      // Generate embedding using OpenAI
      const embedding = await this.embeddingService.generateEmbedding(text);

      // Update database with embedding
      if (type === 'job') {
        await this.database.updateJobEmbedding(jobId, embedding);
      }

      logger.debug(`Embedding generated successfully`, {
        jobId,
        embeddingDimensions: embedding.length,
      });

      job.returnvalue = {
        jobId,
        type,
        embeddingDimensions: embedding.length,
      };

    } catch (error) {
      logger.error(`Embedding generation failed for ${type} ${jobId}:`, error);
      throw error;
    }
  }

  private async queueEmbeddingJobs(jobIds: string[], normalizedJobs: any[]): Promise<void> {
    const embeddingJobs: EmbeddingJobData[] = [];

    for (let i = 0; i < jobIds.length; i++) {
      const jobId = jobIds[i];
      const job = normalizedJobs[i];

      // Create text for embedding (title + description + requirements)
      const textParts = [
        job.title,
        job.company,
        job.description,
        job.requirements,
        job.skills?.join(' '),
      ].filter(Boolean);

      const text = textParts.join(' ').substring(0, 8000); // Limit text length

      embeddingJobs.push({
        jobId,
        text,
        type: 'job',
      });
    }

    if (embeddingJobs.length > 0) {
      await this.queueManager.addBulkEmbeddingJobs(embeddingJobs);
    }
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    return batches;
  }

  public async triggerIngestion(source: string, priority: 'low' | 'normal' | 'high' = 'normal'): Promise<string> {
    const sourceConfig = config.sources[source as keyof typeof config.sources];
    
    if (!sourceConfig || !sourceConfig.enabled) {
      throw new Error(`Source ${source} is not enabled or configured`);
    }

    const job = await this.queueManager.addIngestionJob({
      source,
      timestamp: new Date(),
      priority,
      maxPages: sourceConfig.maxPages,
    });

    return job.id as string;
  }

  public async triggerEmbeddingGeneration(): Promise<number> {
    // Get jobs without embeddings
    const jobsWithoutEmbeddings = await this.database.getJobsWithoutEmbeddings(100);
    
    if (jobsWithoutEmbeddings.length === 0) {
      logger.info('No jobs found without embeddings');
      return 0;
    }

    const embeddingJobs: EmbeddingJobData[] = jobsWithoutEmbeddings.map(job => ({
      jobId: job.id,
      text: `${job.title} ${job.company} ${job.description}`.substring(0, 8000),
      type: 'job',
    }));

    await this.queueManager.addBulkEmbeddingJobs(embeddingJobs);
    
    logger.info(`Queued ${embeddingJobs.length} embedding generation jobs`);
    return embeddingJobs.length;
  }

  public async getProcessorStats(): Promise<{
    workers: any;
    scrapers: any;
    embeddings: any;
  }> {
    const queueStats = await this.queueManager.getQueueStats();
    const jobsWithoutEmbeddings = await this.database.getJobsWithoutEmbeddings(1);

    return {
      workers: {
        jobWorker: {
          isRunning: !this.jobWorker.closing,
          concurrency: this.jobWorker.opts.concurrency,
        },
        embeddingWorker: {
          isRunning: !this.embeddingWorker.closing,
          concurrency: this.embeddingWorker.opts.concurrency,
        },
      },
      scrapers: {
        initialized: this.scrapers.size,
        available: Array.from(this.scrapers.keys()),
      },
      embeddings: {
        pending: jobsWithoutEmbeddings.length > 0 ? '100+' : '0',
        queueStats: queueStats.embeddingQueue,
      },
    };
  }
}