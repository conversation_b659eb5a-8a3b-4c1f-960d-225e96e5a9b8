import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const configSchema = z.object({
  // Server configuration
  port: z.coerce.number().default(3001),
  nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
  logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),

  // Database configuration
  databaseUrl: z.string().url(),

  // Redis configuration
  redisUrl: z.string().url(),

  // OpenAI configuration
  openaiApiKey: z.string().min(1),

  // Bright Data proxy configuration
  brightDataProxyUrl: z.string().url().optional(),

  // Job ingestion configuration
  ingestion: z.object({
    batchSize: z.coerce.number().default(100),
    maxConcurrentJobs: z.coerce.number().default(5),
    retryAttempts: z.coerce.number().default(3),
    retryDelay: z.coerce.number().default(5000), // milliseconds
    requestTimeout: z.coerce.number().default(30000), // milliseconds
    rateLimitDelay: z.coerce.number().default(1000), // milliseconds between requests
  }).default({}),

  // Source-specific configuration
  sources: z.object({
    shine: z.object({
      baseUrl: z.string().url().default('https://www.shine.com'),
      maxPages: z.coerce.number().default(50),
      enabled: z.coerce.boolean().default(true),
    }).default({}),
    
    naukri: z.object({
      baseUrl: z.string().url().default('https://www.naukri.com'),
      maxPages: z.coerce.number().default(50),
      enabled: z.coerce.boolean().default(true),
    }).default({}),
    
    linkedin: z.object({
      baseUrl: z.string().url().default('https://www.linkedin.com'),
      maxPages: z.coerce.number().default(25),
      enabled: z.coerce.boolean().default(true),
    }).default({}),
    
    remoteOk: z.object({
      apiUrl: z.string().url().default('https://remoteok.io/api'),
      enabled: z.coerce.boolean().default(true),
    }).default({}),
    
    weWorkRemotely: z.object({
      rssUrl: z.string().url().default('https://weworkremotely.com/categories/remote-programming-jobs.rss'),
      enabled: z.coerce.boolean().default(true),
    }).default({}),
  }).default({}),

  // CORS configuration
  cors: z.object({
    allowedOrigins: z.array(z.string()).default(['http://localhost:3000', 'https://app.karmsakha.com']),
  }).default({}),

  // Monitoring configuration
  monitoring: z.object({
    metricsEnabled: z.coerce.boolean().default(true),
    healthCheckInterval: z.coerce.number().default(30000), // milliseconds
  }).default({}),
});

export type Config = z.infer<typeof configSchema>;

const parseConfig = (): Config => {
  try {
    return configSchema.parse({
      port: process.env.PORT,
      nodeEnv: process.env.NODE_ENV,
      logLevel: process.env.LOG_LEVEL,
      
      databaseUrl: process.env.DATABASE_URL,
      redisUrl: process.env.REDIS_URL,
      openaiApiKey: process.env.OPENAI_API_KEY,
      brightDataProxyUrl: process.env.BRIGHT_DATA_PROXY_URL,
      
      ingestion: {
        batchSize: process.env.INGESTION_BATCH_SIZE,
        maxConcurrentJobs: process.env.MAX_CONCURRENT_JOBS,
        retryAttempts: process.env.RETRY_ATTEMPTS,
        retryDelay: process.env.RETRY_DELAY,
        requestTimeout: process.env.REQUEST_TIMEOUT,
        rateLimitDelay: process.env.RATE_LIMIT_DELAY,
      },
      
      sources: {
        shine: {
          baseUrl: process.env.SHINE_BASE_URL,
          maxPages: process.env.SHINE_MAX_PAGES,
          enabled: process.env.SHINE_ENABLED,
        },
        naukri: {
          baseUrl: process.env.NAUKRI_BASE_URL,
          maxPages: process.env.NAUKRI_MAX_PAGES,
          enabled: process.env.NAUKRI_ENABLED,
        },
        linkedin: {
          baseUrl: process.env.LINKEDIN_BASE_URL,
          maxPages: process.env.LINKEDIN_MAX_PAGES,
          enabled: process.env.LINKEDIN_ENABLED,
        },
        remoteOk: {
          apiUrl: process.env.REMOTE_OK_API_URL,
          enabled: process.env.REMOTE_OK_ENABLED,
        },
        weWorkRemotely: {
          rssUrl: process.env.WE_WORK_REMOTELY_RSS_URL,
          enabled: process.env.WE_WORK_REMOTELY_ENABLED,
        },
      },
      
      cors: {
        allowedOrigins: process.env.CORS_ALLOWED_ORIGINS?.split(',') || undefined,
      },
      
      monitoring: {
        metricsEnabled: process.env.METRICS_ENABLED,
        healthCheckInterval: process.env.HEALTH_CHECK_INTERVAL,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      );
      throw new Error(
        `Configuration validation failed:\n${errorMessages.join('\n')}`
      );
    }
    throw error;
  }
};

export const config = parseConfig();