import OpenAI from 'openai';
import { logger } from '../utils/logger';
import { config } from '../config/config';

export class EmbeddingService {
  private openai: OpenAI;
  private initialized = false;

  constructor() {
    this.openai = new OpenAI({
      apiKey: config.openaiApiKey,
      timeout: 30000,
    });
  }

  public async initialize(): Promise<void> {
    try {
      // Test the OpenAI connection
      await this.openai.models.list();
      this.initialized = true;
      logger.info('Embedding service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize embedding service:', error);
      throw error;
    }
  }

  public async generateEmbedding(text: string): Promise<number[]> {
    if (!this.initialized) {
      throw new Error('Embedding service not initialized');
    }

    try {
      // Clean and prepare text
      const cleanedText = this.prepareText(text);
      
      if (!cleanedText || cleanedText.length < 10) {
        throw new Error('Text too short for embedding generation');
      }

      // Generate embedding using OpenAI
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: cleanedText,
        encoding_format: 'float',
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('No embedding data received from OpenAI');
      }

      const embedding = response.data[0].embedding;
      
      logger.debug('Generated embedding', {
        textLength: cleanedText.length,
        embeddingDimensions: embedding.length,
        model: 'text-embedding-3-small',
      });

      return embedding;

    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  public async generateEmbeddingsBatch(texts: string[]): Promise<number[][]> {
    if (!this.initialized) {
      throw new Error('Embedding service not initialized');
    }

    if (texts.length === 0) {
      return [];
    }

    try {
      // Clean and prepare texts
      const cleanedTexts = texts.map(text => this.prepareText(text)).filter(Boolean);
      
      if (cleanedTexts.length === 0) {
        throw new Error('No valid texts for embedding generation');
      }

      // Batch size limit for OpenAI API
      const batchSize = 100;
      const batches = this.createBatches(cleanedTexts, batchSize);
      const allEmbeddings: number[][] = [];

      for (const batch of batches) {
        try {
          const response = await this.openai.embeddings.create({
            model: 'text-embedding-3-small',
            input: batch,
            encoding_format: 'float',
          });

          const batchEmbeddings = response.data.map(item => item.embedding);
          allEmbeddings.push(...batchEmbeddings);

          // Rate limiting
          await this.delay(100);

        } catch (error) {
          logger.error(`Error generating batch embeddings:`, error);
          // Add empty embeddings for failed batch
          const emptyEmbeddings = new Array(batch.length).fill(new Array(1536).fill(0));
          allEmbeddings.push(...emptyEmbeddings);
        }
      }

      logger.info(`Generated ${allEmbeddings.length} embeddings in batch`, {
        originalTexts: texts.length,
        validTexts: cleanedTexts.length,
        batches: batches.length,
      });

      return allEmbeddings;

    } catch (error) {
      logger.error('Error generating batch embeddings:', error);
      throw error;
    }
  }

  public async calculateSimilarity(embedding1: number[], embedding2: number[]): Promise<number> {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions');
    }

    // Calculate cosine similarity
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    return Math.max(0, Math.min(1, similarity)); // Clamp between 0 and 1
  }

  public async findSimilarJobs(
    queryEmbedding: number[], 
    candidateEmbeddings: Array<{ id: string; embedding: number[] }>,
    threshold = 0.8,
    limit = 50
  ): Promise<Array<{ id: string; similarity: number }>> {
    const similarities: Array<{ id: string; similarity: number }> = [];

    for (const candidate of candidateEmbeddings) {
      try {
        const similarity = await this.calculateSimilarity(queryEmbedding, candidate.embedding);
        
        if (similarity >= threshold) {
          similarities.push({
            id: candidate.id,
            similarity,
          });
        }
      } catch (error) {
        logger.warn(`Error calculating similarity for job ${candidate.id}:`, error);
      }
    }

    // Sort by similarity (highest first) and limit results
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit);
  }

  private prepareText(text: string): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    return text
      .trim()
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[^\w\s\.\,\;\:\!\?\-\(\)]/g, '') // Remove special characters
      .substring(0, 8191); // OpenAI token limit for text-embedding-3-small
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    return batches;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public isHealthy(): boolean {
    return this.initialized;
  }

  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.initialized) {
        return false;
      }

      // Quick test with minimal text
      const testEmbedding = await this.generateEmbedding('test');
      return testEmbedding.length > 0;
    } catch (error) {
      logger.error('Embedding service health check failed:', error);
      return false;
    }
  }

  public getUsageStats(): {
    model: string;
    dimensions: number;
    maxTokens: number;
  } {
    return {
      model: 'text-embedding-3-small',
      dimensions: 1536,
      maxTokens: 8191,
    };
  }
}