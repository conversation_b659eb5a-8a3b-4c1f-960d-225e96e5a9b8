import { Client, Pool } from 'pg';
import { logger } from '../utils/logger';
import { config } from '../config/config';

export interface JobData {
  id?: string;
  source: string;
  external_id: string;
  url: string;
  title: string;
  company: string;
  location?: string;
  country?: string;
  is_remote?: boolean;
  employment_type?: string;
  experience_level?: string;
  salary_min?: number;
  salary_max?: number;
  currency?: string;
  description?: string;
  requirements?: string;
  skills?: string[];
  posted_at: Date;
  expires_at?: Date;
  raw_data?: any;
}

export interface IngestionStats {
  totalJobs: number;
  jobsBySource: Record<string, number>;
  jobsToday: number;
  lastIngestion: Date | null;
  averageJobsPerHour: number;
}

export class Database {
  private pool: Pool;
  private isConnected = false;

  constructor() {
    this.pool = new Pool({
      connectionString: config.databaseUrl,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
      ssl: config.nodeEnv === 'production' ? { rejectUnauthorized: false } : false,
    });

    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  public async connect(): Promise<void> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      this.isConnected = true;
      logger.info('Database connection established');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.pool.end();
      this.isConnected = false;
      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error closing database connection:', error);
      throw error;
    }
  }

  public isHealthy(): boolean {
    return this.isConnected;
  }

  public async healthCheck(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  public async insertJob(jobData: JobData): Promise<string> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        INSERT INTO jobs (
          source, external_id, url, title, company, location, country,
          is_remote, employment_type, experience_level, salary_min, salary_max,
          currency, description, requirements, skills, posted_at, expires_at, raw_data
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
        )
        ON CONFLICT (source, external_id) 
        DO UPDATE SET
          url = EXCLUDED.url,
          title = EXCLUDED.title,
          company = EXCLUDED.company,
          location = EXCLUDED.location,
          country = EXCLUDED.country,
          is_remote = EXCLUDED.is_remote,
          employment_type = EXCLUDED.employment_type,
          experience_level = EXCLUDED.experience_level,
          salary_min = EXCLUDED.salary_min,
          salary_max = EXCLUDED.salary_max,
          currency = EXCLUDED.currency,
          description = EXCLUDED.description,
          requirements = EXCLUDED.requirements,
          skills = EXCLUDED.skills,
          posted_at = EXCLUDED.posted_at,
          expires_at = EXCLUDED.expires_at,
          raw_data = EXCLUDED.raw_data,
          updated_at = NOW()
        RETURNING id
      `;

      const values = [
        jobData.source,
        jobData.external_id,
        jobData.url,
        jobData.title,
        jobData.company,
        jobData.location,
        jobData.country || 'IN',
        jobData.is_remote || false,
        jobData.employment_type,
        jobData.experience_level,
        jobData.salary_min,
        jobData.salary_max,
        jobData.currency || 'INR',
        jobData.description,
        jobData.requirements,
        jobData.skills,
        jobData.posted_at,
        jobData.expires_at,
        JSON.stringify(jobData.raw_data),
      ];

      const result = await client.query(query, values);
      return result.rows[0].id;
    } finally {
      client.release();
    }
  }

  public async insertJobsBatch(jobsData: JobData[]): Promise<string[]> {
    if (jobsData.length === 0) return [];

    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      const insertedIds: string[] = [];

      for (const jobData of jobsData) {
        try {
          const id = await this.insertJob(jobData);
          insertedIds.push(id);
        } catch (error) {
          logger.warn(`Failed to insert job ${jobData.external_id} from ${jobData.source}:`, error);
          // Continue with other jobs
        }
      }

      await client.query('COMMIT');
      logger.info(`Inserted ${insertedIds.length} jobs out of ${jobsData.length} attempted`);
      
      return insertedIds;
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error in batch insert, transaction rolled back:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  public async jobExists(source: string, externalId: string): Promise<boolean> {
    const client = await this.pool.connect();
    
    try {
      const query = 'SELECT 1 FROM jobs WHERE source = $1 AND external_id = $2 LIMIT 1';
      const result = await client.query(query, [source, externalId]);
      return result.rows.length > 0;
    } finally {
      client.release();
    }
  }

  public async getJobsWithoutEmbeddings(limit = 100): Promise<Array<{ id: string; title: string; description: string; company: string }>> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT id, title, description, company
        FROM jobs 
        WHERE embedding IS NULL 
          AND is_active = TRUE
          AND description IS NOT NULL
        ORDER BY created_at DESC
        LIMIT $1
      `;
      
      const result = await client.query(query, [limit]);
      return result.rows;
    } finally {
      client.release();
    }
  }

  public async updateJobEmbedding(jobId: string, embedding: number[]): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      const query = 'UPDATE jobs SET embedding = $1, updated_at = NOW() WHERE id = $2';
      await client.query(query, [JSON.stringify(embedding), jobId]);
    } finally {
      client.release();
    }
  }

  public async getIngestionStats(): Promise<IngestionStats> {
    const client = await this.pool.connect();
    
    try {
      // Total jobs
      const totalResult = await client.query('SELECT COUNT(*) as count FROM jobs WHERE is_active = TRUE');
      const totalJobs = parseInt(totalResult.rows[0].count);

      // Jobs by source
      const sourceResult = await client.query(`
        SELECT source, COUNT(*) as count 
        FROM jobs 
        WHERE is_active = TRUE 
        GROUP BY source
      `);
      const jobsBySource = sourceResult.rows.reduce((acc, row) => {
        acc[row.source] = parseInt(row.count);
        return acc;
      }, {} as Record<string, number>);

      // Jobs today
      const todayResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM jobs 
        WHERE is_active = TRUE 
          AND created_at >= CURRENT_DATE
      `);
      const jobsToday = parseInt(todayResult.rows[0].count);

      // Last ingestion
      const lastResult = await client.query(`
        SELECT MAX(created_at) as last_ingestion 
        FROM jobs
      `);
      const lastIngestion = lastResult.rows[0].last_ingestion;

      // Average jobs per hour (last 24 hours)
      const avgResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM jobs 
        WHERE created_at >= NOW() - INTERVAL '24 hours'
      `);
      const averageJobsPerHour = Math.round(parseInt(avgResult.rows[0].count) / 24);

      return {
        totalJobs,
        jobsBySource,
        jobsToday,
        lastIngestion,
        averageJobsPerHour,
      };
    } finally {
      client.release();
    }
  }

  public async cleanupOldJobs(daysOld = 90): Promise<number> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        UPDATE jobs 
        SET is_active = FALSE, updated_at = NOW()
        WHERE is_active = TRUE 
          AND (
            expires_at < NOW() 
            OR posted_at < NOW() - INTERVAL '${daysOld} days'
          )
      `;
      
      const result = await client.query(query);
      return result.rowCount || 0;
    } finally {
      client.release();
    }
  }
}