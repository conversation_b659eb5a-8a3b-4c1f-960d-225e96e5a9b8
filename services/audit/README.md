# Karmsakha Audit & Compliance Service

A comprehensive DPDP Act 2023 compliant audit and compliance service for the Karmsakha job platform.

## 🚀 Features

### DPDP Act 2023 Compliance
- **Consent Management**: Complete consent lifecycle management with granular controls
- **Audit Logging**: Comprehensive audit trails for all data processing activities
- **Data Subject Rights**: Support for access, rectification, erasure, portability, and objection requests
- **Breach Management**: Automated data breach detection, reporting, and resolution workflows
- **Compliance Reporting**: Automated generation of compliance reports and metrics

### Core Capabilities
- Real-time audit logging with 7-year retention
- Consent validation and expiry management
- Data processing record keeping
- Automated compliance checks
- Risk assessment and mitigation tracking
- Regulatory reporting and notifications

## 📊 Architecture

### Components
- **Audit Service**: Core audit logging and compliance tracking
- **Consent Manager**: User consent lifecycle management
- **Breach Handler**: Data breach detection and response
- **Report Generator**: Compliance report generation
- **Background Workers**: Automated compliance tasks

### Data Models
- **AuditLog**: All system events and user actions
- **ConsentRecord**: User consent with evidence and lifecycle
- **DataProcessingRecord**: Data processing activities tracking
- **DataBreachRecord**: Security incident management
- **ComplianceReport**: Generated compliance reports
- **DataSubjectRequest**: User rights request handling

## 🛠️ Development

### Prerequisites
- Node.js 18+
- PostgreSQL 15+ with pgvector extension
- Redis 7+
- Docker (optional)

### Setup
```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Start development server
npm run dev
```

### Environment Variables
```env
# Server Configuration
NODE_ENV=development
PORT=3005
HOST=0.0.0.0

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/karmsakha_audit

# Redis
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-jwt-secret

# DPDP Compliance
DATA_RETENTION_DAYS=2555
CONSENT_EXPIRY_DAYS=365
AUDIT_LOG_RETENTION_DAYS=2555

# Email
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=password
FROM_EMAIL=<EMAIL>

# Compliance Officer
COMPLIANCE_OFFICER_EMAIL=<EMAIL>
DPO_EMAIL=<EMAIL>

# Reports
REPORT_STORAGE_PATH=/tmp/reports
REPORT_RETENTION_DAYS=1095
```

## 📚 API Documentation

### Endpoints

#### Audit Logs
- `POST /api/v1/audit` - Create audit log entry
- `GET /api/v1/audit` - Get audit logs with filtering
- `GET /api/v1/audit/:id` - Get specific audit log
- `GET /api/v1/audit/user/:userId` - Get user audit trail
- `GET /api/v1/audit/dashboard/compliance` - Compliance dashboard

#### Consent Management
- `POST /api/v1/consent` - Record user consent
- `POST /api/v1/consent/withdraw` - Withdraw consent
- `GET /api/v1/consent/user/:userId` - Get user consents
- `POST /api/v1/consent/validate` - Validate consent
- `PUT /api/v1/consent/preferences` - Update preferences
- `POST /api/v1/consent/renew` - Renew expiring consents
- `GET /api/v1/consent/expiring` - Get expiring consents
- `GET /api/v1/consent/analytics` - Consent analytics

#### Compliance
- `GET /api/v1/compliance/status` - Overall compliance status
- `POST /api/v1/compliance/check` - Run compliance check
- `GET /api/v1/compliance/risks` - Risk assessment
- `POST /api/v1/compliance/remediate` - Remediation actions

#### Reports
- `GET /api/v1/reports` - List compliance reports
- `POST /api/v1/reports/generate` - Generate new report
- `GET /api/v1/reports/:id` - Get specific report
- `GET /api/v1/reports/:id/download` - Download report

#### Breach Management
- `POST /api/v1/breach/report` - Report data breach
- `GET /api/v1/breach` - List data breaches
- `PUT /api/v1/breach/:id/status` - Update breach status
- `POST /api/v1/breach/:id/notify` - Send breach notifications

### Authentication
All endpoints require JWT authentication via `Authorization: Bearer <token>` header.

## 🔒 Security Features

### Data Protection
- End-to-end encryption for sensitive data
- Secure storage with AES-256 encryption
- PII tokenization and pseudonymization
- Secure data deletion with cryptographic erasure

### Access Control
- Role-based access control (RBAC)
- Audit trail for all admin actions
- Multi-factor authentication support
- Session management with secure cookies

### Compliance
- DPDP Act 2023 requirements
- Data minimization principles
- Purpose limitation enforcement
- Consent granularity and withdrawal
- Automated retention policy enforcement

## 📊 Monitoring & Alerting

### Metrics
- Compliance score tracking
- Consent rate monitoring
- Audit log volume metrics
- Data breach detection rates
- Response time monitoring

### Alerts
- Data breach notifications
- Consent expiry warnings
- Compliance violations
- System health alerts
- Performance degradation

## 🧪 Testing

```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run coverage report
npm run test:coverage

# Run specific test file
npm test -- audit.test.ts
```

### Test Coverage
- Unit tests for all services
- Integration tests for API endpoints
- End-to-end compliance workflows
- Performance and load testing
- Security vulnerability testing

## 🚀 Deployment

### Docker
```bash
# Build image
docker build -t karmsakha/audit .

# Run container
docker run -p 3005:3005 karmsakha/audit
```

### Kubernetes
```yaml
# Use Helm chart
helm install audit ./helm/karmsakha-audit
```

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL/TLS certificates installed
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery tested
- [ ] Security scanning completed
- [ ] Load testing performed
- [ ] Compliance validation completed

## 📋 Compliance Checklist

### DPDP Act 2023 Requirements
- [x] Lawful basis for processing
- [x] Consent management system
- [x] Data subject rights implementation
- [x] Breach notification procedures
- [x] Data protection impact assessments
- [x] Record of processing activities
- [x] Data retention and deletion
- [x] Cross-border transfer safeguards
- [x] Privacy by design implementation
- [x] Regular compliance audits

### Technical Safeguards
- [x] Data encryption at rest and in transit
- [x] Access controls and authentication
- [x] Audit logging and monitoring
- [x] Backup and disaster recovery
- [x] Security incident response
- [x] Vulnerability management
- [x] Secure development practices
- [x] Third-party risk management

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/compliance-enhancement`)
3. Commit changes (`git commit -am 'Add new compliance feature'`)
4. Push to branch (`git push origin feature/compliance-enhancement`)
5. Create Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: https://docs.karmsakha.com/audit
- **Issues**: https://github.com/karmsakha/platform/issues
- **Security**: <EMAIL>

## 🔗 Related Services

- [Job Ingestor](../ingestor/README.md) - Job data ingestion service
- [Vector Ranker](../ranker/README.md) - Job ranking and matching
- [Stagehand Worker](../stagehand-worker/README.md) - Automated job applications
- [GraphQL Gateway](../gateway/README.md) - API gateway and federation
- [Admin Dashboard](../admin-dashboard/README.md) - Management interface