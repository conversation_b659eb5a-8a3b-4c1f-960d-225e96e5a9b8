// Prisma schema for DPDP Act 2023 Compliance and Audit Service

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Audit Log Model
model AuditLog {
  id                String           @id @default(uuid())
  eventType         String
  userId            String?          @db.Uuid
  sessionId         String?
  ipAddress         String
  userAgent         String?
  timestamp         DateTime         @default(now())
  resource          String
  action            String
  outcome           String           // SUCCESS, FAILURE, ERROR
  errorCode         String?
  errorMessage      String?
  metadata          Json?
  dataCategories    String[]         // Array of data categories
  processingPurposes String[]        // Array of processing purposes
  legalBasis        String?
  
  // Retention information
  expiresAt         DateTime?
  retentionReason   String?
  
  // Compliance flags
  dpdpCompliant     Boolean          @default(true)
  consentRequired   <PERSON>olean          @default(false)
  consentObtained   Boolean          @default(false)
  dataMinimized     Boolean          @default(true)
  purposeLimited    Boolean          @default(true)
  
  // Geolocation
  country           String?
  region            String?
  city              String?
  
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  
  @@index([userId])
  @@index([eventType])
  @@index([timestamp])
  @@index([resource])
  @@index([outcome])
  @@index([country])
  @@map("audit_logs")
}

// Data Processing Records
model DataProcessingRecord {
  id                    String           @id @default(uuid())
  userId                String           @db.Uuid
  dataType              String
  processingPurpose     String
  legalBasis            String
  consentId             String?          @db.Uuid
  dataSource            String
  dataDestination       String?
  processingLocation    String
  retentionPeriod       Int              // Days
  isAutomatedDecision   Boolean          @default(false)
  hasProfilingImpact    Boolean          @default(false)
  
  // Third party sharing
  thirdPartySharing     Json?            // Array of third party sharing records
  
  // Data subject rights
  canAccess             Boolean          @default(true)
  canRectify            Boolean          @default(true)
  canErase              Boolean          @default(true)
  canPortability        Boolean          @default(true)
  canRestrict           Boolean          @default(true)
  canObject             Boolean          @default(true)
  
  // Risk assessment
  riskLevel             String           // LOW, MEDIUM, HIGH, VERY_HIGH
  mitigationMeasures    String[]
  lastRiskAssessment    DateTime?
  
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt
  
  // Relations
  consent               ConsentRecord?   @relation(fields: [consentId], references: [id])
  
  @@index([userId])
  @@index([dataType])
  @@index([processingPurpose])
  @@index([legalBasis])
  @@index([riskLevel])
  @@map("data_processing_records")
}

// Consent Records
model ConsentRecord {
  id                    String           @id @default(uuid())
  userId                String           @db.Uuid
  status                String           // GIVEN, WITHDRAWN, EXPIRED, PENDING, PARTIAL
  granularity           String           // BROAD, SPECIFIC, GRANULAR
  method                String           // EXPLICIT_OPT_IN, CHECKBOX, etc.
  
  // Consent scope
  dataCategories        String[]
  processingPurposes    String[]
  legalBasis            String
  
  // Lifecycle
  givenAt               DateTime         @default(now())
  expiresAt             DateTime?
  withdrawnAt           DateTime?
  withdrawalReason      String?
  lastConfirmedAt       DateTime?
  
  // Context
  ipAddress             String
  userAgent             String?
  sessionId             String?
  
  // Evidence
  consentText           String
  language              String           @default("en")
  version               String
  checksum              String
  attachments           String[]         @default([])
  
  // Compliance flags
  isInformedConsent     Boolean          @default(true)
  isSpecificConsent     Boolean          @default(true)
  isUnambiguousConsent  Boolean          @default(true)
  isWithdrawable        Boolean          @default(true)
  isDpdpCompliant       Boolean          @default(true)
  requiresReconfirmation Boolean         @default(false)
  
  // Geolocation
  country               String
  region                String?
  jurisdiction          String
  
  // Audit trail
  auditTrail            Json[]           @default([])
  
  // Metadata
  metadata              Json?
  
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt
  
  // Relations
  dataProcessingRecords DataProcessingRecord[]
  preferences           ConsentPreference[]
  
  @@index([userId])
  @@index([status])
  @@index([givenAt])
  @@index([expiresAt])
  @@index([country])
  @@map("consent_records")
}

// Consent Preferences
model ConsentPreference {
  id              String         @id @default(uuid())
  userId          String         @db.Uuid
  consentId       String         @db.Uuid
  category        String
  purpose         String
  isConsented     Boolean
  isRequired      Boolean        @default(false)
  priority        Int            @default(5)
  notes           String?
  lastUpdated     DateTime       @default(now())
  
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  
  // Relations
  consent         ConsentRecord  @relation(fields: [consentId], references: [id], onDelete: Cascade)
  
  @@unique([userId, consentId, category, purpose])
  @@index([userId])
  @@index([category])
  @@index([purpose])
  @@map("consent_preferences")
}

// Data Breach Records
model DataBreachRecord {
  id                        String           @id @default(uuid())
  incidentId                String           @unique
  severity                  String           // LOW, MEDIUM, HIGH, CRITICAL
  status                    String           // DETECTED, INVESTIGATING, CONTAINED, RESOLVED
  category                  String           // UNAUTHORIZED_ACCESS, DATA_LOSS, SYSTEM_BREACH, etc.
  
  // Breach details
  description               String
  affectedDataTypes         String[]
  affectedUserCount         Int              @default(0)
  affectedRecords           Int              @default(0)
  potentialImpact           String
  
  // Timeline
  detectedAt                DateTime         @default(now())
  occurredAt                DateTime?
  containedAt               DateTime?
  resolvedAt                DateTime?
  reportedAt                DateTime?
  
  // DPDP compliance
  notificationRequired      Boolean          @default(true)
  authorityNotified         Boolean          @default(false)
  usersNotified             Boolean          @default(false)
  notificationDeadline      DateTime?
  
  // Investigation
  investigationNotes        String?
  rootCause                 String?
  mitigationSteps           String[]         @default([])
  preventiveMeasures        String[]         @default([])
  
  // Responsible parties
  reportedBy                String?
  assignedTo                String?
  investigatedBy            String[]         @default([])
  
  // Legal and regulatory
  legalImplications         String?
  regulatoryActions         String[]         @default([])
  complianceStatus          String           @default("PENDING")
  
  // Financial impact
  estimatedCost             Decimal?         @db.Decimal(10, 2)
  actualCost                Decimal?         @db.Decimal(10, 2)
  
  // Metadata
  metadata                  Json?
  attachments               String[]         @default([])
  
  createdAt                 DateTime         @default(now())
  updatedAt                 DateTime         @updatedAt
  
  @@index([severity])
  @@index([status])
  @@index([detectedAt])
  @@index([notificationRequired])
  @@map("data_breach_records")
}

// Compliance Reports
model ComplianceReport {
  id                String     @id @default(uuid())
  title             String
  type              String     // ANNUAL, QUARTERLY, MONTHLY, INCIDENT, AUDIT
  period            String     // e.g., "2024-Q1"
  status            String     // DRAFT, PENDING_REVIEW, APPROVED, PUBLISHED
  
  // Report content
  executiveSummary  String?
  findings          Json[]     @default([])
  recommendations   Json[]     @default([])
  metrics           Json?
  
  // Compliance scores
  overallScore      Decimal?   @db.Decimal(5, 2)
  consentScore      Decimal?   @db.Decimal(5, 2)
  dataProtectionScore Decimal? @db.Decimal(5, 2)
  auditScore        Decimal?   @db.Decimal(5, 2)
  
  // Generation details
  generatedBy       String
  reviewedBy        String?
  approvedBy        String?
  
  // Timeline
  periodStart       DateTime
  periodEnd         DateTime
  generatedAt       DateTime   @default(now())
  reviewedAt        DateTime?
  approvedAt        DateTime?
  publishedAt       DateTime?
  
  // File information
  filePath          String?
  fileSize          Int?
  fileHash          String?
  
  // Metadata
  metadata          Json?
  
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  
  @@index([type])
  @@index([status])
  @@index([periodStart])
  @@index([periodEnd])
  @@map("compliance_reports")
}

// Data Subject Rights Requests
model DataSubjectRequest {
  id                  String     @id @default(uuid())
  requestId           String     @unique
  userId              String     @db.Uuid
  type                String     // ACCESS, RECTIFICATION, ERASURE, PORTABILITY, RESTRICTION, OBJECTION
  status              String     // RECEIVED, PROCESSING, COMPLETED, REJECTED
  priority            String     @default("NORMAL") // LOW, NORMAL, HIGH, URGENT
  
  // Request details
  description         String
  requestedData       String[]   @default([])
  legalBasis          String?
  
  // Timeline
  receivedAt          DateTime   @default(now())
  acknowledgedAt      DateTime?
  completedAt         DateTime?
  responseDeadline    DateTime
  
  // Processing
  processedBy         String?
  processingNotes     String?
  verificationStatus  String     @default("PENDING") // PENDING, VERIFIED, REJECTED
  verificationMethod  String?
  
  // Response
  responseMethod      String?    // EMAIL, POSTAL, SECURE_PORTAL
  responseDelivered   Boolean    @default(false)
  responseFilePath    String?
  
  // Compliance
  isWithinDeadline    Boolean    @default(true)
  extensionGranted    Boolean    @default(false)
  extensionReason     String?
  extensionDays       Int        @default(0)
  
  // Metadata
  metadata            Json?
  attachments         String[]   @default([])
  
  createdAt           DateTime   @default(now())
  updatedAt           DateTime   @updatedAt
  
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([receivedAt])
  @@index([responseDeadline])
  @@map("data_subject_requests")
}