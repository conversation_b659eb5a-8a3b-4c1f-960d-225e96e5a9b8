# Multi-stage Dockerfile for Audit Service

# Stage 1: Base dependencies
FROM node:18-alpine AS base
WORKDIR /app

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache \
    tini \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Stage 2: Development dependencies
FROM base AS deps
RUN npm ci --include=dev

# Stage 3: Production dependencies
FROM base AS prod-deps
RUN npm ci --omit=dev && npm cache clean --force

# Stage 4: Build stage
FROM deps AS build
WORKDIR /app

# Copy source code
COPY src/ ./src/
COPY prisma/ ./prisma/

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Remove development dependencies
RUN npm prune --production

# Stage 5: Development image
FROM base AS development
WORKDIR /app

# Copy all dependencies
COPY --from=deps /app/node_modules ./node_modules
COPY --from=build /app/prisma ./prisma

# Copy source code and built files
COPY src/ ./src/
COPY package*.json ./
COPY tsconfig.json ./

# Generate Prisma client
RUN npx prisma generate

# Expose port
EXPOSE 3005

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3005/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Use tini for proper signal handling
ENTRYPOINT ["/sbin/tini", "--"]

# Start development server
CMD ["npm", "run", "dev"]

# Stage 6: Production image
FROM base AS production
WORKDIR /app

# Copy production dependencies
COPY --from=prod-deps /app/node_modules ./node_modules
COPY --from=build /app/dist ./dist
COPY --from=build /app/prisma ./prisma
COPY package*.json ./

# Generate Prisma client
RUN npx prisma generate

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/reports && \
    chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3005

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3005/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Labels for container metadata
LABEL maintainer="<EMAIL>" \
      org.opencontainers.image.title="Karmsakha Audit Service" \
      org.opencontainers.image.description="DPDP Act 2023 Compliance and Audit Service" \
      org.opencontainers.image.vendor="Karmsakha" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.source="https://github.com/karmsakha/platform"

# Use dumb-init for proper process management
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Start production server
CMD ["npm", "start"]