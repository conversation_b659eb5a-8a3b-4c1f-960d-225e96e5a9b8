import { FastifyPluginAsync } from 'fastify';
import { consentService } from '../services/consent.service';
import { 
  createConsentSchema, 
  withdrawConsentSchema,
  updateConsentPreferencesSchema,
  consentQuerySchema,
  consentValidationSchema,
  consentRenewalSchema
} from '../schemas/consent';
import { authMiddleware } from '../middleware/auth';

export const consentRoutes: FastifyPluginAsync = async (server) => {
  // Create consent record
  server.post('/', {
    schema: {
      tags: ['Consent'],
      summary: 'Record user consent',
      description: 'Create a new consent record for DPDP Act compliance',
      body: createConsentSchema,
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            message: { type: 'string' },
          },
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            details: { type: 'array' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const consentData = request.body as any;
      
      // Add request context
      consentData.ipAddress = request.ip;
      consentData.userAgent = request.headers['user-agent'];
      consentData.sessionId = request.headers['x-session-id'];
      
      const consent = await consentService.createConsent(consentData);
      
      reply.status(201).send({
        id: consent.id,
        status: consent.status,
        message: 'Consent recorded successfully',
      });
    } catch (error) {
      server.log.error('Failed to create consent:', error);
      reply.status(500).send({
        error: 'Failed to record consent',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Withdraw consent
  server.post('/withdraw', {
    schema: {
      tags: ['Consent'],
      summary: 'Withdraw user consent',
      description: 'Withdraw previously given consent',
      body: withdrawConsentSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            effectiveDate: { type: 'string', format: 'date-time' },
          },
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const withdrawalData = request.body as any;
      
      // Add request context
      withdrawalData.ipAddress = request.ip;
      withdrawalData.userAgent = request.headers['user-agent'];
      withdrawalData.sessionId = request.headers['x-session-id'];
      
      const result = await consentService.withdrawConsent(withdrawalData);
      
      if (!result) {
        return reply.status(404).send({
          error: 'Consent record not found',
        });
      }
      
      reply.send({
        message: 'Consent withdrawn successfully',
        effectiveDate: result.withdrawnAt,
      });
    } catch (error) {
      server.log.error('Failed to withdraw consent:', error);
      reply.status(500).send({
        error: 'Failed to withdraw consent',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Get user consents
  server.get('/user/:userId', {
    schema: {
      tags: ['Consent'],
      summary: 'Get user consents',
      description: 'Retrieve all consent records for a specific user',
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string', format: 'uuid' },
        },
        required: ['userId'],
      },
      querystring: {
        type: 'object',
        properties: {
          status: { type: 'string' },
          includeExpired: { type: 'boolean', default: false },
          includeWithdrawn: { type: 'boolean', default: false },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            consents: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  status: { type: 'string' },
                  dataCategories: { type: 'array' },
                  processingPurposes: { type: 'array' },
                  givenAt: { type: 'string', format: 'date-time' },
                  expiresAt: { type: 'string', format: 'date-time' },
                  compliance: { type: 'object' },
                },
              },
            },
            summary: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                active: { type: 'number' },
                expired: { type: 'number' },
                withdrawn: { type: 'number' },
              },
            },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const { userId } = request.params as { userId: string };
      const query = request.query as any;
      
      const result = await consentService.getUserConsents(userId, query);
      reply.send(result);
    } catch (error) {
      server.log.error('Failed to get user consents:', error);
      reply.status(500).send({
        error: 'Failed to retrieve user consents',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Validate consent
  server.post('/validate', {
    schema: {
      tags: ['Consent'],
      summary: 'Validate consent',
      description: 'Check if valid consent exists for specific data processing',
      body: consentValidationSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            isValid: { type: 'boolean' },
            consentId: { type: 'string' },
            details: {
              type: 'object',
              properties: {
                hasConsent: { type: 'boolean' },
                isActive: { type: 'boolean' },
                isExpired: { type: 'boolean' },
                expiresAt: { type: 'string', format: 'date-time' },
                needsRenewal: { type: 'boolean' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const validationData = request.body as any;
      const result = await consentService.validateConsent(validationData);
      reply.send(result);
    } catch (error) {
      server.log.error('Failed to validate consent:', error);
      reply.status(500).send({
        error: 'Failed to validate consent',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Update consent preferences
  server.put('/preferences', {
    schema: {
      tags: ['Consent'],
      summary: 'Update consent preferences',
      description: 'Update granular consent preferences for a user',
      body: updateConsentPreferencesSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            updated: { type: 'number' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const preferencesData = request.body as any;
      
      // Add request context
      preferencesData.ipAddress = request.ip;
      preferencesData.userAgent = request.headers['user-agent'];
      preferencesData.sessionId = request.headers['x-session-id'];
      
      const result = await consentService.updateConsentPreferences(preferencesData);
      
      reply.send({
        message: 'Consent preferences updated successfully',
        updated: result.updated,
      });
    } catch (error) {
      server.log.error('Failed to update consent preferences:', error);
      reply.status(500).send({
        error: 'Failed to update consent preferences',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Renew consents
  server.post('/renew', {
    schema: {
      tags: ['Consent'],
      summary: 'Renew consent',
      description: 'Renew expiring consents for a user',
      body: consentRenewalSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            message: { type: 'string' },
            renewed: { type: 'number' },
            notificationSent: { type: 'boolean' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const renewalData = request.body as any;
      
      // Add request context
      renewalData.ipAddress = request.ip;
      renewalData.userAgent = request.headers['user-agent'];
      renewalData.sessionId = request.headers['x-session-id'];
      
      const result = await consentService.renewConsent(renewalData);
      reply.send(result);
    } catch (error) {
      server.log.error('Failed to renew consent:', error);
      reply.status(500).send({
        error: 'Failed to renew consent',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Get expiring consents
  server.get('/expiring', {
    schema: {
      tags: ['Consent'],
      summary: 'Get expiring consents',
      description: 'Retrieve consents that are expiring soon',
      querystring: {
        type: 'object',
        properties: {
          daysAhead: { type: 'number', minimum: 1, maximum: 365, default: 30 },
          userId: { type: 'string', format: 'uuid' },
          limit: { type: 'number', minimum: 1, maximum: 1000, default: 100 },
          offset: { type: 'number', minimum: 0, default: 0 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            expiringConsents: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  userId: { type: 'string' },
                  expiresAt: { type: 'string', format: 'date-time' },
                  daysUntilExpiry: { type: 'number' },
                  dataCategories: { type: 'array' },
                  processingPurposes: { type: 'array' },
                },
              },
            },
            totalCount: { type: 'number' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const query = request.query as any;
      const result = await consentService.getExpiringConsents(query);
      reply.send(result);
    } catch (error) {
      server.log.error('Failed to get expiring consents:', error);
      reply.status(500).send({
        error: 'Failed to retrieve expiring consents',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Consent analytics
  server.get('/analytics', {
    schema: {
      tags: ['Consent'],
      summary: 'Get consent analytics',
      description: 'Retrieve consent analytics and statistics',
      querystring: {
        type: 'object',
        properties: {
          dateFrom: { type: 'string', format: 'date' },
          dateTo: { type: 'string', format: 'date' },
          granularity: { 
            type: 'string', 
            enum: ['day', 'week', 'month'],
            default: 'day'
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            metrics: {
              type: 'object',
              properties: {
                totalConsents: { type: 'number' },
                activeConsents: { type: 'number' },
                withdrawnConsents: { type: 'number' },
                expiredConsents: { type: 'number' },
                consentRate: { type: 'number' },
                withdrawalRate: { type: 'number' },
              },
            },
            trends: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string', format: 'date' },
                  given: { type: 'number' },
                  withdrawn: { type: 'number' },
                  expired: { type: 'number' },
                },
              },
            },
            breakdown: {
              type: 'object',
              properties: {
                byCategory: { type: 'object' },
                byPurpose: { type: 'object' },
                byMethod: { type: 'object' },
              },
            },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const query = request.query as any;
      const analytics = await consentService.getConsentAnalytics(query);
      reply.send(analytics);
    } catch (error) {
      server.log.error('Failed to get consent analytics:', error);
      reply.status(500).send({
        error: 'Failed to retrieve consent analytics',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });
};