import { FastifyPluginAsync } from 'fastify';
import { auditService } from '../services/audit.service';
import { createAuditLogSchema, auditQuerySchema } from '../schemas/audit';
import { authMiddleware } from '../middleware/auth';

export const auditRoutes: FastifyPluginAsync = async (server) => {
  // Create audit log entry
  server.post('/', {
    schema: {
      tags: ['Audit'],
      summary: 'Create audit log entry',
      description: 'Record a new audit event in compliance with DPDP Act 2023',
      body: createAuditLogSchema,
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            message: { type: 'string' },
          },
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            details: { type: 'array' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const auditData = request.body as any;
      
      // Add request context
      auditData.ipAddress = request.ip;
      auditData.userAgent = request.headers['user-agent'];
      auditData.sessionId = request.headers['x-session-id'];
      
      const auditLog = await auditService.createAuditLog(auditData);
      
      reply.status(201).send({
        id: auditLog.id,
        message: 'Audit log created successfully',
      });
    } catch (error) {
      server.log.error('Failed to create audit log:', error);
      reply.status(500).send({
        error: 'Failed to create audit log',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Get audit logs with filtering and pagination
  server.get('/', {
    schema: {
      tags: ['Audit'],
      summary: 'Get audit logs',
      description: 'Retrieve audit logs with filtering and pagination',
      querystring: auditQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  eventType: { type: 'string' },
                  userId: { type: 'string' },
                  resource: { type: 'string' },
                  action: { type: 'string' },
                  outcome: { type: 'string' },
                  timestamp: { type: 'string', format: 'date-time' },
                  ipAddress: { type: 'string' },
                  metadata: { type: 'object' },
                },
              },
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                limit: { type: 'number' },
                offset: { type: 'number' },
                hasMore: { type: 'boolean' },
              },
            },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const query = request.query as any;
      const result = await auditService.getAuditLogs(query);
      
      reply.send(result);
    } catch (error) {
      server.log.error('Failed to get audit logs:', error);
      reply.status(500).send({
        error: 'Failed to retrieve audit logs',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Get audit log by ID
  server.get('/:id', {
    schema: {
      tags: ['Audit'],
      summary: 'Get audit log by ID',
      description: 'Retrieve a specific audit log entry',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
        },
        required: ['id'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            eventType: { type: 'string' },
            userId: { type: 'string' },
            resource: { type: 'string' },
            action: { type: 'string' },
            outcome: { type: 'string' },
            timestamp: { type: 'string', format: 'date-time' },
            ipAddress: { type: 'string' },
            metadata: { type: 'object' },
            compliance: { type: 'object' },
          },
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const auditLog = await auditService.getAuditLogById(id);
      
      if (!auditLog) {
        return reply.status(404).send({
          error: 'Audit log not found',
        });
      }
      
      reply.send(auditLog);
    } catch (error) {
      server.log.error('Failed to get audit log:', error);
      reply.status(500).send({
        error: 'Failed to retrieve audit log',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Get user audit trail
  server.get('/user/:userId', {
    schema: {
      tags: ['Audit'],
      summary: 'Get user audit trail',
      description: 'Retrieve complete audit trail for a specific user',
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string', format: 'uuid' },
        },
        required: ['userId'],
      },
      querystring: {
        type: 'object',
        properties: {
          eventType: { type: 'string' },
          dateFrom: { type: 'string', format: 'date' },
          dateTo: { type: 'string', format: 'date' },
          limit: { type: 'number', minimum: 1, maximum: 1000, default: 100 },
          offset: { type: 'number', minimum: 0, default: 0 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            auditTrail: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  eventType: { type: 'string' },
                  action: { type: 'string' },
                  timestamp: { type: 'string', format: 'date-time' },
                  outcome: { type: 'string' },
                  metadata: { type: 'object' },
                },
              },
            },
            summary: {
              type: 'object',
              properties: {
                totalEvents: { type: 'number' },
                eventTypes: { type: 'object' },
                complianceScore: { type: 'number' },
              },
            },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const { userId } = request.params as { userId: string };
      const query = request.query as any;
      
      const result = await auditService.getUserAuditTrail(userId, query);
      reply.send(result);
    } catch (error) {
      server.log.error('Failed to get user audit trail:', error);
      reply.status(500).send({
        error: 'Failed to retrieve user audit trail',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });

  // Compliance dashboard data
  server.get('/dashboard/compliance', {
    schema: {
      tags: ['Audit'],
      summary: 'Get compliance dashboard data',
      description: 'Retrieve compliance metrics and statistics',
      querystring: {
        type: 'object',
        properties: {
          dateFrom: { type: 'string', format: 'date' },
          dateTo: { type: 'string', format: 'date' },
          granularity: { 
            type: 'string', 
            enum: ['hour', 'day', 'week', 'month'],
            default: 'day'
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            metrics: {
              type: 'object',
              properties: {
                totalEvents: { type: 'number' },
                complianceRate: { type: 'number' },
                breachCount: { type: 'number' },
                consentRate: { type: 'number' },
              },
            },
            trends: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string', format: 'date' },
                  events: { type: 'number' },
                  compliance: { type: 'number' },
                },
              },
            },
            alerts: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  level: { type: 'string' },
                  message: { type: 'string' },
                  timestamp: { type: 'string', format: 'date-time' },
                },
              },
            },
          },
        },
      },
      security: [{ Bearer: [] }],
    },
    preHandler: [authMiddleware],
  }, async (request, reply) => {
    try {
      const query = request.query as any;
      const dashboard = await auditService.getComplianceDashboard(query);
      reply.send(dashboard);
    } catch (error) {
      server.log.error('Failed to get compliance dashboard:', error);
      reply.status(500).send({
        error: 'Failed to retrieve compliance dashboard',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  });
};