import { FastifyPluginAsync } from 'fastify';
import { env } from '../config/env';

export const healthRoutes: FastifyPluginAsync = async (server) => {
  // Basic health check
  server.get('/', {
    schema: {
      tags: ['Health'],
      summary: 'Service health check',
      description: 'Check if the audit service is healthy',
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string', format: 'date-time' },
            version: { type: 'string' },
            environment: { type: 'string' },
            uptime: { type: 'number' },
          },
        },
      },
    },
  }, async (request, reply) => {
    reply.send({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: env.NODE_ENV,
      uptime: process.uptime(),
    });
  });

  // Detailed health check with dependencies
  server.get('/detailed', {
    schema: {
      tags: ['Health'],
      summary: 'Detailed health check',
      description: 'Comprehensive health check including dependencies',
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string', format: 'date-time' },
            version: { type: 'string' },
            environment: { type: 'string' },
            uptime: { type: 'number' },
            dependencies: {
              type: 'object',
              properties: {
                database: { type: 'object' },
                redis: { type: 'object' },
                external_services: { type: 'object' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    // TODO: Implement actual dependency checks
    const checks = {
      database: { status: 'healthy', responseTime: 5 },
      redis: { status: 'healthy', responseTime: 2 },
      external_services: { status: 'healthy', responseTime: 10 },
    };

    const allHealthy = Object.values(checks).every(check => check.status === 'healthy');

    reply.status(allHealthy ? 200 : 503).send({
      status: allHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: env.NODE_ENV,
      uptime: process.uptime(),
      dependencies: checks,
    });
  });

  // Readiness probe
  server.get('/ready', {
    schema: {
      tags: ['Health'],
      summary: 'Readiness probe',
      description: 'Check if the service is ready to accept traffic',
      response: {
        200: {
          type: 'object',
          properties: {
            ready: { type: 'boolean' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  }, async (request, reply) => {
    // TODO: Implement readiness checks
    reply.send({
      ready: true,
      timestamp: new Date().toISOString(),
    });
  });

  // Liveness probe
  server.get('/live', {
    schema: {
      tags: ['Health'],
      summary: 'Liveness probe',
      description: 'Check if the service is alive',
      response: {
        200: {
          type: 'object',
          properties: {
            alive: { type: 'boolean' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  }, async (request, reply) => {
    reply.send({
      alive: true,
      timestamp: new Date().toISOString(),
    });
  });
};