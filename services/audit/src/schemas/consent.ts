import { z } from 'zod';
import { DataCategory, ProcessingPurpose, LegalBasis } from './audit';

// Consent status enum
export const ConsentStatus = z.enum([
  'GIVEN',
  'WITHDRAWN',
  'EXPIRED',
  'PENDING',
  'PARTIAL',
]);

// Consent granularity levels
export const ConsentGranularity = z.enum([
  'BROAD', // General consent for all processing
  'SPECIFIC', // Consent for specific purposes
  'GRANULAR', // Detailed consent per data type and purpose
]);

// Consent method enum
export const ConsentMethod = z.enum([
  'EXPLICIT_OPT_IN',
  'CHECKBOX',
  'ELECTRONIC_SIGNATURE',
  'VERBAL_RECORDED',
  'WRITTEN_FORM',
  'BIOMETRIC',
  'API_CONSENT',
]);

// Withdrawal reason enum
export const WithdrawalReason = z.enum([
  'USER_REQUEST',
  'DATA_BREACH',
  'SERVICE_CHANGE',
  'POLICY_UPDATE',
  'LEGAL_REQUIREMENT',
  'EXPIRED',
  'SYSTEM_CLEANUP',
  'GDPR_REQUEST',
]);

// Consent record schema
export const consentRecordSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  
  // Consent details
  status: ConsentStatus,
  granularity: ConsentGranularity,
  method: ConsentMethod,
  
  // What consent covers
  dataCategories: z.array(DataCategory),
  processingPurposes: z.array(ProcessingPurpose),
  legalBasis: LegalBasis,
  
  // Consent lifecycle
  givenAt: z.date(),
  expiresAt: z.date().optional(),
  withdrawnAt: z.date().optional(),
  withdrawalReason: WithdrawalReason.optional(),
  lastConfirmedAt: z.date().optional(),
  
  // Context information
  ipAddress: z.string().ip(),
  userAgent: z.string().optional(),
  sessionId: z.string().optional(),
  
  // Consent evidence
  evidence: z.object({
    consentText: z.string(), // The exact text shown to user
    language: z.string().default('en'),
    version: z.string(), // Policy/consent version
    checksum: z.string(), // Hash of consent text for integrity
    attachments: z.array(z.string()).optional(), // File URLs/paths
  }),
  
  // Additional metadata
  metadata: z.record(z.any()).optional(),
  
  // Compliance flags
  compliance: z.object({
    isInformedConsent: z.boolean(),
    isSpecificConsent: z.boolean(),
    isUnambiguousConsent: z.boolean(),
    isWithdrawable: z.boolean(),
    isDpdpCompliant: z.boolean(),
    requiresReconfirmation: z.boolean(),
  }),
  
  // Geolocation context
  geolocation: z.object({
    country: z.string(),
    region: z.string().optional(),
    jurisdiction: z.string(), // Legal jurisdiction
  }),
  
  // Audit trail
  auditTrail: z.array(z.object({
    action: z.string(),
    timestamp: z.date(),
    actor: z.string().optional(), // User ID or system
    details: z.record(z.any()).optional(),
  })).optional(),
  
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Consent preference schema
export const consentPreferenceSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  category: DataCategory,
  purpose: ProcessingPurpose,
  
  // Preference settings
  isConsented: z.boolean(),
  isRequired: z.boolean(), // If consent is required for service
  priority: z.number().min(1).max(10).default(5), // User priority level
  
  // Preference metadata
  notes: z.string().optional(),
  lastUpdated: z.date(),
  
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Request schemas
export const createConsentSchema = z.object({
  userId: z.string().uuid(),
  dataCategories: z.array(DataCategory),
  processingPurposes: z.array(ProcessingPurpose),
  method: ConsentMethod,
  granularity: ConsentGranularity.default('SPECIFIC'),
  consentText: z.string(),
  language: z.string().default('en'),
  version: z.string(),
  expiresAt: z.coerce.date().optional(),
  metadata: z.record(z.any()).optional(),
});

export const withdrawConsentSchema = z.object({
  consentId: z.string().uuid(),
  reason: WithdrawalReason,
  effectiveDate: z.coerce.date().optional(),
  notes: z.string().optional(),
});

export const updateConsentPreferencesSchema = z.object({
  userId: z.string().uuid(),
  preferences: z.array(z.object({
    category: DataCategory,
    purpose: ProcessingPurpose,
    isConsented: z.boolean(),
    priority: z.number().min(1).max(10).default(5),
    notes: z.string().optional(),
  })),
});

export const consentQuerySchema = z.object({
  userId: z.string().uuid().optional(),
  status: ConsentStatus.optional(),
  dataCategory: DataCategory.optional(),
  purpose: ProcessingPurpose.optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  expiringBefore: z.coerce.date().optional(),
  requiresReconfirmation: z.coerce.boolean().optional(),
  limit: z.coerce.number().min(1).max(1000).default(100),
  offset: z.coerce.number().min(0).default(0),
  sortBy: z.enum(['givenAt', 'expiresAt', 'status']).default('givenAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Consent validation schema
export const consentValidationSchema = z.object({
  userId: z.string().uuid(),
  dataCategories: z.array(DataCategory),
  processingPurposes: z.array(ProcessingPurpose),
  checkExpiry: z.boolean().default(true),
  requireActive: z.boolean().default(true),
});

// Consent renewal schema
export const consentRenewalSchema = z.object({
  userId: z.string().uuid(),
  consentIds: z.array(z.string().uuid()).optional(),
  dataCategories: z.array(DataCategory).optional(),
  newExpiryDate: z.coerce.date(),
  reason: z.string(),
  sendNotification: z.boolean().default(true),
});

// Export types
export type ConsentRecord = z.infer<typeof consentRecordSchema>;
export type ConsentPreference = z.infer<typeof consentPreferenceSchema>;
export type CreateConsent = z.infer<typeof createConsentSchema>;
export type WithdrawConsent = z.infer<typeof withdrawConsentSchema>;
export type UpdateConsentPreferences = z.infer<typeof updateConsentPreferencesSchema>;
export type ConsentQuery = z.infer<typeof consentQuerySchema>;
export type ConsentValidation = z.infer<typeof consentValidationSchema>;
export type ConsentRenewal = z.infer<typeof consentRenewalSchema>;