import { z } from 'zod';

// Enum definitions
export const AuditEventType = z.enum([
  'USER_REGISTRATION',
  'USER_LOGIN',
  'USER_LOGOUT',
  'USER_PROFILE_UPDATE',
  'USER_DELETION',
  'JOB_VIEW',
  'JO<PERSON>_APPLICATION',
  'JOB_SAVE',
  'RESUME_UPLOAD',
  'RESUME_UPDATE',
  'RESUME_DELETE',
  'CONSENT_GIVEN',
  'CONSENT_WITHDRAWN',
  'CONSENT_UPDATED',
  'DATA_EXPORT_REQUEST',
  'DATA_DELETION_REQUEST',
  'DATA_BREACH_DETECTED',
  'DATA_BREACH_REPORTED',
  'SYSTEM_ACCESS',
  'ADMIN_ACTION',
  'COMPLIANCE_CHECK',
  'REPORT_GENERATED',
  'POLICY_UPDATE',
  'SETTINGS_CHANGE',
]);

export const DataCategory = z.enum([
  'PER<PERSON>NAL_IDENTIFIABLE',
  'SENSITIVE_PERSONAL',
  'PROFESSIONAL',
  'B<PERSON><PERSON>VIORAL',
  'TECHNICAL',
  'FINANCIAL',
  'HEALTH',
  'BIOMETRIC',
  'LOCATION',
  'COMMUNICATION',
]);

export const ProcessingPurpose = z.enum([
  'JOB_MATCHING',
  'PROFILE_CREATION',
  'APPLICATION_PROCESSING',
  'COMMUNICATION',
  'ANALYTICS',
  'MARKETING',
  'FRAUD_PREVENTION',
  'LEGAL_COMPLIANCE',
  'RESEARCH',
  'SERVICE_IMPROVEMENT',
]);

export const LegalBasis = z.enum([
  'CONSENT',
  'CONTRACT',
  'LEGAL_OBLIGATION',
  'VITAL_INTERESTS',
  'PUBLIC_TASK',
  'LEGITIMATE_INTERESTS',
]);

// Base audit log schema
export const auditLogSchema = z.object({
  id: z.string().uuid(),
  eventType: AuditEventType,
  userId: z.string().uuid().optional(),
  sessionId: z.string().optional(),
  ipAddress: z.string().ip(),
  userAgent: z.string().optional(),
  timestamp: z.date(),
  resource: z.string(), // The resource being accessed/modified
  action: z.string(), // Specific action taken
  outcome: z.enum(['SUCCESS', 'FAILURE', 'ERROR']),
  errorCode: z.string().optional(),
  errorMessage: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  dataCategories: z.array(DataCategory).optional(),
  processingPurposes: z.array(ProcessingPurpose).optional(),
  legalBasis: LegalBasis.optional(),
  retention: z.object({
    expiresAt: z.date(),
    reason: z.string(),
  }).optional(),
  compliance: z.object({
    dpdpCompliant: z.boolean(),
    consentRequired: z.boolean(),
    consentObtained: z.boolean(),
    dataMinimized: z.boolean(),
    purposeLimited: z.boolean(),
  }),
  geolocation: z.object({
    country: z.string(),
    region: z.string().optional(),
    city: z.string().optional(),
  }).optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Data processing record schema
export const dataProcessingRecordSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  dataType: DataCategory,
  processingPurpose: ProcessingPurpose,
  legalBasis: LegalBasis,
  consentId: z.string().uuid().optional(),
  dataSource: z.string(), // Where the data came from
  dataDestination: z.string().optional(), // Where the data is sent
  processingLocation: z.string(), // Country/region where processing occurs
  retentionPeriod: z.number(), // Days
  isAutomatedDecision: z.boolean(),
  hasProfilingImpact: z.boolean(),
  thirdPartySharing: z.array(z.object({
    recipient: z.string(),
    purpose: z.string(),
    legalBasis: LegalBasis,
    country: z.string(),
    adequacyDecision: z.boolean().optional(),
  })).optional(),
  dataSubjectRights: z.object({
    canAccess: z.boolean(),
    canRectify: z.boolean(),
    canErase: z.boolean(),
    canPortability: z.boolean(),
    canRestrict: z.boolean(),
    canObject: z.boolean(),
  }),
  riskAssessment: z.object({
    riskLevel: z.enum(['LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH']),
    mitigationMeasures: z.array(z.string()),
    lastAssessed: z.date(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Request schemas for API endpoints
export const createAuditLogSchema = z.object({
  eventType: AuditEventType,
  userId: z.string().uuid().optional(),
  sessionId: z.string().optional(),
  resource: z.string(),
  action: z.string(),
  outcome: z.enum(['SUCCESS', 'FAILURE', 'ERROR']),
  errorCode: z.string().optional(),
  errorMessage: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  dataCategories: z.array(DataCategory).optional(),
  processingPurposes: z.array(ProcessingPurpose).optional(),
  legalBasis: LegalBasis.optional(),
});

export const auditQuerySchema = z.object({
  userId: z.string().uuid().optional(),
  eventType: AuditEventType.optional(),
  resource: z.string().optional(),
  outcome: z.enum(['SUCCESS', 'FAILURE', 'ERROR']).optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  limit: z.coerce.number().min(1).max(1000).default(100),
  offset: z.coerce.number().min(0).default(0),
  sortBy: z.enum(['timestamp', 'eventType', 'outcome']).default('timestamp'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Export types
export type AuditLog = z.infer<typeof auditLogSchema>;
export type DataProcessingRecord = z.infer<typeof dataProcessingRecordSchema>;
export type CreateAuditLog = z.infer<typeof createAuditLogSchema>;
export type AuditQuery = z.infer<typeof auditQuerySchema>;