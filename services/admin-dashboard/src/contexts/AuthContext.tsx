import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { gql } from '@apollo/client';
import { getAuthToken, setAuthToken, removeAuthToken, isAuthenticated } from '@services/apollo-client';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// GraphQL queries and mutations
const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    currentUser {
      id
      name
      email
      role
      permissions
    }
  }
`;

const LOGIN_MUTATION = gql`
  mutation Login($email: String!, $password: String!) {
    login(email: $email, password: $password) {
      token
      user {
        id
        name
        email
        role
        permissions
      }
    }
  }
`;

const REFRESH_TOKEN_MUTATION = gql`
  mutation RefreshToken {
    refreshToken {
      token
      user {
        id
        name
        email
        role
        permissions
      }
    }
  }
`;

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if user is authenticated on mount
  const { data, loading, error } = useQuery(GET_CURRENT_USER, {
    skip: !isAuthenticated(),
    errorPolicy: 'ignore',
    onCompleted: (data) => {
      if (data?.currentUser) {
        setUser(data.currentUser);
      }
      setIsLoading(false);
    },
    onError: () => {
      // Token might be invalid, clear it
      removeAuthToken();
      setUser(null);
      setIsLoading(false);
    },
  });

  const [loginMutation] = useMutation(LOGIN_MUTATION, {
    onCompleted: (data) => {
      if (data?.login) {
        setAuthToken(data.login.token);
        setUser(data.login.user);
      }
    },
    onError: (error) => {
      console.error('Login error:', error);
      throw error;
    },
  });

  const [refreshTokenMutation] = useMutation(REFRESH_TOKEN_MUTATION, {
    onCompleted: (data) => {
      if (data?.refreshToken) {
        setAuthToken(data.refreshToken.token);
        setUser(data.refreshToken.user);
      }
    },
    onError: () => {
      // Refresh failed, logout user
      logout();
    },
  });

  // Initialize auth state
  useEffect(() => {
    if (!isAuthenticated()) {
      setIsLoading(false);
      return;
    }

    // If we have a token but no user data yet, wait for the query
    if (loading) {
      return;
    }

    // If query completed but no user data, token is invalid
    if (!data?.currentUser && !loading) {
      removeAuthToken();
      setUser(null);
    }

    setIsLoading(false);
  }, [data, loading, error]);

  // Auto-refresh token before expiration
  useEffect(() => {
    if (!user) return;

    const token = getAuthToken();
    if (!token) return;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const refreshTime = expirationTime - 5 * 60 * 1000; // Refresh 5 minutes before expiration
      const timeUntilRefresh = refreshTime - Date.now();

      if (timeUntilRefresh > 0) {
        const refreshTimeout = setTimeout(() => {
          refreshToken();
        }, timeUntilRefresh);

        return () => clearTimeout(refreshTimeout);
      }
    } catch (error) {
      console.error('Error parsing token:', error);
    }
  }, [user]);

  const login = async (email: string, password: string): Promise<void> => {
    try {
      await loginMutation({
        variables: { email, password },
      });
    } catch (error) {
      throw error;
    }
  };

  const logout = (): void => {
    removeAuthToken();
    setUser(null);
    // Redirect to login page
    window.location.href = '/login';
  };

  const refreshToken = async (): Promise<void> => {
    try {
      await refreshTokenMutation();
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};