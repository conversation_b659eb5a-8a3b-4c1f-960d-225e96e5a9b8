import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSubscription } from '@apollo/client';
import { gql } from '@apollo/client';
import toast from 'react-hot-toast';
import { useAuth } from './AuthContext';

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearNotification: (id: string) => void;
  clearAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// GraphQL subscriptions
const APPLICATION_STATUS_SUBSCRIPTION = gql`
  subscription ApplicationStatusChanged($userId: UUID!) {
    applicationStatusChanged(userId: $userId) {
      id
      userId
      jobId
      status
      confirmationNumber
      errorMessage
      appliedAt
      lastUpdated
      job {
        title
        company
      }
    }
  }
`;

const QUEUE_STATS_SUBSCRIPTION = gql`
  subscription QueueStatsUpdated {
    queueStatsUpdated {
      waiting
      active
      completed
      failed
      delayed
    }
  }
`;

const SYSTEM_HEALTH_SUBSCRIPTION = gql`
  subscription SystemHealthUpdated {
    systemHealthUpdated {
      status
      services {
        name
        status
        responseTime
        lastChecked
      }
      timestamp
    }
  }
`;

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Subscribe to application status changes
  const { data: applicationData } = useSubscription(APPLICATION_STATUS_SUBSCRIPTION, {
    variables: { userId: user?.id },
    skip: !user?.id || import.meta.env.VITE_ENABLE_REAL_TIME_UPDATES !== 'true',
    onData: ({ data }) => {
      if (data?.data?.applicationStatusChanged) {
        handleApplicationStatusChange(data.data.applicationStatusChanged);
      }
    },
  });

  // Subscribe to queue stats updates
  const { data: queueData } = useSubscription(QUEUE_STATS_SUBSCRIPTION, {
    skip: import.meta.env.VITE_ENABLE_REAL_TIME_UPDATES !== 'true',
    onData: ({ data }) => {
      if (data?.data?.queueStatsUpdated) {
        handleQueueStatsUpdate(data.data.queueStatsUpdated);
      }
    },
  });

  // Subscribe to system health updates
  const { data: healthData } = useSubscription(SYSTEM_HEALTH_SUBSCRIPTION, {
    skip: import.meta.env.VITE_ENABLE_REAL_TIME_UPDATES !== 'true',
    onData: ({ data }) => {
      if (data?.data?.systemHealthUpdated) {
        handleSystemHealthUpdate(data.data.systemHealthUpdated);
      }
    },
  });

  // Handle application status changes
  const handleApplicationStatusChange = (application: any) => {
    const { status, job, confirmationNumber, errorMessage } = application;
    
    let notification: Omit<Notification, 'id'>;
    
    switch (status) {
      case 'SUBMITTED':
        notification = {
          type: 'success',
          title: 'Application Submitted',
          message: `Successfully applied to ${job.title} at ${job.company}`,
          timestamp: new Date().toISOString(),
          read: false,
          actionUrl: `/applications/${application.id}`,
          metadata: { applicationId: application.id, jobId: application.jobId },
        };
        toast.success(notification.message);
        break;
        
      case 'ACKNOWLEDGED':
        notification = {
          type: 'info',
          title: 'Application Acknowledged',
          message: `Your application to ${job.title} has been acknowledged${confirmationNumber ? ` (${confirmationNumber})` : ''}`,
          timestamp: new Date().toISOString(),
          read: false,
          actionUrl: `/applications/${application.id}`,
          metadata: { applicationId: application.id, jobId: application.jobId },
        };
        toast.success(notification.message);
        break;
        
      case 'FAILED':
        notification = {
          type: 'error',
          title: 'Application Failed',
          message: `Failed to apply to ${job.title}: ${errorMessage || 'Unknown error'}`,
          timestamp: new Date().toISOString(),
          read: false,
          actionUrl: `/applications/${application.id}`,
          metadata: { applicationId: application.id, jobId: application.jobId, error: errorMessage },
        };
        toast.error(notification.message);
        break;
        
      case 'INTERVIEW_SCHEDULED':
        notification = {
          type: 'success',
          title: 'Interview Scheduled',
          message: `Interview scheduled for ${job.title} at ${job.company}`,
          timestamp: new Date().toISOString(),
          read: false,
          actionUrl: `/applications/${application.id}`,
          metadata: { applicationId: application.id, jobId: application.jobId },
        };
        toast.success(notification.message);
        break;
        
      case 'OFFER_RECEIVED':
        notification = {
          type: 'success',
          title: 'Job Offer Received!',
          message: `Congratulations! You received an offer for ${job.title} at ${job.company}`,
          timestamp: new Date().toISOString(),
          read: false,
          actionUrl: `/applications/${application.id}`,
          metadata: { applicationId: application.id, jobId: application.jobId },
        };
        toast.success(notification.message, { duration: 6000 });
        break;
        
      default:
        return; // Don't create notification for other status changes
    }
    
    addNotification(notification);
  };

  // Handle queue stats updates
  const handleQueueStatsUpdate = (stats: any) => {
    const { failed } = stats;
    
    // Only notify if there are new failures
    const previousFailedCount = parseInt(localStorage.getItem('last_failed_count') || '0');
    if (failed > previousFailedCount) {
      const newFailures = failed - previousFailedCount;
      
      const notification: Omit<Notification, 'id'> = {
        type: 'warning',
        title: 'Queue Processing Issues',
        message: `${newFailures} job application${newFailures > 1 ? 's' : ''} failed to process`,
        timestamp: new Date().toISOString(),
        read: false,
        actionUrl: '/queue',
        metadata: { failedCount: failed, newFailures },
      };
      
      addNotification(notification);
      toast.warning(notification.message);
    }
    
    localStorage.setItem('last_failed_count', failed.toString());
  };

  // Handle system health updates
  const handleSystemHealthUpdate = (health: any) => {
    const { status, services } = health;
    
    // Check for unhealthy services
    const unhealthyServices = services.filter((service: any) => service.status !== 'healthy');
    
    if (unhealthyServices.length > 0) {
      const notification: Omit<Notification, 'id'> = {
        type: 'error',
        title: 'System Health Alert',
        message: `${unhealthyServices.length} service${unhealthyServices.length > 1 ? 's are' : ' is'} experiencing issues`,
        timestamp: new Date().toISOString(),
        read: false,
        actionUrl: '/dashboard',
        metadata: { 
          systemStatus: status, 
          unhealthyServices: unhealthyServices.map((s: any) => s.name) 
        },
      };
      
      addNotification(notification);
      
      // Only show toast for critical issues to avoid spam
      if (status === 'critical') {
        toast.error(notification.message);
      }
    }
  };

  // Add notification to list
  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const newNotification: Notification = {
      ...notification,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
    
    setNotifications(prev => [newNotification, ...prev].slice(0, 100)); // Keep only last 100
  };

  // Mark notification as read
  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // Clear specific notification
  const clearNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Load notifications from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem('notifications');
    if (stored) {
      try {
        const parsedNotifications = JSON.parse(stored);
        setNotifications(parsedNotifications);
      } catch (error) {
        console.error('Failed to parse stored notifications:', error);
      }
    }
  }, []);

  // Save notifications to localStorage
  useEffect(() => {
    localStorage.setItem('notifications', JSON.stringify(notifications));
  }, [notifications]);

  // Browser notification permission (optional)
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      // Request permission when component mounts
      Notification.requestPermission();
    }
  }, []);

  // Show browser notifications for important events
  useEffect(() => {
    if (notifications.length > 0) {
      const latestNotification = notifications[0];
      
      if (!latestNotification.read && 'Notification' in window && Notification.permission === 'granted') {
        // Only show browser notifications for important events
        if (['success', 'error'].includes(latestNotification.type)) {
          new Notification(latestNotification.title, {
            body: latestNotification.message,
            icon: '/notification-icon.png',
            badge: '/notification-badge.png',
            tag: latestNotification.id,
            requireInteraction: latestNotification.type === 'error',
          });
        }
      }
    }
  }, [notifications]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};