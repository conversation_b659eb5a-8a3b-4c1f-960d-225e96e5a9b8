@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Inter font with font-display: swap for performance */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables for theme consistency */
:root {
  --color-primary-50: 239 246 255;
  --color-primary-500: 59 130 246;
  --color-primary-600: 37 99 235;
  --color-primary-700: 29 78 216;
  
  --color-secondary-50: 248 250 252;
  --color-secondary-500: 100 116 139;
  --color-secondary-600: 71 85 105;
  --color-secondary-700: 51 65 85;
  --color-secondary-800: 30 41 59;
  --color-secondary-900: 15 23 42;
  
  --color-success-500: 16 185 129;
  --color-warning-500: 245 158 11;
  --color-error-500: 239 68 68;
  --color-info-500: 59 130 246;
  
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-soft-lg: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  --border-radius-base: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  --transition-base: all 0.2s ease-in-out;
  --transition-colors: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* Dark mode variables */
.dark {
  --color-secondary-50: 15 23 42;
  --color-secondary-500: 148 163 184;
  --color-secondary-600: 100 116 139;
  --color-secondary-700: 71 85 105;
  --color-secondary-800: 51 65 85;
  --color-secondary-900: 248 250 252;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  color: rgb(var(--color-secondary-800));
  background-color: rgb(var(--color-secondary-50));
  transition: var(--transition-colors);
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid rgb(var(--color-primary-500));
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--color-secondary-100));
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--color-secondary-300));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--color-secondary-400));
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: rgb(var(--color-secondary-800));
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(var(--color-secondary-600));
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--color-secondary-500));
}

/* Component styles */
@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply border-secondary-300 text-secondary-700 bg-white hover:bg-secondary-50 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-error {
    @apply bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-secondary-200/50 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-secondary-200/50 bg-secondary-50/50;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-secondary-200/50 bg-secondary-50/50;
  }
  
  /* Form styles */
  .form-input {
    @apply block w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-colors;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-colors;
  }
  
  .form-textarea {
    @apply block w-full px-3 py-2 border border-secondary-300 rounded-md shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-colors;
  }
  
  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-1;
  }
  
  .form-error {
    @apply text-sm text-error-600 mt-1;
  }
  
  /* Status indicators */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply bg-success-100 text-success-800;
  }
  
  .status-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .status-error {
    @apply bg-error-100 text-error-800;
  }
  
  .status-info {
    @apply bg-info-100 text-info-800;
  }
  
  .status-neutral {
    @apply bg-secondary-100 text-secondary-800;
  }
  
  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-secondary-200;
  }
  
  .table-header {
    @apply bg-secondary-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-secondary-200;
  }
  
  .table-row {
    @apply hover:bg-secondary-50 transition-colors;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-secondary-900;
  }
  
  /* Animation utilities */
  .animate-enter {
    animation: slideInUp 0.3s ease-out;
  }
  
  .animate-exit {
    animation: slideOutDown 0.3s ease-in;
  }
  
  .loading-skeleton {
    @apply animate-pulse bg-secondary-200 rounded;
  }
  
  /* Dark mode overrides */
  .dark .card {
    @apply bg-secondary-800 border-secondary-700/50;
  }
  
  .dark .card-header {
    @apply bg-secondary-700/50 border-secondary-700/50;
  }
  
  .dark .card-footer {
    @apply bg-secondary-700/50 border-secondary-700/50;
  }
  
  .dark .form-input,
  .dark .form-select,
  .dark .form-textarea {
    @apply bg-secondary-800 border-secondary-600 text-secondary-100 placeholder-secondary-400;
  }
  
  .dark .form-label {
    @apply text-secondary-300;
  }
  
  .dark .table-header {
    @apply bg-secondary-700;
  }
  
  .dark .table-body {
    @apply bg-secondary-800 divide-secondary-700;
  }
  
  .dark .table-row {
    @apply hover:bg-secondary-700;
  }
  
  .dark .table-cell {
    @apply text-secondary-100;
  }
  
  .dark .loading-skeleton {
    @apply bg-secondary-700;
  }
}

/* Utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .transition-base {
    transition: var(--transition-base);
  }
  
  .transition-colors {
    transition: var(--transition-colors);
  }
  
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }
  
  .shadow-soft-lg {
    box-shadow: var(--shadow-soft-lg);
  }
  
  /* Performance optimizations */
  .will-change-transform {
    will-change: transform;
  }
  
  .will-change-opacity {
    will-change: opacity;
  }
  
  .contain-layout {
    contain: layout;
  }
  
  .contain-paint {
    contain: paint;
  }
  
  .contain-size {
    contain: size;
  }
  
  .contain-style {
    contain: style;
  }
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOutDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(20px);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Print styles */
@media print {
  * {
    color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  @page {
    margin: 0.5in;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-secondary-900;
  }
  
  .btn {
    @apply border-2 border-current;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Toast styles override */
.toast {
  @apply shadow-soft-lg border border-secondary-200/50;
}

.dark .toast {
  @apply bg-secondary-800 border-secondary-600/50 text-secondary-100;
}