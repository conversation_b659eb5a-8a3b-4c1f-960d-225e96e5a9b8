import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';

import { LoadingSpinner } from '@components/ui/LoadingSpinner';
import { ProtectedRoute } from '@components/auth/ProtectedRoute';
import { Layout } from '@components/layout/Layout';
import { useAuth } from '@hooks/useAuth';

// Lazy load pages for code splitting and better performance
const Dashboard = lazy(() => import('@pages/Dashboard'));
const Jobs = lazy(() => import('@pages/Jobs'));
const Applications = lazy(() => import('@pages/Applications'));
const Users = lazy(() => import('@pages/Users'));
const Analytics = lazy(() => import('@pages/Analytics'));
const Sources = lazy(() => import('@pages/Sources'));
const Queue = lazy(() => import('@pages/Queue'));
const Settings = lazy(() => import('@pages/Settings'));
const Login = lazy(() => import('@pages/Login'));
const NotFound = lazy(() => import('@pages/NotFound'));

// Route configuration for better maintainability
const routes = [
  { path: '/', element: Dashboard, protected: true },
  { path: '/dashboard', element: Dashboard, protected: true },
  { path: '/jobs', element: Jobs, protected: true },
  { path: '/applications', element: Applications, protected: true },
  { path: '/users', element: Users, protected: true },
  { path: '/analytics', element: Analytics, protected: true },
  { path: '/sources', element: Sources, protected: true },
  { path: '/queue', element: Queue, protected: true },
  { path: '/settings', element: Settings, protected: true },
  { path: '/login', element: Login, protected: false },
  { path: '/404', element: NotFound, protected: false },
];

function App() {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-secondary-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <Router>
      <div className="App">
        <AnimatePresence mode="wait">
          <Suspense
            fallback={
              <div className="min-h-screen flex items-center justify-center bg-secondary-50">
                <LoadingSpinner size="lg" />
              </div>
            }
          >
            <Routes>
              {/* Public routes */}
              <Route
                path="/login"
                element={
                  isAuthenticated ? (
                    <Navigate to="/dashboard" replace />
                  ) : (
                    <Login />
                  )
                }
              />

              {/* Protected routes wrapped in layout */}
              <Route
                path="/*"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <Routes>
                        {/* Dashboard - default route */}
                        <Route
                          path="/"
                          element={<Navigate to="/dashboard" replace />}
                        />
                        <Route path="/dashboard" element={<Dashboard />} />

                        {/* Main features */}
                        <Route path="/jobs/*" element={<Jobs />} />
                        <Route path="/applications/*" element={<Applications />} />
                        <Route path="/users/*" element={<Users />} />
                        <Route path="/analytics/*" element={<Analytics />} />
                        
                        {/* System management */}
                        <Route path="/sources/*" element={<Sources />} />
                        <Route path="/queue/*" element={<Queue />} />
                        <Route path="/settings/*" element={<Settings />} />

                        {/* Error pages */}
                        <Route path="/404" element={<NotFound />} />
                        <Route path="*" element={<Navigate to="/404" replace />} />
                      </Routes>
                    </Layout>
                  </ProtectedRoute>
                }
              />

              {/* Catch all - redirect to 404 */}
              <Route path="*" element={<Navigate to="/404" replace />} />
            </Routes>
          </Suspense>
        </AnimatePresence>
      </div>
    </Router>
  );
}

export default App;