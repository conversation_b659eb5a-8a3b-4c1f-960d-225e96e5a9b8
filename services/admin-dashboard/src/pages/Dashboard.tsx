import React from 'react';
import { useQuery } from '@apollo/client';
import { gql } from '@apollo/client';
import {
  ChartBarIcon,
  UsersIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

import { LoadingSpinner } from '@components/ui/LoadingSpinner';
import { StatsCard } from '@components/dashboard/StatsCard';
import { ApplicationChart } from '@components/dashboard/ApplicationChart';
import { RecentActivity } from '@components/dashboard/RecentActivity';
import { SystemStatus } from '@components/dashboard/SystemStatus';
import { QueueMonitor } from '@components/dashboard/QueueMonitor';

// GraphQL queries
const GET_DASHBOARD_DATA = gql`
  query GetDashboardData {
    platformStats {
      totalJobs
      totalApplications
      successRate
      averageProcessingTime
      applicationsByStatus {
        status
        count
        percentage
      }
      topCompanies {
        company
        jobCount
        applicationCount
        successRate
      }
    }
    queueStats {
      waiting
      active
      completed
      failed
      delayed
    }
    systemHealth {
      status
      services {
        name
        status
        responseTime
        lastChecked
      }
      timestamp
    }
  }
`;

const Dashboard: React.FC = () => {
  const { data, loading, error, refetch } = useQuery(GET_DASHBOARD_DATA, {
    pollInterval: 30000, // Poll every 30 seconds
    errorPolicy: 'partial',
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="w-12 h-12 text-error-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-secondary-900 mb-2">
          Failed to load dashboard data
        </h3>
        <p className="text-secondary-600 mb-4">
          {error.message}
        </p>
        <button onClick={() => refetch()} className="btn btn-primary">
          Try again
        </button>
      </div>
    );
  }

  const { platformStats, queueStats, systemHealth } = data || {};

  // Calculate additional metrics
  const pendingApplications = platformStats?.applicationsByStatus?.find(
    (status: any) => status.status === 'PENDING'
  )?.count || 0;

  const inProgressApplications = platformStats?.applicationsByStatus?.find(
    (status: any) => status.status === 'IN_PROGRESS'
  )?.count || 0;

  const successfulApplications = platformStats?.applicationsByStatus?.find(
    (status: any) => status.status === 'SUBMITTED'
  )?.count || 0;

  const failedApplications = platformStats?.applicationsByStatus?.find(
    (status: any) => status.status === 'FAILED'
  )?.count || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Dashboard</h1>
        <p className="text-secondary-600">
          Monitor your job platform performance and system health
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatsCard
            title="Total Jobs"
            value={platformStats?.totalJobs?.toLocaleString() || '0'}
            icon={BriefcaseIcon}
            color="primary"
            trend={{
              value: '+12%',
              isPositive: true,
              period: 'vs last month',
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <StatsCard
            title="Total Applications"
            value={platformStats?.totalApplications?.toLocaleString() || '0'}
            icon={DocumentTextIcon}
            color="info"
            trend={{
              value: '+8%',
              isPositive: true,
              period: 'vs last month',
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <StatsCard
            title="Success Rate"
            value={`${((platformStats?.successRate || 0) * 100).toFixed(1)}%`}
            icon={CheckCircleIcon}
            color="success"
            trend={{
              value: '+2.4%',
              isPositive: true,
              period: 'vs last month',
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <StatsCard
            title="Avg Processing Time"
            value={`${(platformStats?.averageProcessingTime || 0).toFixed(1)}s`}
            icon={ClockIcon}
            color="warning"
            trend={{
              value: '-5.2%',
              isPositive: true,
              period: 'vs last month',
            }}
          />
        </motion.div>
      </div>

      {/* Application Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <StatsCard
            title="Pending"
            value={pendingApplications.toLocaleString()}
            icon={ClockIcon}
            color="warning"
            subtitle="Waiting to process"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <StatsCard
            title="In Progress"
            value={inProgressApplications.toLocaleString()}
            icon={ChartBarIcon}
            color="info"
            subtitle="Currently processing"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <StatsCard
            title="Successful"
            value={successfulApplications.toLocaleString()}
            icon={CheckCircleIcon}
            color="success"
            subtitle="Successfully submitted"
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <StatsCard
            title="Failed"
            value={failedApplications.toLocaleString()}
            icon={XCircleIcon}
            color="error"
            subtitle="Failed to submit"
          />
        </motion.div>
      </div>

      {/* Charts and Monitoring */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Application Trends Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <ApplicationChart applicationsByStatus={platformStats?.applicationsByStatus} />
        </motion.div>

        {/* System Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
        >
          <SystemStatus health={systemHealth} />
        </motion.div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Queue Monitor */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.1 }}
        >
          <QueueMonitor queueStats={queueStats} />
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
        >
          <RecentActivity />
        </motion.div>
      </div>

      {/* Top Companies */}
      {platformStats?.topCompanies && platformStats.topCompanies.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.3 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-secondary-900">Top Companies</h3>
            <p className="text-sm text-secondary-600">Companies with the most job applications</p>
          </div>
          <div className="card-body">
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Company</th>
                    <th className="table-header-cell">Jobs</th>
                    <th className="table-header-cell">Applications</th>
                    <th className="table-header-cell">Success Rate</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {platformStats.topCompanies.slice(0, 10).map((company: any, index: number) => (
                    <tr key={company.company} className="table-row">
                      <td className="table-cell">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-secondary-900">
                            {company.company}
                          </span>
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className="text-sm text-secondary-900">
                          {company.jobCount.toLocaleString()}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className="text-sm text-secondary-900">
                          {company.applicationCount.toLocaleString()}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className={`status-indicator ${
                          company.successRate > 0.8 ? 'status-success' :
                          company.successRate > 0.6 ? 'status-warning' : 'status-error'
                        }`}>
                          {(company.successRate * 100).toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Dashboard;