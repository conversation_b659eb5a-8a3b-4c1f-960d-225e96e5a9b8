import {
  ApolloClient,
  InMemoryCache,
  createHttpLink,
  from,
  split,
} from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { createUploadLink } from 'apollo-upload-client';
import { getMainDefinition } from '@apollo/client/utilities';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { createClient } from 'graphql-ws';
import toast from 'react-hot-toast';

// Environment variables
const GRAPHQL_URI = import.meta.env.VITE_GRAPHQL_URI || 'http://localhost:3000/graphql';
const GRAPHQL_WS_URI = import.meta.env.VITE_GRAPHQL_WS_URI || 'ws://localhost:3000/graphql';
const TOKEN_KEY = import.meta.env.VITE_JWT_TOKEN_KEY || 'karmsakha_admin_token';

// Create HTTP link with upload support
const httpLink = createUploadLink({
  uri: GRAPHQL_URI,
  credentials: 'include',
});

// Create WebSocket link for subscriptions
const wsLink = import.meta.env.VITE_ENABLE_REAL_TIME_UPDATES === 'true'
  ? new GraphQLWsLink(
      createClient({
        url: GRAPHQL_WS_URI,
        connectionParams: () => {
          const token = localStorage.getItem(TOKEN_KEY);
          return {
            authorization: token ? `Bearer ${token}` : '',
          };
        },
        retryAttempts: 5,
        shouldRetry: () => true,
      })
    )
  : null;

// Authentication link
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem(TOKEN_KEY);
  
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
      'Apollo-Require-Preflight': 'true',
    },
  };
});

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  // Handle GraphQL errors
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path, extensions }) => {
      console.error(
        `GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`
      );

      // Handle specific error types
      switch (extensions?.code) {
        case 'UNAUTHENTICATED':
          // Clear token and redirect to login
          localStorage.removeItem(TOKEN_KEY);
          window.location.href = '/login';
          break;
        
        case 'FORBIDDEN':
          toast.error('You do not have permission to perform this action');
          break;
        
        case 'BAD_USER_INPUT':
          toast.error(`Invalid input: ${message}`);
          break;
        
        case 'INTERNAL_ERROR':
          toast.error('An internal error occurred. Please try again.');
          break;
        
        default:
          toast.error(message || 'An error occurred');
      }
    });
  }

  // Handle network errors
  if (networkError) {
    console.error(`Network error: ${networkError}`);
    
    if ('statusCode' in networkError) {
      const statusCode = networkError.statusCode;
      
      switch (statusCode) {
        case 401:
          localStorage.removeItem(TOKEN_KEY);
          window.location.href = '/login';
          break;
        
        case 403:
          toast.error('Access denied');
          break;
        
        case 500:
          toast.error('Server error. Please try again later.');
          break;
        
        case 503:
          toast.error('Service temporarily unavailable');
          break;
        
        default:
          toast.error('Network error. Please check your connection.');
      }
    } else {
      toast.error('Network error. Please check your connection.');
    }
  }
});

// Split link for HTTP and WebSocket
const splitLink = wsLink
  ? split(
      ({ query }) => {
        const definition = getMainDefinition(query);
        return (
          definition.kind === 'OperationDefinition' &&
          definition.operation === 'subscription'
        );
      },
      wsLink,
      from([errorLink, authLink, httpLink])
    )
  : from([errorLink, authLink, httpLink]);

// Cache configuration
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        // Pagination for jobs
        discoverJobs: {
          keyArgs: ['filters'],
          merge(existing = { edges: [], pageInfo: {} }, incoming) {
            return {
              ...incoming,
              edges: [...existing.edges, ...incoming.edges],
            };
          },
        },
        
        // Pagination for applications
        applications: {
          keyArgs: ['userId', 'filters'],
          merge(existing = { edges: [], pageInfo: {} }, incoming) {
            return {
              ...incoming,
              edges: [...existing.edges, ...incoming.edges],
            };
          },
        },
        
        // Cache user profile separately
        user: {
          keyArgs: ['id'],
        },
        
        // Cache job details
        job: {
          keyArgs: ['id'],
        },
        
        // Cache application details
        application: {
          keyArgs: ['id'],
        },
      },
    },
    
    User: {
      fields: {
        applications: {
          merge(existing = { edges: [], pageInfo: {} }, incoming) {
            return {
              ...incoming,
              edges: [...existing.edges, ...incoming.edges],
            };
          },
        },
      },
    },
    
    Job: {
      fields: {
        applications: {
          merge(existing = { edges: [], pageInfo: {} }, incoming) {
            return {
              ...incoming,
              edges: [...existing.edges, ...incoming.edges],
            };
          },
        },
        similarJobs: {
          merge(existing = [], incoming = []) {
            return incoming;
          },
        },
      },
    },
    
    Application: {
      keyFields: ['id'],
      fields: {
        automationLog: {
          merge(existing = [], incoming = []) {
            return incoming;
          },
        },
        screenshotUrls: {
          merge(existing = [], incoming = []) {
            return incoming;
          },
        },
      },
    },
    
    // Real-time data that shouldn't be cached too long
    QueueStats: {
      keyFields: false,
    },
    
    SystemHealth: {
      keyFields: false,
    },
  },
  
  // Possible types for fragments
  possibleTypes: {
    // Add interface implementations here if needed
  },
});

// Create Apollo Client
export const apolloClient = new ApolloClient({
  link: splitLink,
  cache,
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-and-network',
      notifyOnNetworkStatusChange: true,
    },
    query: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
  connectToDevTools: import.meta.env.DEV,
  name: 'Karmsakha Admin Dashboard',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
});

// Helper functions
export const getAuthToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

export const setAuthToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

export const removeAuthToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
  // Clear Apollo cache
  apolloClient.clearStore();
};

export const isAuthenticated = (): boolean => {
  const token = getAuthToken();
  if (!token) return false;
  
  try {
    // Basic JWT structure check
    const payload = JSON.parse(atob(token.split('.')[1]));
    const isExpired = payload.exp * 1000 < Date.now();
    return !isExpired;
  } catch {
    return false;
  }
};

// Utility function to handle optimistic updates
export const createOptimisticResponse = (
  operation: string,
  variables: any,
  typename: string
) => {
  const timestamp = new Date().toISOString();
  
  return {
    __typename: 'Mutation',
    [operation]: {
      __typename: typename,
      ...variables,
      id: `temp-${Date.now()}`,
      createdAt: timestamp,
      updatedAt: timestamp,
    },
  };
};

// Cache update helpers
export const updateCacheAfterMutation = (
  cache: any,
  result: any,
  queryToUpdate: any,
  operation: 'ADD' | 'UPDATE' | 'DELETE'
) => {
  try {
    const existingData = cache.readQuery(queryToUpdate);
    if (!existingData) return;

    let updatedData;
    
    switch (operation) {
      case 'ADD':
        updatedData = {
          ...existingData,
          [queryToUpdate.query.definitions[0].selectionSet.selections[0].name.value]: {
            ...existingData[queryToUpdate.query.definitions[0].selectionSet.selections[0].name.value],
            edges: [
              { node: result.data, cursor: result.data.id },
              ...existingData[queryToUpdate.query.definitions[0].selectionSet.selections[0].name.value].edges,
            ],
          },
        };
        break;
      
      case 'UPDATE':
        // Update logic here
        break;
      
      case 'DELETE':
        // Delete logic here
        break;
    }

    if (updatedData) {
      cache.writeQuery({
        ...queryToUpdate,
        data: updatedData,
      });
    }
  } catch (error) {
    console.warn('Cache update failed:', error);
  }
};

export default apolloClient;