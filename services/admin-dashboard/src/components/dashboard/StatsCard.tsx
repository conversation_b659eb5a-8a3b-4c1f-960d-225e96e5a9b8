import React from 'react';
import { motion } from 'framer-motion';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';
import { clsx } from 'clsx';

interface StatsCardProps {
  title: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  subtitle?: string;
  trend?: {
    value: string;
    isPositive: boolean;
    period: string;
  };
  className?: string;
}

const colorClasses = {
  primary: {
    icon: 'text-primary-600 bg-primary-100',
    accent: 'text-primary-600',
  },
  secondary: {
    icon: 'text-secondary-600 bg-secondary-100',
    accent: 'text-secondary-600',
  },
  success: {
    icon: 'text-success-600 bg-success-100',
    accent: 'text-success-600',
  },
  warning: {
    icon: 'text-warning-600 bg-warning-100',
    accent: 'text-warning-600',
  },
  error: {
    icon: 'text-error-600 bg-error-100',
    accent: 'text-error-600',
  },
  info: {
    icon: 'text-info-600 bg-info-100',
    accent: 'text-info-600',
  },
};

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon: Icon,
  color,
  subtitle,
  trend,
  className,
}) => {
  return (
    <motion.div
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
      className={clsx('card hover:shadow-soft-lg transition-all duration-200', className)}
    >
      <div className="card-body">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-secondary-600 truncate">
              {title}
            </p>
            <p className="text-2xl font-bold text-secondary-900 mt-1">
              {value}
            </p>
            {subtitle && (
              <p className="text-xs text-secondary-500 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          
          <div className={clsx(
            'flex-shrink-0 p-3 rounded-lg',
            colorClasses[color].icon
          )}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
        
        {trend && (
          <div className="flex items-center mt-4 pt-4 border-t border-secondary-200">
            <div className={clsx(
              'flex items-center text-sm',
              trend.isPositive ? 'text-success-600' : 'text-error-600'
            )}>
              {trend.isPositive ? (
                <ArrowUpIcon className="w-4 h-4 mr-1" />
              ) : (
                <ArrowDownIcon className="w-4 h-4 mr-1" />
              )}
              <span className="font-medium">{trend.value}</span>
            </div>
            <span className="text-sm text-secondary-500 ml-2">
              {trend.period}
            </span>
          </div>
        )}
      </div>
    </motion.div>
  );
};