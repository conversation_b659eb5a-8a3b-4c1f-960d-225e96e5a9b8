import React from 'react';
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetError }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50 px-4">
      <div className="max-w-md w-full">
        <div className="card text-center">
          <div className="card-body">
            <div className="flex justify-center mb-4">
              <ExclamationTriangleIcon className="w-12 h-12 text-error-500" />
            </div>
            
            <h1 className="text-xl font-semibold text-secondary-900 mb-2">
              Something went wrong
            </h1>
            
            <p className="text-secondary-600 mb-6">
              An unexpected error occurred. Please try refreshing the page.
            </p>
            
            {import.meta.env.DEV && (
              <details className="mb-6 text-left">
                <summary className="cursor-pointer text-sm text-secondary-500 hover:text-secondary-700">
                  Error details
                </summary>
                <pre className="mt-2 text-xs bg-secondary-100 p-3 rounded overflow-auto">
                  {error.message}
                  {error.stack && `\n\n${error.stack}`}
                </pre>
              </details>
            )}
            
            <div className="space-y-3">
              <button
                onClick={resetError}
                className="btn btn-primary w-full"
              >
                <ArrowPathIcon className="w-4 h-4 mr-2" />
                Try again
              </button>
              
              <button
                onClick={() => window.location.reload()}
                className="btn btn-outline w-full"
              >
                Refresh page
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};