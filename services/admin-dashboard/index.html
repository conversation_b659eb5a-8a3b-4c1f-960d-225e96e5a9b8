<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- Primary Meta Tags -->
  <title>Karmsakha Admin Dashboard</title>
  <meta name="title" content="Karmsakha Admin Dashboard" />
  <meta name="description" content="Admin dashboard for Karmsakha job platform - monitor, manage and analyze job applications and user activity" />
  <meta name="keywords" content="admin, dashboard, jobs, management, analytics, monitoring" />
  <meta name="author" content="Karmsakha Team" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://admin.karmsakha.com" />
  <meta property="og:title" content="Karmsakha Admin Dashboard" />
  <meta property="og:description" content="Admin dashboard for Karmsakha job platform - monitor, manage and analyze job applications and user activity" />
  <meta property="og:image" content="/og-image.png" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://admin.karmsakha.com" />
  <meta property="twitter:title" content="Karmsakha Admin Dashboard" />
  <meta property="twitter:description" content="Admin dashboard for Karmsakha job platform - monitor, manage and analyze job applications and user activity" />
  <meta property="twitter:image" content="/twitter-image.png" />
  
  <!-- Theme and Mobile -->
  <meta name="theme-color" content="#1f2937" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="Karmsakha Admin" />
  
  <!-- Icons -->
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  <link rel="mask-icon" href="/mask-icon.svg" color="#1f2937" />
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- Critical CSS for performance -->
  <style>
    /* Critical CSS for above-the-fold content */
    *,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
    ::before,::after{--tw-content:''}
    html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
    body{margin:0;line-height:inherit}
    
    /* Loading spinner */
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #ffffff20;
      border-top: 4px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      color: white;
      margin-top: 1rem;
      font-weight: 500;
      font-size: 1.125rem;
    }
    
    /* Hide loading when app is ready */
    .app-loaded .loading-container {
      display: none;
    }
  </style>
  
  <!-- Preload critical fonts -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'" />
  <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" /></noscript>
  
  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
  
  <!-- Performance monitoring -->
  <script>
    window.addEventListener('load', () => {
      // Mark app as loaded for CSS
      document.body.classList.add('app-loaded');
      
      // Performance metrics
      if ('performance' in window) {
        setTimeout(() => {
          const perfData = performance.getEntriesByType('navigation')[0];
          if (perfData) {
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.fetchStart + 'ms');
          }
        }, 0);
      }
    });
  </script>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading-container">
    <div class="text-center">
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading Karmsakha Admin...</div>
    </div>
  </div>
  
  <!-- Root Element -->
  <div id="root"></div>
  
  <!-- Fallback for no JavaScript -->
  <noscript>
    <div style="text-align: center; padding: 2rem; font-family: sans-serif;">
      <h1>JavaScript Required</h1>
      <p>This application requires JavaScript to run. Please enable JavaScript in your browser.</p>
    </div>
  </noscript>
  
  <!-- Main Application Script -->
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>