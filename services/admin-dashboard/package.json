{"name": "karmsakha-admin-dashboard", "version": "1.0.0", "description": "React Admin Dashboard for Karmsakha job platform - monitoring and management interface", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "typecheck": "tsc --noEmit", "analyze": "npm run build && npx vite-bundle-analyzer dist", "lighthouse": "lhci autorun", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "pre-commit": "lint-staged"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "@apollo/client": "^3.8.7", "graphql": "^16.8.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.4", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-virtual": "^2.10.4", "react-intersection-observer": "^9.5.3", "react-error-boundary": "^4.0.11", "socket.io-client": "^4.7.4", "lodash-es": "^4.17.21", "axios": "^1.6.2"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "vite": "^5.0.0", "typescript": "^5.2.2", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "vite-plugin-pwa": "^0.17.4", "workbox-window": "^7.0.0", "@lhci/cli": "^0.12.0", "vite-bundle-analyzer": "^0.7.0", "lint-staged": "^15.1.0", "husky": "^8.0.3", "vite-plugin-eslint": "^1.8.1"}, "engines": {"node": ">=18.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,md}": ["prettier --write"]}}