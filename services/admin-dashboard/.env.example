# Application Configuration
VITE_APP_NAME=Karmsakha Admin Dashboard
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Admin dashboard for Karmsakha job platform

# API Configuration
VITE_GRAPHQL_URI=http://localhost:3000/graphql
VITE_GRAPHQL_WS_URI=ws://localhost:3000/graphql
VITE_API_BASE_URL=http://localhost:3000

# Authentication
VITE_AUTH_ENABLED=true
VITE_JWT_TOKEN_KEY=karmsakha_admin_token
VITE_REFRESH_TOKEN_KEY=karmsakha_admin_refresh_token

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_REAL_TIME_UPDATES=true
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_PWA=true

# Performance Configuration
VITE_ENABLE_QUERY_CACHE=true
VITE_CACHE_DURATION=300000
VITE_POLLING_INTERVAL=30000
VITE_DEBOUNCE_DELAY=300

# Pagination
VITE_DEFAULT_PAGE_SIZE=25
VITE_MAX_PAGE_SIZE=100

# Charts and Visualization
VITE_CHART_REFRESH_INTERVAL=60000
VITE_ENABLE_CHART_ANIMATIONS=true
VITE_CHART_THEME=light

# File Upload
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# External Services
VITE_ENABLE_SENTRY=false
VITE_SENTRY_DSN=
VITE_GOOGLE_ANALYTICS_ID=
VITE_HOTJAR_ID=

# WebSocket Configuration
VITE_WS_RECONNECT_ATTEMPTS=5
VITE_WS_RECONNECT_INTERVAL=3000

# Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_LOG_LEVEL=info

# Theme Configuration
VITE_DEFAULT_THEME=light
VITE_PRIMARY_COLOR=#3b82f6
VITE_SECONDARY_COLOR=#64748b

# Date and Time
VITE_DEFAULT_TIMEZONE=UTC
VITE_DATE_FORMAT=yyyy-MM-dd
VITE_TIME_FORMAT=HH:mm:ss

# Lighthouse Optimization
VITE_PRELOAD_CRITICAL_RESOURCES=true
VITE_ENABLE_RESOURCE_HINTS=true
VITE_OPTIMIZE_IMAGES=true

# Development
VITE_ENABLE_DEBUG=false
VITE_MOCK_API=false
VITE_SHOW_REDUX_DEVTOOLS=false

# Build Configuration
VITE_BUILD_ANALYZE=false
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true