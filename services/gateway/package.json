{"name": "karmsakha-graphql-gateway", "version": "1.0.0", "description": "GraphQL Gateway for Karmsakha job platform - unified API interface", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "typecheck": "tsc --noEmit", "codegen": "graphql-codegen --config codegen.ts", "schema:download": "rover graph fetch karmsakha@main --output schema.graphql"}, "keywords": ["graphql", "gateway", "jobs", "api", "microservices", "apollo"], "dependencies": {"@apollo/server": "^4.9.5", "@apollo/subgraph": "^2.5.6", "@apollo/gateway": "^2.5.6", "@apollo/federation": "^0.38.1", "apollo-server-express": "^3.12.1", "express": "^4.18.2", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "graphql-scalars": "^1.22.4", "graphql-upload": "^16.0.2", "dataloader": "^2.2.2", "axios": "^1.6.2", "redis": "^4.6.10", "ioredis": "^5.3.2", "winston": "^3.11.0", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "zod": "^3.22.4", "uuid": "^9.0.1", "lodash": "^4.17.21", "node-cron": "^3.0.3", "prom-client": "^15.0.0", "apollo-server-plugin-response-cache": "^3.8.2", "apollo-cache-redis": "^6.1.0", "graphql-depth-limit": "^1.1.0", "graphql-query-complexity": "^0.12.0", "graphql-rate-limit": "^2.0.1"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.16", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/graphql-upload": "^16.0.5", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-resolvers": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.0.1", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.4.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "author": "Karmsakha Team", "license": "MIT"}