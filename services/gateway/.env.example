# Server Configuration
PORT=3000
NODE_ENV=development
SERVICE_NAME=karmsakha-graphql-gateway

# GraphQL Configuration
GRAPHQL_PATH=/graphql
GRAPHQL_PLAYGROUND=true
GRAPHQL_INTROSPECTION=true
GRAPHQL_DEBUG=true

# Microservices URLs
INGESTOR_SERVICE_URL=http://localhost:3001
RANKER_SERVICE_URL=http://localhost:3002
STAGEHAND_SERVICE_URL=http://localhost:3003

# Database Configuration
DATABASE_URL=postgresql://karmsakha_user:secure_password@localhost:5432/karmsakha_db

# Redis Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=300
CACHE_PREFIX=gql:

# Authentication & Security
JWT_SECRET=your-jwt-secret-key-for-gateway
JWT_EXPIRY=24h
API_KEY=your-gateway-api-key
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL=true

# Query Complexity & Depth
MAX_QUERY_DEPTH=10
MAX_QUERY_COMPLEXITY=1000
QUERY_TIMEOUT_MS=30000

# Caching
ENABLE_RESPONSE_CACHE=true
RESPONSE_CACHE_TTL=300
ENABLE_QUERY_CACHE=true
QUERY_CACHE_TTL=60

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,txt
UPLOAD_PATH=/tmp/uploads

# Monitoring & Metrics
METRICS_PORT=9000
LOG_LEVEL=info
ENABLE_TRACING=true
ENABLE_METRICS=true

# External Services Timeouts
INGESTOR_TIMEOUT=30000
RANKER_TIMEOUT=30000
STAGEHAND_TIMEOUT=60000

# DataLoader Configuration
DATALOADER_CACHE=true
DATALOADER_BATCH_SCHEDULE_FN=process.nextTick

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Feature Flags
ENABLE_SUBSCRIPTIONS=true
ENABLE_FILE_UPLOADS=true
ENABLE_ANALYTICS=true
ENABLE_AUDIT_LOGS=true

# Notification Configuration
WEBHOOK_URL=
SLACK_WEBHOOK_URL=

# Health Check
HEALTH_CHECK_INTERVAL=30000

# CORS Configuration
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# Apollo Studio (Optional)
APOLLO_KEY=
APOLLO_GRAPH_REF=karmsakha@main
APOLLO_SCHEMA_REPORTING=false

# Development
HOT_RELOAD=true
WATCH_FILES=true