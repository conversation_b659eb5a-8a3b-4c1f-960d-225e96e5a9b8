import { BaseService, ServiceResponse, PaginatedResponse } from './base-service';
import config from '@/config';

export interface JobRecommendation {
  id: string;
  title: string;
  company: string;
  location?: string;
  country: string;
  isRemote: boolean;
  salaryMin?: number;
  salaryMax?: number;
  currency?: string;
  experienceLevel?: string;
  skills: string[];
  postedAt: string;
  url: string;
  similarity: number;
  rankingScore?: number;
  rankingBreakdown?: RankingBreakdown;
}

export interface RankingBreakdown {
  vectorSimilarity: number;
  skillsMatch: number;
  experienceMatch: number;
  locationMatch: number;
  salaryMatch: number;
  overallScore: number;
}

export interface RankingFilters {
  country?: string;
  isRemote?: boolean;
  experienceLevel?: string;
  minSalary?: number;
  maxSalary?: number;
  skills?: string[];
  location?: string;
  excludeAppliedJobs?: boolean;
}

export interface UserRankingRequest {
  userId: string;
  filters?: RankingFilters;
  limit?: number;
  useCache?: boolean;
}

export interface QueryRankingRequest {
  query: string;
  filters?: RankingFilters;
  limit?: number;
  useCache?: boolean;
}

export interface RankingResponse {
  userId?: string;
  query?: string;
  totalJobs: number;
  jobs: JobRecommendation[];
  filtersApplied: Record<string, any>;
  rankingMetadata: {
    algorithm: string;
    cached: boolean;
    processingTime: number;
    timestamp: string;
  };
}

export interface RankingStats {
  totalRankings: number;
  cacheHitRate: number;
  averageResponseTime: number;
  rankingsByPlatform: Record<string, number>;
  successRate: number;
}

export class RankerService extends BaseService {
  constructor() {
    super('ranker-service', config.services.rankerUrl, config.timeouts.ranker);
  }

  /**
   * Get personalized job recommendations for a user
   * Main implementation for /discover endpoint
   */
  async getJobRecommendations(request: UserRankingRequest): Promise<RankingResponse> {
    const response = await this.post<RankingResponse>('/api/v1/ranking/user', request);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get job recommendations');
    }

    return response.data;
  }

  /**
   * Get job recommendations based on text query
   */
  async searchJobsByQuery(request: QueryRankingRequest): Promise<RankingResponse> {
    const response = await this.post<RankingResponse>('/api/v1/ranking/query', request);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to search jobs by query');
    }

    return response.data;
  }

  /**
   * Get ranking statistics
   */
  async getRankingStats(): Promise<RankingStats> {
    const response = await this.get<RankingStats>('/api/v1/ranking/stats');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get ranking stats');
    }

    return response.data;
  }

  /**
   * Clear ranking cache
   */
  async clearCache(): Promise<boolean> {
    const response = await this.delete('/api/v1/ranking/cache');
    return response.success;
  }

  /**
   * Clear cache for specific user
   */
  async clearUserCache(userId: string): Promise<{ deletedCount: number }> {
    const response = await this.delete<{ deletedCount: number }>(`/api/v1/ranking/cache/user/${userId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to clear user cache');
    }

    return response.data;
  }

  /**
   * Get ranking service configuration
   */
  async getConfig(): Promise<Record<string, any>> {
    const response = await this.get<Record<string, any>>('/api/v1/ranking/config');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get ranking config');
    }

    return response.data;
  }

  /**
   * Get job details by ID
   */
  async getJob(jobId: string): Promise<JobRecommendation> {
    const response = await this.get<JobRecommendation>(`/api/v1/jobs/${jobId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Job not found');
    }

    return response.data;
  }

  /**
   * Search jobs with filters and pagination
   */
  async searchJobs(params: {
    page?: number;
    limit?: number;
    country?: string;
    isRemote?: boolean;
    experienceLevel?: string;
    hasEmbedding?: boolean;
  }): Promise<{
    total: number;
    jobs: JobRecommendation[];
    page: number;
    limit: number;
  }> {
    const queryParams = this.buildQueryParams(params);
    const response = await this.get<{
      total: number;
      jobs: JobRecommendation[];
      page: number;
      limit: number;
    }>(`/api/v1/jobs?${queryParams}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to search jobs');
    }

    return response.data;
  }

  /**
   * Generate or regenerate embedding for a job
   */
  async generateJobEmbedding(jobId: string): Promise<{
    jobId: string;
    embeddingDimensions: number;
    textLength: number;
    timestamp: string;
  }> {
    const response = await this.post<{
      jobId: string;
      embeddingDimensions: number;
      textLength: number;
      timestamp: string;
    }>(`/api/v1/jobs/${jobId}/embedding`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to generate job embedding');
    }

    return response.data;
  }

  /**
   * Find similar jobs to a specific job
   */
  async findSimilarJobs(
    jobId: string,
    options: { limit?: number; threshold?: number } = {}
  ): Promise<{
    jobId: string;
    similarJobs: Array<JobRecommendation & { similarity: number }>;
    totalFound: number;
    threshold: number;
  }> {
    const params = this.buildQueryParams({
      limit: options.limit || 10,
      threshold: options.threshold || 0.8,
    });

    const response = await this.get<{
      jobId: string;
      similarJobs: Array<JobRecommendation & { similarity: number }>;
      totalFound: number;
      threshold: number;
    }>(`/api/v1/jobs/${jobId}/similar?${params}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to find similar jobs');
    }

    return response.data;
  }

  /**
   * Get embedding statistics
   */
  async getEmbeddingStats(): Promise<{
    totalJobs: number;
    jobsWithEmbeddings: number;
    embeddingCoverage: number;
    averageEmbeddingQuality: number;
    lastEmbeddingGenerated?: string;
    embeddingModel: string;
    embeddingDimensions: number;
  }> {
    const response = await this.get<{
      totalJobs: number;
      jobsWithEmbeddings: number;
      embeddingCoverage: number;
      averageEmbeddingQuality: number;
      lastEmbeddingGenerated?: string;
      embeddingModel: string;
      embeddingDimensions: number;
    }>('/api/v1/jobs/stats/embeddings');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get embedding stats');
    }

    return response.data;
  }

  /**
   * Get user profile and generate/update user embedding
   */
  async getUserProfile(userId: string): Promise<{
    id: string;
    name: string;
    location?: string;
    country: string;
    preferredLocations?: string[];
    remotePreference: boolean;
    skills?: string[];
    experienceYears?: number;
    currentTitle?: string;
    preferredSalaryMin?: number;
    preferredSalaryMax?: number;
    preferredCurrency?: string;
    preferences?: Record<string, any>;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  }> {
    const response = await this.get<{
      id: string;
      name: string;
      location?: string;
      country: string;
      preferredLocations?: string[];
      remotePreference: boolean;
      skills?: string[];
      experienceYears?: number;
      currentTitle?: string;
      preferredSalaryMin?: number;
      preferredSalaryMax?: number;
      preferredCurrency?: string;
      preferences?: Record<string, any>;
      isActive: boolean;
      createdAt: string;
      updatedAt: string;
    }>(`/api/v1/users/${userId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'User not found');
    }

    return response.data;
  }

  /**
   * Update user embedding
   */
  async updateUserEmbedding(
    userId: string,
    data: {
      resumeText?: string;
      forceRegenerate?: boolean;
    }
  ): Promise<{
    userId: string;
    embeddingDimensions: number;
    textLength: number;
    timestamp: string;
  }> {
    const response = await this.post<{
      userId: string;
      embeddingDimensions: number;
      textLength: number;
      timestamp: string;
    }>(`/api/v1/users/${userId}/embedding`, data);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to update user embedding');
    }

    return response.data;
  }

  /**
   * Get user preferences
   */
  async getUserPreferences(userId: string): Promise<{
    userId: string;
    preferences: Record<string, any>;
    timestamp: string;
  }> {
    const response = await this.get<{
      userId: string;
      preferences: Record<string, any>;
      timestamp: string;
    }>(`/api/v1/users/${userId}/preferences`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get user preferences');
    }

    return response.data;
  }

  /**
   * Calculate matching score between user and job
   */
  async calculateMatchingScore(userId: string, jobId: string): Promise<{
    userId: string;
    jobId: string;
    overallSimilarity: number;
    matchingBreakdown: {
      vectorSimilarity: number;
      skillsMatch: number;
      locationMatch: number;
    };
    recommendation: 'high' | 'medium' | 'low';
    timestamp: string;
  }> {
    const response = await this.get<{
      userId: string;
      jobId: string;
      overallSimilarity: number;
      matchingBreakdown: {
        vectorSimilarity: number;
        skillsMatch: number;
        locationMatch: number;
      };
      recommendation: 'high' | 'medium' | 'low';
      timestamp: string;
    }>(`/api/v1/users/${userId}/matching-score/${jobId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to calculate matching score');
    }

    return response.data;
  }
}

export default RankerService;