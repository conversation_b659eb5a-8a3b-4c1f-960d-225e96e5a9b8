import { BaseService, ServiceResponse } from './base-service';
import config from '@/config';

export interface JobSource {
  id: string;
  name: string;
  url: string;
  type: 'api' | 'scraper' | 'feed';
  country: string;
  isActive: boolean;
  lastScrapedAt?: string;
  nextScheduledRun?: string;
  totalJobsIngested: number;
  successRate: number;
  averageJobsPerRun: number;
  rateLimitInfo?: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
  configuration: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface IngestionJob {
  id: string;
  sourceId: string;
  sourceName: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  jobsFound: number;
  jobsIngested: number;
  jobsUpdated: number;
  jobsSkipped: number;
  errorCount: number;
  errors: IngestionError[];
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface IngestionError {
  timestamp: string;
  type: string;
  message: string;
  url?: string;
  details?: Record<string, any>;
}

export interface IngestionStats {
  totalJobs: number;
  jobsToday: number;
  jobsThisWeek: number;
  jobsThisMonth: number;
  activeSources: number;
  averageJobsPerHour: number;
  successRate: number;
  topSources: Array<{
    sourceName: string;
    jobCount: number;
    percentage: number;
  }>;
  topCountries: Array<{
    country: string;
    jobCount: number;
    percentage: number;
  }>;
  topCompanies: Array<{
    company: string;
    jobCount: number;
    percentage: number;
  }>;
  recentIngestions: IngestionJob[];
}

export interface JobData {
  id: string;
  externalId: string;
  source: string;
  url: string;
  title: string;
  company: string;
  location?: string;
  country: string;
  isRemote: boolean;
  employmentType?: string;
  experienceLevel?: string;
  salaryMin?: number;
  salaryMax?: number;
  currency?: string;
  description?: string;
  requirements?: string;
  skills: string[];
  atsPlatform?: string;
  applyUrl?: string;
  applicationMethod: string;
  postedAt: string;
  expiresAt?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface JobFilters {
  source?: string;
  country?: string;
  location?: string;
  isRemote?: boolean;
  isActive?: boolean;
  company?: string;
  skills?: string[];
  postedAfter?: string;
  postedBefore?: string;
}

export class IngestorService extends BaseService {
  constructor() {
    super('ingestor-service', config.services.ingestorUrl, config.timeouts.ingestor);
  }

  /**
   * Get ingestion statistics
   */
  async getIngestionStats(): Promise<IngestionStats> {
    const response = await this.get<IngestionStats>('/api/v1/stats');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get ingestion stats');
    }

    return response.data;
  }

  /**
   * Get all job sources
   */
  async getJobSources(): Promise<JobSource[]> {
    const response = await this.get<{ sources: JobSource[] }>('/api/v1/sources');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get job sources');
    }

    return response.data.sources;
  }

  /**
   * Get job source by ID
   */
  async getJobSource(sourceId: string): Promise<JobSource> {
    const response = await this.get<JobSource>(`/api/v1/sources/${sourceId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Job source not found');
    }

    return response.data;
  }

  /**
   * Get active job sources
   */
  async getActiveJobSources(): Promise<JobSource[]> {
    const sources = await this.getJobSources();
    return sources.filter(source => source.isActive);
  }

  /**
   * Get ingestion jobs (history)
   */
  async getIngestionJobs(options: {
    sourceId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    jobs: IngestionJob[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const params = this.buildQueryParams({
      sourceId: options.sourceId,
      status: options.status,
      limit: options.limit || 50,
      offset: options.offset || 0,
    });

    const response = await this.get<{
      jobs: IngestionJob[];
      total: number;
      limit: number;
      offset: number;
    }>(`/api/v1/ingestion-jobs?${params}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get ingestion jobs');
    }

    return response.data;
  }

  /**
   * Get specific ingestion job
   */
  async getIngestionJob(jobId: string): Promise<IngestionJob> {
    const response = await this.get<IngestionJob>(`/api/v1/ingestion-jobs/${jobId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Ingestion job not found');
    }

    return response.data;
  }

  /**
   * Get jobs from the ingestor
   */
  async getJobs(options: {
    filters?: JobFilters;
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<{
    jobs: JobData[];
    total: number;
    limit: number;
    offset: number;
    filters: JobFilters;
  }> {
    const params = this.buildQueryParams({
      ...options.filters,
      limit: options.limit || 50,
      offset: options.offset || 0,
      sortBy: options.sortBy,
      sortOrder: options.sortOrder,
    });

    const response = await this.get<{
      jobs: JobData[];
      total: number;
      limit: number;
      offset: number;
      filters: JobFilters;
    }>(`/api/v1/jobs?${params}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get jobs');
    }

    return response.data;
  }

  /**
   * Get job by ID
   */
  async getJob(jobId: string): Promise<JobData> {
    const response = await this.get<JobData>(`/api/v1/jobs/${jobId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Job not found');
    }

    return response.data;
  }

  /**
   * Search jobs by text
   */
  async searchJobs(query: string, options: {
    filters?: JobFilters;
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    jobs: JobData[];
    total: number;
    query: string;
    limit: number;
    offset: number;
  }> {
    const params = this.buildQueryParams({
      q: query,
      ...options.filters,
      limit: options.limit || 50,
      offset: options.offset || 0,
    });

    const response = await this.get<{
      jobs: JobData[];
      total: number;
      query: string;
      limit: number;
      offset: number;
    }>(`/api/v1/jobs/search?${params}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to search jobs');
    }

    return response.data;
  }

  /**
   * Trigger manual ingestion for a source
   */
  async triggerIngestion(sourceId: string, options?: {
    priority?: 'low' | 'normal' | 'high';
    maxJobs?: number;
  }): Promise<{
    jobId: string;
    sourceId: string;
    status: string;
    estimatedDuration: number;
  }> {
    const response = await this.post<{
      jobId: string;
      sourceId: string;
      status: string;
      estimatedDuration: number;
    }>(`/api/v1/sources/${sourceId}/ingest`, options);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to trigger ingestion');
    }

    return response.data;
  }

  /**
   * Pause source ingestion
   */
  async pauseSource(sourceId: string): Promise<boolean> {
    const response = await this.post(`/api/v1/sources/${sourceId}/pause`);
    return response.success;
  }

  /**
   * Resume source ingestion
   */
  async resumeSource(sourceId: string): Promise<boolean> {
    const response = await this.post(`/api/v1/sources/${sourceId}/resume`);
    return response.success;
  }

  /**
   * Get ingestion queue status
   */
  async getQueueStatus(): Promise<{
    pending: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    totalProcessed: number;
    processingRate: number;
  }> {
    const response = await this.get<{
      pending: number;
      active: number;
      completed: number;
      failed: number;
      delayed: number;
      totalProcessed: number;
      processingRate: number;
    }>('/api/v1/queue/status');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get queue status');
    }

    return response.data;
  }

  /**
   * Get filter options for jobs
   */
  async getJobFilterOptions(): Promise<{
    countries: string[];
    locations: string[];
    companies: string[];
    sources: string[];
    skills: string[];
    employmentTypes: string[];
    experienceLevels: string[];
  }> {
    const response = await this.get<{
      countries: string[];
      locations: string[];
      companies: string[];
      sources: string[];
      skills: string[];
      employmentTypes: string[];
      experienceLevels: string[];
    }>('/api/v1/jobs/filter-options');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get filter options');
    }

    return response.data;
  }

  /**
   * Get recent jobs (for feeds, notifications)
   */
  async getRecentJobs(hours: number = 24): Promise<JobData[]> {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000).toISOString();
    
    const result = await this.getJobs({
      filters: { postedAfter: since },
      limit: 1000,
      sortBy: 'created_at',
      sortOrder: 'desc',
    });

    return result.jobs;
  }

  /**
   * Get jobs by company
   */
  async getJobsByCompany(company: string, options: {
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    jobs: JobData[];
    company: string;
    total: number;
  }> {
    const result = await this.getJobs({
      filters: { company },
      limit: options.limit || 50,
      offset: options.offset || 0,
    });

    return {
      jobs: result.jobs,
      company,
      total: result.total,
    };
  }

  /**
   * Get jobs by country
   */
  async getJobsByCountry(country: string, options: {
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    jobs: JobData[];
    country: string;
    total: number;
  }> {
    const result = await this.getJobs({
      filters: { country },
      limit: options.limit || 50,
      offset: options.offset || 0,
    });

    return {
      jobs: result.jobs,
      country,
      total: result.total,
    };
  }

  /**
   * Get service health
   */
  async getServiceHealth(): Promise<{
    status: string;
    ingestionQueue: {
      active: number;
      waiting: number;
      failed: number;
    };
    activeSources: number;
    lastIngestion: string;
    uptime: number;
  }> {
    const response = await this.get<{
      status: string;
      ingestionQueue: {
        active: number;
        waiting: number;
        failed: number;
      };
      activeSources: number;
      lastIngestion: string;
      uptime: number;
    }>('/health');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get service health');
    }

    return response.data;
  }
}

export default IngestorService;