import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import config from '@/config';
import { logger } from '@/utils/logger';

export interface ServiceError extends Error {
  code: string;
  statusCode?: number;
  details?: any;
}

export class BaseServiceError extends Error implements ServiceError {
  public readonly code: string;
  public readonly statusCode?: number;
  public readonly details?: any;

  constructor(message: string, code: string, statusCode?: number, details?: any) {
    super(message);
    this.name = 'ServiceError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export abstract class BaseService {
  protected client: AxiosInstance;
  protected serviceName: string;
  protected baseURL: string;
  protected timeout: number;

  constructor(serviceName: string, baseURL: string, timeout: number = 30000) {
    this.serviceName = serviceName;
    this.baseURL = baseURL;
    this.timeout = timeout;

    this.client = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': `${config.serviceName}/1.0.0`,
        'X-API-Key': config.auth.apiKey,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const requestId = this.generateRequestId();
        config.headers['X-Request-ID'] = requestId;
        
        logger.debug(`${this.serviceName} request`, {
          requestId,
          method: config.method?.toUpperCase(),
          url: config.url,
          params: config.params,
        });

        return config;
      },
      (error) => {
        logger.error(`${this.serviceName} request error`, { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        const requestId = response.config.headers['X-Request-ID'];
        
        logger.debug(`${this.serviceName} response`, {
          requestId,
          status: response.status,
          statusText: response.statusText,
          responseTime: this.calculateResponseTime(response),
        });

        return response;
      },
      (error) => {
        const requestId = error.config?.headers['X-Request-ID'];
        
        if (error.response) {
          // Server responded with error status
          logger.error(`${this.serviceName} response error`, {
            requestId,
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data,
            url: error.config?.url,
          });

          throw new BaseServiceError(
            error.response.data?.message || error.response.statusText,
            error.response.data?.error?.code || 'SERVICE_ERROR',
            error.response.status,
            error.response.data
          );
        } else if (error.request) {
          // Request was made but no response received
          logger.error(`${this.serviceName} network error`, {
            requestId,
            message: error.message,
            code: error.code,
            url: error.config?.url,
          });

          throw new BaseServiceError(
            `Network error: ${error.message}`,
            'NETWORK_ERROR',
            undefined,
            { code: error.code }
          );
        } else {
          // Something else happened
          logger.error(`${this.serviceName} request setup error`, {
            requestId,
            message: error.message,
          });

          throw new BaseServiceError(
            `Request setup error: ${error.message}`,
            'REQUEST_ERROR'
          );
        }
      }
    );
  }

  protected async get<T>(
    endpoint: string,
    params?: any,
    options?: AxiosRequestConfig
  ): Promise<ServiceResponse<T>> {
    const response = await this.client.get(endpoint, { params, ...options });
    return this.processResponse<T>(response);
  }

  protected async post<T>(
    endpoint: string,
    data?: any,
    options?: AxiosRequestConfig
  ): Promise<ServiceResponse<T>> {
    const response = await this.client.post(endpoint, data, options);
    return this.processResponse<T>(response);
  }

  protected async put<T>(
    endpoint: string,
    data?: any,
    options?: AxiosRequestConfig
  ): Promise<ServiceResponse<T>> {
    const response = await this.client.put(endpoint, data, options);
    return this.processResponse<T>(response);
  }

  protected async patch<T>(
    endpoint: string,
    data?: any,
    options?: AxiosRequestConfig
  ): Promise<ServiceResponse<T>> {
    const response = await this.client.patch(endpoint, data, options);
    return this.processResponse<T>(response);
  }

  protected async delete<T>(
    endpoint: string,
    options?: AxiosRequestConfig
  ): Promise<ServiceResponse<T>> {
    const response = await this.client.delete(endpoint, options);
    return this.processResponse<T>(response);
  }

  private processResponse<T>(response: AxiosResponse): ServiceResponse<T> {
    // Handle different response formats from services
    if (response.data.success !== undefined) {
      // Standard service response format
      return response.data;
    }

    // Direct data response
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toISOString(),
    };
  }

  protected buildQueryParams(filters: any): string {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => params.append(key, String(item)));
        } else {
          params.append(key, String(value));
        }
      }
    });

    return params.toString();
  }

  protected async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/health', { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      logger.warn(`${this.serviceName} health check failed`, { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return false;
    }
  }

  protected async readinessCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/health/ready', { timeout: 5000 });
      return response.status === 200 && response.data.status === 'ready';
    } catch (error) {
      logger.warn(`${this.serviceName} readiness check failed`, { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return false;
    }
  }

  public async getServiceHealth(): Promise<{
    name: string;
    status: string;
    responseTime?: number;
    lastChecked: string;
  }> {
    const startTime = Date.now();
    const lastChecked = new Date().toISOString();

    try {
      const isHealthy = await this.healthCheck();
      const responseTime = Date.now() - startTime;

      return {
        name: this.serviceName,
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        lastChecked,
      };
    } catch (error) {
      return {
        name: this.serviceName,
        status: 'error',
        lastChecked,
      };
    }
  }

  private generateRequestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateResponseTime(response: AxiosResponse): number | undefined {
    const requestTime = response.config.metadata?.startTime;
    return requestTime ? Date.now() - requestTime : undefined;
  }

  public getBaseURL(): string {
    return this.baseURL;
  }

  public getServiceName(): string {
    return this.serviceName;
  }
}

export default BaseService;