import { BaseService, ServiceResponse } from './base-service';
import config from '@/config';

export interface ApplicationRequest {
  userId: string;
  jobId: string;
  priority: 'low' | 'normal' | 'high';
  customCoverLetter?: string;
  customResumeUrl?: string;
  scheduledAt?: string;
  metadata?: Record<string, any>;
}

export interface Application {
  id: string;
  userId: string;
  jobId: string;
  status: ApplicationStatus;
  applicationUrl?: string;
  confirmationNumber?: string;
  coverLetterText?: string;
  resumeVersion?: string;
  appliedAt?: string;
  lastUpdated: string;
  retryCount: number;
  errorMessage?: string;
  screenshotUrls: string[];
  metadata: Record<string, any>;
  atsApplicationId?: string;
  automationLog: AutomationLogEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface AutomationLogEntry {
  timestamp: string;
  action: string;
  status: string;
  details?: string;
  screenshotUrl?: string;
}

export type ApplicationStatus = 
  | 'pending'
  | 'in_progress'
  | 'submitted'
  | 'acknowledged'
  | 'under_review'
  | 'interview_scheduled'
  | 'interview_completed'
  | 'offer_received'
  | 'rejected'
  | 'withdrawn'
  | 'expired'
  | 'failed';

export interface ApplicationFilters {
  status?: ApplicationStatus;
  jobId?: string;
  dateFrom?: string;
  dateTo?: string;
  hasConfirmationNumber?: boolean;
}

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
}

export interface ApplicationStats {
  total: number;
  successful: number;
  failed: number;
  pending: number;
  inProgress: number;
}

export interface BrowserStats {
  totalBrowsers: number;
  activeBrowsers: number;
  totalSessions: number;
  activeSessions: number;
  averageSessionsPerBrowser: number;
}

export interface Recipe {
  id: string;
  name: string;
  atsPlatform: string;
  company?: string;
  urlPattern: string;
  description?: string;
  version: string;
  steps: RecipeStep[];
  fieldMappings: Record<string, string>;
  prerequisites: string[];
  successIndicators: string[];
  failureIndicators: string[];
  estimatedDuration: number;
  retryStrategy: {
    maxAttempts: number;
    delayBetweenAttempts: number;
    exponentialBackoff: boolean;
  };
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

export interface RecipeStep {
  id: string;
  name: string;
  type: 'navigate' | 'click' | 'type' | 'select' | 'upload' | 'wait' | 'submit' | 'screenshot' | 'extract' | 'validate';
  selector?: string;
  value?: string;
  timeout: number;
  required: boolean;
  retryOnFail: boolean;
  waitAfter: number;
  condition?: {
    type: 'element_visible' | 'element_clickable' | 'text_present' | 'url_contains' | 'custom';
    value?: string;
    timeout: number;
  };
}

export class StagehandService extends BaseService {
  constructor() {
    super('stagehand-service', config.services.stagehandUrl, config.timeouts.stagehand);
  }

  /**
   * Submit job application
   * Main implementation for /apply endpoint
   */
  async submitApplication(request: ApplicationRequest): Promise<{
    queueJobId: string;
    userId: string;
    jobId: string;
  }> {
    const response = await this.post<{
      queueJobId: string;
      userId: string;
      jobId: string;
    }>('/api/v1/applications', request);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to submit application');
    }

    return response.data;
  }

  /**
   * Get application by ID
   * Part of /status endpoint functionality
   */
  async getApplication(applicationId: string): Promise<Application> {
    const response = await this.get<Application>(`/api/v1/applications/${applicationId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Application not found');
    }

    return response.data;
  }

  /**
   * Get applications for a user
   * Part of /status endpoint functionality
   */
  async getUserApplications(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{
    applications: Application[];
    count: number;
    limit: number;
    offset: number;
  }> {
    const params = this.buildQueryParams({
      limit: options.limit || 50,
      offset: options.offset || 0,
    });

    const response = await this.get<{
      applications: Application[];
      count: number;
      limit: number;
      offset: number;
    }>(`/api/v1/applications/user/${userId}?${params}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get user applications');
    }

    return response.data;
  }

  /**
   * Get applications by status
   */
  async getApplicationsByStatus(
    status: ApplicationStatus,
    options: {
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{
    applications: Application[];
    status: ApplicationStatus;
    count: number;
    limit: number;
    offset: number;
  }> {
    const params = this.buildQueryParams({
      limit: options.limit || 100,
      offset: options.offset || 0,
    });

    const response = await this.get<{
      applications: Application[];
      status: ApplicationStatus;
      count: number;
      limit: number;
      offset: number;
    }>(`/api/v1/applications/status/${status}?${params}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get applications by status');
    }

    return response.data;
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<QueueStats> {
    const response = await this.get<QueueStats>('/api/v1/queue/stats');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get queue stats');
    }

    return response.data;
  }

  /**
   * Pause queue processing
   */
  async pauseQueue(): Promise<boolean> {
    const response = await this.post('/api/v1/queue/pause');
    return response.success;
  }

  /**
   * Resume queue processing
   */
  async resumeQueue(): Promise<boolean> {
    const response = await this.post('/api/v1/queue/resume');
    return response.success;
  }

  /**
   * Get application statistics
   */
  async getApplicationStats(userId?: string): Promise<ApplicationStats> {
    const params = userId ? this.buildQueryParams({ userId }) : '';
    
    const response = await this.get<ApplicationStats>(`/api/v1/stats/applications?${params}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get application stats');
    }

    return response.data;
  }

  /**
   * Get browser statistics
   */
  async getBrowserStats(): Promise<BrowserStats> {
    const response = await this.get<BrowserStats>('/api/v1/stats/browser');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get browser stats');
    }

    return response.data;
  }

  /**
   * Get service statistics
   */
  async getServiceStats(): Promise<{
    service: string;
    version: string;
    environment: string;
    uptime: number;
    memory: NodeJS.MemoryUsage;
    metrics: Record<string, any>;
    applications: {
      totalApplications: number;
      successfulApplications: number;
      failedApplications: number;
      pendingApplications: number;
      successRate: number;
      averageApplicationTime: number;
      applicationsPerHour: number;
      atsPlatformBreakdown: Record<string, number>;
      errorBreakdown: Record<string, number>;
    };
  }> {
    const response = await this.get<{
      service: string;
      version: string;
      environment: string;
      uptime: number;
      memory: NodeJS.MemoryUsage;
      metrics: Record<string, any>;
      applications: {
        totalApplications: number;
        successfulApplications: number;
        failedApplications: number;
        pendingApplications: number;
        successRate: number;
        averageApplicationTime: number;
        applicationsPerHour: number;
        atsPlatformBreakdown: Record<string, number>;
        errorBreakdown: Record<string, number>;
      };
    }>('/api/v1/stats/service');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to get service stats');
    }

    return response.data;
  }

  /**
   * List available recipes
   */
  async listRecipes(): Promise<{ recipes: Recipe[] }> {
    const response = await this.get<{ recipes: Recipe[] }>('/api/v1/recipes');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to list recipes');
    }

    return response.data;
  }

  /**
   * Get recipe by ID
   */
  async getRecipe(recipeId: string): Promise<Recipe> {
    const response = await this.get<Recipe>(`/api/v1/recipes/${recipeId}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Recipe not found');
    }

    return response.data;
  }

  /**
   * Create new recipe
   */
  async createRecipe(recipe: Omit<Recipe, 'id' | 'createdAt' | 'updatedAt'>): Promise<{
    recipeId: string;
  }> {
    const response = await this.post<{
      recipeId: string;
    }>('/api/v1/recipes', recipe);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || 'Failed to create recipe');
    }

    return response.data;
  }

  /**
   * Cancel application
   */
  async cancelApplication(applicationId: string): Promise<boolean> {
    // This would be implemented by updating the application status
    // For now, return placeholder
    throw new Error('Cancel application not implemented yet');
  }

  /**
   * Retry failed application
   */
  async retryApplication(applicationId: string): Promise<{
    queueJobId: string;
    applicationId: string;
  }> {
    // This would be implemented by re-queueing the application
    // For now, return placeholder
    throw new Error('Retry application not implemented yet');
  }

  /**
   * Update application status (admin function)
   */
  async updateApplicationStatus(
    applicationId: string,
    status: ApplicationStatus,
    notes?: string
  ): Promise<boolean> {
    // This would be implemented by directly updating the application
    // For now, return placeholder
    throw new Error('Update application status not implemented yet');
  }

  /**
   * Check if user has applied to specific job
   */
  async hasUserAppliedToJob(userId: string, jobId: string): Promise<boolean> {
    try {
      const applications = await this.getUserApplications(userId, { limit: 1000 });
      return applications.applications.some(app => 
        app.jobId === jobId && 
        !['failed', 'withdrawn', 'expired'].includes(app.status)
      );
    } catch (error) {
      // If we can't determine, assume not applied to avoid blocking
      return false;
    }
  }

  /**
   * Get application history for a job
   */
  async getJobApplicationHistory(jobId: string): Promise<Application[]> {
    try {
      const response = await this.getApplicationsByStatus('submitted');
      return response.applications.filter(app => app.jobId === jobId);
    } catch (error) {
      return [];
    }
  }
}

export default StagehandService;