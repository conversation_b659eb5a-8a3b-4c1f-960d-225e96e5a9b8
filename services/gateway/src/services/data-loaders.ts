import DataLoader from 'dataloader';
import { RankerService, JobRecommendation } from './ranker-service';
import { StagehandService, Application } from './stagehand-service';
import { IngestorService, JobData, JobSource } from './ingestor-service';
import { logger } from '@/utils/logger';

export interface DataLoaders {
  // Job-related loaders
  jobLoader: DataLoader<string, JobRecommendation | null>;
  jobsByUserLoader: DataLoader<string, JobRecommendation[]>;
  similarJobsLoader: DataLoader<string, JobRecommendation[]>;
  
  // Application-related loaders
  applicationLoader: DataLoader<string, Application | null>;
  applicationsByUserLoader: DataLoader<string, Application[]>;
  applicationsByJobLoader: DataLoader<string, Application[]>;
  userApplicationStatsLoader: DataLoader<string, any>;
  
  // User-related loaders
  userProfileLoader: DataLoader<string, any>;
  userPreferencesLoader: DataLoader<string, any>;
  userAppliedJobsLoader: DataLoader<string, Set<string>>;
  
  // Ingestor-related loaders
  jobSourceLoader: DataLoader<string, JobSource | null>;
  jobSourcesLoader: DataLoader<string, JobSource[]>;
  
  // Stats and metrics loaders
  queueStatsLoader: DataLoader<string, any>;
  serviceHealthLoader: DataLoader<string, any>;
}

export function createDataLoaders(
  rankerService: RankerService,
  stagehandService: StagehandService,
  ingestorService: IngestorService
): DataLoaders {
  
  // Job Loaders
  const jobLoader = new DataLoader<string, JobRecommendation | null>(
    async (jobIds: readonly string[]) => {
      const results = await Promise.allSettled(
        jobIds.map(id => rankerService.getJob(id))
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : null
      );
    },
    {
      cacheKeyFn: (key: string) => `job:${key}`,
      maxBatchSize: 50,
    }
  );

  const jobsByUserLoader = new DataLoader<string, JobRecommendation[]>(
    async (userIds: readonly string[]) => {
      const results = await Promise.allSettled(
        userIds.map(async userId => {
          try {
            const recommendations = await rankerService.getJobRecommendations({
              userId,
              limit: 100,
              useCache: true,
            });
            return recommendations.jobs;
          } catch (error) {
            logger.warn('Failed to load jobs for user', { userId, error });
            return [];
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : []
      );
    },
    {
      cacheKeyFn: (key: string) => `jobs_by_user:${key}`,
      maxBatchSize: 10, // Smaller batch size for expensive operations
    }
  );

  const similarJobsLoader = new DataLoader<string, JobRecommendation[]>(
    async (jobIds: readonly string[]) => {
      const results = await Promise.allSettled(
        jobIds.map(async jobId => {
          try {
            const similar = await rankerService.findSimilarJobs(jobId, { limit: 10 });
            return similar.similarJobs;
          } catch (error) {
            logger.warn('Failed to load similar jobs', { jobId, error });
            return [];
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : []
      );
    },
    {
      cacheKeyFn: (key: string) => `similar_jobs:${key}`,
      maxBatchSize: 20,
    }
  );

  // Application Loaders
  const applicationLoader = new DataLoader<string, Application | null>(
    async (applicationIds: readonly string[]) => {
      const results = await Promise.allSettled(
        applicationIds.map(id => stagehandService.getApplication(id))
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : null
      );
    },
    {
      cacheKeyFn: (key: string) => `application:${key}`,
      maxBatchSize: 50,
    }
  );

  const applicationsByUserLoader = new DataLoader<string, Application[]>(
    async (userIds: readonly string[]) => {
      const results = await Promise.allSettled(
        userIds.map(async userId => {
          try {
            const response = await stagehandService.getUserApplications(userId, { limit: 1000 });
            return response.applications;
          } catch (error) {
            logger.warn('Failed to load applications for user', { userId, error });
            return [];
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : []
      );
    },
    {
      cacheKeyFn: (key: string) => `applications_by_user:${key}`,
      maxBatchSize: 10,
    }
  );

  const applicationsByJobLoader = new DataLoader<string, Application[]>(
    async (jobIds: readonly string[]) => {
      const results = await Promise.allSettled(
        jobIds.map(async jobId => {
          try {
            return await stagehandService.getJobApplicationHistory(jobId);
          } catch (error) {
            logger.warn('Failed to load applications for job', { jobId, error });
            return [];
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : []
      );
    },
    {
      cacheKeyFn: (key: string) => `applications_by_job:${key}`,
      maxBatchSize: 20,
    }
  );

  const userApplicationStatsLoader = new DataLoader<string, any>(
    async (userIds: readonly string[]) => {
      const results = await Promise.allSettled(
        userIds.map(async userId => {
          try {
            return await stagehandService.getApplicationStats(userId);
          } catch (error) {
            logger.warn('Failed to load application stats for user', { userId, error });
            return {
              total: 0,
              successful: 0,
              failed: 0,
              pending: 0,
              inProgress: 0,
            };
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : {
          total: 0,
          successful: 0,
          failed: 0,
          pending: 0,
          inProgress: 0,
        }
      );
    },
    {
      cacheKeyFn: (key: string) => `user_app_stats:${key}`,
      maxBatchSize: 20,
    }
  );

  // User Loaders
  const userProfileLoader = new DataLoader<string, any>(
    async (userIds: readonly string[]) => {
      const results = await Promise.allSettled(
        userIds.map(async userId => {
          try {
            return await rankerService.getUserProfile(userId);
          } catch (error) {
            logger.warn('Failed to load user profile', { userId, error });
            return null;
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : null
      );
    },
    {
      cacheKeyFn: (key: string) => `user_profile:${key}`,
      maxBatchSize: 50,
    }
  );

  const userPreferencesLoader = new DataLoader<string, any>(
    async (userIds: readonly string[]) => {
      const results = await Promise.allSettled(
        userIds.map(async userId => {
          try {
            const response = await rankerService.getUserPreferences(userId);
            return response.preferences;
          } catch (error) {
            logger.warn('Failed to load user preferences', { userId, error });
            return {};
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : {}
      );
    },
    {
      cacheKeyFn: (key: string) => `user_preferences:${key}`,
      maxBatchSize: 50,
    }
  );

  const userAppliedJobsLoader = new DataLoader<string, Set<string>>(
    async (userIds: readonly string[]) => {
      const results = await Promise.allSettled(
        userIds.map(async userId => {
          try {
            const applications = await applicationsByUserLoader.load(userId);
            const appliedJobIds = new Set(
              applications
                .filter(app => !['failed', 'withdrawn', 'expired'].includes(app.status))
                .map(app => app.jobId)
            );
            return appliedJobIds;
          } catch (error) {
            logger.warn('Failed to load applied jobs for user', { userId, error });
            return new Set<string>();
          }
        })
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : new Set<string>()
      );
    },
    {
      cacheKeyFn: (key: string) => `user_applied_jobs:${key}`,
      maxBatchSize: 20,
    }
  );

  // Ingestor Loaders
  const jobSourceLoader = new DataLoader<string, JobSource | null>(
    async (sourceIds: readonly string[]) => {
      const results = await Promise.allSettled(
        sourceIds.map(id => ingestorService.getJobSource(id))
      );
      
      return results.map(result => 
        result.status === 'fulfilled' ? result.value : null
      );
    },
    {
      cacheKeyFn: (key: string) => `job_source:${key}`,
      maxBatchSize: 20,
    }
  );

  const jobSourcesLoader = new DataLoader<string, JobSource[]>(
    async (keys: readonly string[]) => {
      // This loader doesn't really batch since we're getting all sources
      // But it provides caching
      try {
        const sources = await ingestorService.getJobSources();
        return keys.map(() => sources);
      } catch (error) {
        logger.warn('Failed to load job sources', { error });
        return keys.map(() => []);
      }
    },
    {
      cacheKeyFn: () => 'all_job_sources',
      maxBatchSize: 1,
    }
  );

  // Stats and Metrics Loaders
  const queueStatsLoader = new DataLoader<string, any>(
    async (keys: readonly string[]) => {
      try {
        const [stagehandStats, ingestorStats] = await Promise.allSettled([
          stagehandService.getQueueStats(),
          ingestorService.getQueueStatus(),
        ]);

        const combinedStats = {
          stagehand: stagehandStats.status === 'fulfilled' ? stagehandStats.value : null,
          ingestor: ingestorStats.status === 'fulfilled' ? ingestorStats.value : null,
        };

        return keys.map(() => combinedStats);
      } catch (error) {
        logger.warn('Failed to load queue stats', { error });
        return keys.map(() => ({}));
      }
    },
    {
      cacheKeyFn: () => 'queue_stats',
      maxBatchSize: 1,
      cache: false, // Don't cache queue stats for too long
    }
  );

  const serviceHealthLoader = new DataLoader<string, any>(
    async (serviceNames: readonly string[]) => {
      const results = await Promise.allSettled([
        rankerService.getServiceHealth(),
        stagehandService.getServiceHealth(),
        ingestorService.getServiceHealth(),
      ]);

      const healthMap = {
        'ranker-service': results[0].status === 'fulfilled' ? results[0].value : { status: 'error' },
        'stagehand-service': results[1].status === 'fulfilled' ? results[1].value : { status: 'error' },
        'ingestor-service': results[2].status === 'fulfilled' ? results[2].value : { status: 'error' },
      };

      return serviceNames.map(name => healthMap[name as keyof typeof healthMap] || { status: 'unknown' });
    },
    {
      cacheKeyFn: (key: string) => `service_health:${key}`,
      maxBatchSize: 10,
      cache: false, // Don't cache health checks
    }
  );

  return {
    jobLoader,
    jobsByUserLoader,
    similarJobsLoader,
    applicationLoader,
    applicationsByUserLoader,
    applicationsByJobLoader,
    userApplicationStatsLoader,
    userProfileLoader,
    userPreferencesLoader,
    userAppliedJobsLoader,
    jobSourceLoader,
    jobSourcesLoader,
    queueStatsLoader,
    serviceHealthLoader,
  };
}

export function clearDataLoaderCaches(loaders: DataLoaders): void {
  Object.values(loaders).forEach(loader => {
    if (loader && typeof loader.clearAll === 'function') {
      loader.clearAll();
    }
  });
}

export function clearUserCaches(loaders: DataLoaders, userId: string): void {
  loaders.userProfileLoader.clear(userId);
  loaders.userPreferencesLoader.clear(userId);
  loaders.jobsByUserLoader.clear(userId);
  loaders.applicationsByUserLoader.clear(userId);
  loaders.userApplicationStatsLoader.clear(userId);
  loaders.userAppliedJobsLoader.clear(userId);
}

export function clearJobCaches(loaders: DataLoaders, jobId: string): void {
  loaders.jobLoader.clear(jobId);
  loaders.similarJobsLoader.clear(jobId);
  loaders.applicationsByJobLoader.clear(jobId);
}

export default createDataLoaders;