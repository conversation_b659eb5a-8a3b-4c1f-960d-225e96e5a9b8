import { gql } from 'graphql-tag';

export const typeDefs = gql`
  # Scalars
  scalar DateTime
  scalar JSON
  scalar Upload
  scalar UUID

  # Enums
  enum ApplicationStatus {
    PENDING
    IN_PROGRESS
    SUBMITTED
    ACKNOWLEDGED
    UNDER_REVIEW
    INTERVIEW_SCHEDULED
    INTERVIEW_COMPLETED
    OFFER_RECEIVED
    REJECTED
    WITHDRAWN
    EXPIRED
    FAILED
  }

  enum JobApplicationMethod {
    DIRECT
    ATS
    EMAIL
    EXTERNAL
  }

  enum EmploymentType {
    FULL_TIME
    PART_TIME
    CONTRACT
    INTERNSHIP
    FREELANCE
    TEMPORARY
  }

  enum ExperienceLevel {
    ENTRY
    MID
    SENIOR
    EXECUTIVE
    DIRECTOR
  }

  enum Priority {
    LOW
    NORMAL
    HIGH
  }

  enum SortOrder {
    ASC
    DESC
  }

  enum JobSortField {
    CREATED_AT
    POSTED_AT
    SIMILARITY_SCORE
    RANKING_SCORE
    SALARY_MIN
    SALARY_MAX
  }

  enum ApplicationSortField {
    CREATED_AT
    APPLIED_AT
    LAST_UPDATED
    STATUS
  }

  # Input Types
  input PaginationInput {
    page: Int = 1
    limit: Int = 20
  }

  input SortInput {
    field: String!
    order: SortOrder = ASC
  }

  input JobFiltersInput {
    country: String
    location: String
    isRemote: Boolean
    employmentType: EmploymentType
    experienceLevel: ExperienceLevel
    minSalary: Int
    maxSalary: Int
    currency: String
    skills: [String!]
    companies: [String!]
    sources: [String!]
    postedAfter: DateTime
    postedBefore: DateTime
  }

  input UserPreferencesInput {
    preferredLocations: [String!]
    remotePreference: Boolean
    minSalary: Int
    maxSalary: Int
    preferredCurrency: String
    experienceLevel: ExperienceLevel
    employmentTypes: [EmploymentType!]
    excludedCompanies: [String!]
    keywords: [String!]
    skills: [String!]
  }

  input ApplicationInput {
    jobId: UUID!
    priority: Priority = NORMAL
    customCoverLetter: String
    customResumeUrl: String
    scheduledAt: DateTime
    metadata: JSON
  }

  input ApplicationFiltersInput {
    status: ApplicationStatus
    jobId: UUID
    dateFrom: DateTime
    dateTo: DateTime
    hasConfirmationNumber: Boolean
  }

  # Core Types
  type User {
    id: UUID!
    name: String!
    email: String!
    phone: String
    location: String
    country: String!
    resumeUrl: String
    coverLetterTemplate: String
    skills: [String!]!
    experienceYears: Int
    currentTitle: String
    linkedinUrl: String
    githubUrl: String
    portfolioUrl: String
    preferences: JSON
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Computed fields
    applications(
      filters: ApplicationFiltersInput
      pagination: PaginationInput
      sort: SortInput
    ): ApplicationConnection!
    
    applicationStats: UserApplicationStats!
  }

  type Job {
    id: UUID!
    externalId: String!
    source: String!
    url: String!
    title: String!
    company: String!
    location: String
    country: String!
    isRemote: Boolean!
    employmentType: EmploymentType
    experienceLevel: ExperienceLevel
    salaryMin: Int
    salaryMax: Int
    currency: String
    description: String
    requirements: String
    skills: [String!]!
    atsPlatform: String
    applyUrl: String
    applicationMethod: JobApplicationMethod!
    postedAt: DateTime!
    expiresAt: DateTime
    isActive: Boolean!
    rankingScore: Float
    similarityScore: Float
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Computed fields
    applications(
      pagination: PaginationInput
      sort: SortInput
    ): ApplicationConnection!
    
    isAppliedByUser(userId: UUID!): Boolean!
    similarJobs(limit: Int = 10): [Job!]!
  }

  type Application {
    id: UUID!
    userId: UUID!
    jobId: UUID!
    status: ApplicationStatus!
    applicationUrl: String
    confirmationNumber: String
    coverLetterText: String
    resumeVersion: String
    appliedAt: DateTime
    lastUpdated: DateTime!
    retryCount: Int!
    errorMessage: String
    screenshotUrls: [String!]!
    metadata: JSON
    atsApplicationId: String
    automationLog: [AutomationLogEntry!]!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
    job: Job!
  }

  type AutomationLogEntry {
    timestamp: DateTime!
    action: String!
    status: String!
    details: String
    screenshotUrl: String
  }

  # Ranking and Recommendation Types
  type JobRecommendation {
    job: Job!
    similarityScore: Float!
    rankingScore: Float!
    rankingBreakdown: RankingBreakdown
    matchingReasons: [String!]!
  }

  type RankingBreakdown {
    skillsMatch: Float!
    experienceMatch: Float!
    locationMatch: Float!
    salaryMatch: Float!
    overallScore: Float!
  }

  # Connection Types (for pagination)
  type JobConnection {
    edges: [JobEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
    filters: JobFiltersInfo
  }

  type JobEdge {
    node: Job!
    cursor: String!
  }

  type ApplicationConnection {
    edges: [ApplicationEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type ApplicationEdge {
    node: Application!
    cursor: String!
  }

  type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
  }

  type JobFiltersInfo {
    availableCountries: [String!]!
    availableLocations: [String!]!
    availableCompanies: [String!]!
    availableSources: [String!]!
    availableSkills: [String!]!
    salaryRange: SalaryRange
  }

  type SalaryRange {
    min: Int
    max: Int
    currency: String
  }

  # Statistics Types
  type UserApplicationStats {
    total: Int!
    successful: Int!
    failed: Int!
    pending: Int!
    inProgress: Int!
    successRate: Float!
    averageResponseTime: Float
    lastApplicationDate: DateTime
  }

  type PlatformStats {
    totalJobs: Int!
    totalApplications: Int!
    successRate: Float!
    averageProcessingTime: Float!
    topCompanies: [CompanyStats!]!
    topSkills: [SkillStats!]!
    applicationsByStatus: [StatusStats!]!
  }

  type CompanyStats {
    company: String!
    jobCount: Int!
    applicationCount: Int!
    successRate: Float!
  }

  type SkillStats {
    skill: String!
    demandCount: Int!
    averageSalary: Int
  }

  type StatusStats {
    status: ApplicationStatus!
    count: Int!
    percentage: Float!
  }

  # Queue and System Types
  type QueueStats {
    waiting: Int!
    active: Int!
    completed: Int!
    failed: Int!
    delayed: Int!
  }

  type SystemHealth {
    status: String!
    services: [ServiceHealth!]!
    timestamp: DateTime!
  }

  type ServiceHealth {
    name: String!
    status: String!
    responseTime: Float
    lastChecked: DateTime!
  }

  # File Upload Types
  type FileUploadResult {
    success: Boolean!
    fileUrl: String
    fileName: String
    fileSize: Int
    error: String
  }

  # Mutation Response Types
  type ApplicationMutationResponse {
    success: Boolean!
    message: String
    application: Application
    queueJobId: String
    errors: [String!]
  }

  type UserMutationResponse {
    success: Boolean!
    message: String
    user: User
    errors: [String!]
  }

  # Main Query Type
  type Query {
    # Job Discovery (Main /discover endpoint functionality)
    discoverJobs(
      userId: UUID!
      filters: JobFiltersInput
      pagination: PaginationInput
      sort: SortInput
    ): JobConnection!
    
    # Job Recommendations (Personalized)
    recommendJobs(
      userId: UUID!
      limit: Int = 20
      useCache: Boolean = true
    ): [JobRecommendation!]!
    
    # Job Search (Text-based)
    searchJobs(
      query: String!
      filters: JobFiltersInput
      pagination: PaginationInput
      sort: SortInput
    ): JobConnection!
    
    # Individual Job Details
    job(id: UUID!): Job
    jobs(ids: [UUID!]!): [Job!]!
    
    # Application Status (Main /status endpoint functionality)
    application(id: UUID!): Application
    applications(
      userId: UUID
      filters: ApplicationFiltersInput
      pagination: PaginationInput
      sort: SortInput
    ): ApplicationConnection!
    
    # User Management
    user(id: UUID!): User
    currentUser: User
    
    # Statistics and Analytics
    userStats(userId: UUID!): UserApplicationStats!
    platformStats: PlatformStats!
    
    # System Information
    queueStats: QueueStats!
    systemHealth: SystemHealth!
  }

  # Main Mutation Type
  type Mutation {
    # Job Application (Main /apply endpoint functionality)
    applyToJob(input: ApplicationInput!): ApplicationMutationResponse!
    
    # Bulk Application
    applyToJobs(inputs: [ApplicationInput!]!): [ApplicationMutationResponse!]!
    
    # Application Management
    cancelApplication(applicationId: UUID!): ApplicationMutationResponse!
    retryApplication(applicationId: UUID!): ApplicationMutationResponse!
    updateApplicationStatus(
      applicationId: UUID!
      status: ApplicationStatus!
      notes: String
    ): ApplicationMutationResponse!
    
    # User Management
    updateUserPreferences(
      userId: UUID!
      preferences: UserPreferencesInput!
    ): UserMutationResponse!
    
    updateUserProfile(
      userId: UUID!
      name: String
      phone: String
      location: String
      currentTitle: String
      experienceYears: Int
      skills: [String!]
      linkedinUrl: String
      githubUrl: String
      portfolioUrl: String
    ): UserMutationResponse!
    
    # File Uploads
    uploadResume(userId: UUID!, file: Upload!): FileUploadResult!
    uploadCoverLetter(userId: UUID!, file: Upload!): FileUploadResult!
    
    # Queue Management (Admin)
    pauseApplicationQueue: Boolean!
    resumeApplicationQueue: Boolean!
    clearApplicationQueue: Boolean!
  }

  # Subscription Type (for real-time updates)
  type Subscription {
    # Application Status Updates
    applicationStatusChanged(userId: UUID!): Application!
    applicationStatusChangedForJob(jobId: UUID!): Application!
    
    # New Job Notifications
    newJobsForUser(userId: UUID!): Job!
    
    # Queue Status Updates
    queueStatsUpdated: QueueStats!
    
    # System Health Updates
    systemHealthUpdated: SystemHealth!
  }
`;

export default typeDefs;