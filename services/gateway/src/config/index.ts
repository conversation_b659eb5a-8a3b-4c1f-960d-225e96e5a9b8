import dotenv from 'dotenv';
import { z } from 'zod';

dotenv.config();

const configSchema = z.object({
  // Server Configuration
  port: z.coerce.number().min(1000).max(65535).default(3000),
  nodeEnv: z.enum(['development', 'staging', 'production']).default('development'),
  serviceName: z.string().default('karmsakha-graphql-gateway'),

  // GraphQL Configuration
  graphql: z.object({
    path: z.string().default('/graphql'),
    playground: z.coerce.boolean().default(true),
    introspection: z.coerce.boolean().default(true),
    debug: z.coerce.boolean().default(true),
    maxQueryDepth: z.coerce.number().min(1).max(20).default(10),
    maxQueryComplexity: z.coerce.number().min(100).max(10000).default(1000),
    queryTimeout: z.coerce.number().min(1000).max(60000).default(30000),
  }).default({}),

  // Microservices URLs
  services: z.object({
    ingestorUrl: z.string().url().default('http://localhost:3001'),
    rankerUrl: z.string().url().default('http://localhost:3002'),
    stagehandUrl: z.string().url().default('http://localhost:3003'),
  }).default({}),

  // Database Configuration
  databaseUrl: z.string().url(),

  // Redis Configuration
  redis: z.object({
    url: z.string().url().default('redis://localhost:6379'),
    ttl: z.coerce.number().min(10).max(3600).default(300),
    prefix: z.string().default('gql:'),
  }).default({}),

  // Authentication & Security
  auth: z.object({
    jwtSecret: z.string().min(32),
    jwtExpiry: z.string().default('24h'),
    apiKey: z.string().min(16),
    bcryptRounds: z.coerce.number().min(8).max(15).default(12),
  }),

  // Rate Limiting
  rateLimit: z.object({
    windowMs: z.coerce.number().min(60000).max(3600000).default(900000), // 15 minutes
    maxRequests: z.coerce.number().min(10).max(10000).default(1000),
    skipSuccessful: z.coerce.boolean().default(true),
  }).default({}),

  // Caching
  cache: z.object({
    enableResponseCache: z.coerce.boolean().default(true),
    responseCacheTtl: z.coerce.number().min(10).max(3600).default(300),
    enableQueryCache: z.coerce.boolean().default(true),
    queryCacheTtl: z.coerce.number().min(10).max(600).default(60),
  }).default({}),

  // File Upload
  upload: z.object({
    maxFileSize: z.coerce.number().min(1048576).max(52428800).default(10485760), // 10MB
    allowedTypes: z.array(z.string()).default(['pdf', 'doc', 'docx', 'txt']),
    uploadPath: z.string().default('/tmp/uploads'),
  }).default({}),

  // Monitoring & Metrics
  monitoring: z.object({
    metricsPort: z.coerce.number().min(1000).max(65535).default(9000),
    logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    enableTracing: z.coerce.boolean().default(true),
    enableMetrics: z.coerce.boolean().default(true),
  }).default({}),

  // External Services Timeouts
  timeouts: z.object({
    ingestor: z.coerce.number().min(5000).max(60000).default(30000),
    ranker: z.coerce.number().min(5000).max(60000).default(30000),
    stagehand: z.coerce.number().min(10000).max(120000).default(60000),
  }).default({}),

  // DataLoader Configuration
  dataLoader: z.object({
    cache: z.coerce.boolean().default(true),
    batchScheduleFn: z.string().default('process.nextTick'),
  }).default({}),

  // Pagination
  pagination: z.object({
    defaultPageSize: z.coerce.number().min(5).max(100).default(20),
    maxPageSize: z.coerce.number().min(50).max(1000).default(100),
  }).default({}),

  // Feature Flags
  features: z.object({
    enableSubscriptions: z.coerce.boolean().default(true),
    enableFileUploads: z.coerce.boolean().default(true),
    enableAnalytics: z.coerce.boolean().default(true),
    enableAuditLogs: z.coerce.boolean().default(true),
  }).default({}),

  // Notification Configuration
  notifications: z.object({
    webhookUrl: z.string().url().optional(),
    slackWebhookUrl: z.string().url().optional(),
  }).default({}),

  // Health Check
  healthCheckInterval: z.coerce.number().min(5000).max(300000).default(30000),

  // CORS Configuration
  cors: z.object({
    origin: z.string().default('*'),
    credentials: z.coerce.boolean().default(true),
  }).default({}),

  // Apollo Studio Configuration
  apollo: z.object({
    key: z.string().optional(),
    graphRef: z.string().default('karmsakha@main'),
    schemaReporting: z.coerce.boolean().default(false),
  }).default({}),
});

type Config = z.infer<typeof configSchema>;

const parseConfig = (): Config => {
  const rawConfig = {
    port: process.env.PORT,
    nodeEnv: process.env.NODE_ENV,
    serviceName: process.env.SERVICE_NAME,
    
    graphql: {
      path: process.env.GRAPHQL_PATH,
      playground: process.env.GRAPHQL_PLAYGROUND,
      introspection: process.env.GRAPHQL_INTROSPECTION,
      debug: process.env.GRAPHQL_DEBUG,
      maxQueryDepth: process.env.MAX_QUERY_DEPTH,
      maxQueryComplexity: process.env.MAX_QUERY_COMPLEXITY,
      queryTimeout: process.env.QUERY_TIMEOUT_MS,
    },

    services: {
      ingestorUrl: process.env.INGESTOR_SERVICE_URL,
      rankerUrl: process.env.RANKER_SERVICE_URL,
      stagehandUrl: process.env.STAGEHAND_SERVICE_URL,
    },

    databaseUrl: process.env.DATABASE_URL,

    redis: {
      url: process.env.REDIS_URL,
      ttl: process.env.CACHE_TTL,
      prefix: process.env.CACHE_PREFIX,
    },

    auth: {
      jwtSecret: process.env.JWT_SECRET,
      jwtExpiry: process.env.JWT_EXPIRY,
      apiKey: process.env.API_KEY,
      bcryptRounds: process.env.BCRYPT_ROUNDS,
    },

    rateLimit: {
      windowMs: process.env.RATE_LIMIT_WINDOW_MS,
      maxRequests: process.env.RATE_LIMIT_MAX_REQUESTS,
      skipSuccessful: process.env.RATE_LIMIT_SKIP_SUCCESSFUL,
    },

    cache: {
      enableResponseCache: process.env.ENABLE_RESPONSE_CACHE,
      responseCacheTtl: process.env.RESPONSE_CACHE_TTL,
      enableQueryCache: process.env.ENABLE_QUERY_CACHE,
      queryCacheTtl: process.env.QUERY_CACHE_TTL,
    },

    upload: {
      maxFileSize: process.env.MAX_FILE_SIZE,
      allowedTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || ['pdf', 'doc', 'docx', 'txt'],
      uploadPath: process.env.UPLOAD_PATH,
    },

    monitoring: {
      metricsPort: process.env.METRICS_PORT,
      logLevel: process.env.LOG_LEVEL,
      enableTracing: process.env.ENABLE_TRACING,
      enableMetrics: process.env.ENABLE_METRICS,
    },

    timeouts: {
      ingestor: process.env.INGESTOR_TIMEOUT,
      ranker: process.env.RANKER_TIMEOUT,
      stagehand: process.env.STAGEHAND_TIMEOUT,
    },

    dataLoader: {
      cache: process.env.DATALOADER_CACHE,
      batchScheduleFn: process.env.DATALOADER_BATCH_SCHEDULE_FN,
    },

    pagination: {
      defaultPageSize: process.env.DEFAULT_PAGE_SIZE,
      maxPageSize: process.env.MAX_PAGE_SIZE,
    },

    features: {
      enableSubscriptions: process.env.ENABLE_SUBSCRIPTIONS,
      enableFileUploads: process.env.ENABLE_FILE_UPLOADS,
      enableAnalytics: process.env.ENABLE_ANALYTICS,
      enableAuditLogs: process.env.ENABLE_AUDIT_LOGS,
    },

    notifications: {
      webhookUrl: process.env.WEBHOOK_URL,
      slackWebhookUrl: process.env.SLACK_WEBHOOK_URL,
    },

    healthCheckInterval: process.env.HEALTH_CHECK_INTERVAL,

    cors: {
      origin: process.env.CORS_ORIGIN,
      credentials: process.env.CORS_CREDENTIALS,
    },

    apollo: {
      key: process.env.APOLLO_KEY,
      graphRef: process.env.APOLLO_GRAPH_REF,
      schemaReporting: process.env.APOLLO_SCHEMA_REPORTING,
    },
  };

  try {
    return configSchema.parse(rawConfig);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      );
      throw new Error(`Configuration validation failed:\n${errorMessages.join('\n')}`);
    }
    throw error;
  }
};

export const config = parseConfig();

export type { Config };

export default config;