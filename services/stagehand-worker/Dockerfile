# Multi-stage build for Stagehand Worker
FROM node:18-slim as base

# Install system dependencies for Puppeteer
RUN apt-get update && apt-get install -y \
    chromium \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Set Chrome path for Puppeteer
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

# Development stage
FROM base as development

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY src/ ./src/

# Create storage directories
RUN mkdir -p storage/resumes storage/cover-letters storage/screenshots logs

# Expose ports
EXPOSE 3003 9003

# Start development server
CMD ["npm", "run", "dev"]

# Build stage
FROM base as build

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY src/ ./src/

# Build the application
RUN npm run build

# Production stage
FROM base as production

# Create non-root user for security
RUN groupadd -r stagehand && useradd -r -g stagehand stagehand

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=build /app/dist ./dist

# Create necessary directories with proper permissions
RUN mkdir -p storage/resumes storage/cover-letters storage/screenshots logs \
    && chown -R stagehand:stagehand /app

# Switch to non-root user
USER stagehand

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3003/health || exit 1

# Expose ports
EXPOSE 3003 9003

# Set environment
ENV NODE_ENV=production

# Start the application
CMD ["node", "dist/index.js"]

# Multi-architecture build support
FROM production as final
LABEL maintainer="Karmsakha Team"
LABEL version="1.0.0"
LABEL description="Stagehand Worker - Automated job application service"