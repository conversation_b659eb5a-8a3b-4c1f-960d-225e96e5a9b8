# Stagehand Worker - Automated Job Application Service

The Stagehand Worker is a high-performance, browser automation service that handles automated job applications across multiple ATS (Applicant Tracking System) platforms. It processes up to 100+ applications per hour per worker instance using intelligent browser automation and platform-specific recipes.

## Features

### Core Capabilities
- **Multi-ATS Support**: Automated application recipes for Greenhouse, Lever, Workday, LinkedIn, Indeed, and other major platforms
- **Browser Automation**: Headless Chrome with stealth mode to avoid detection
- **Queue Processing**: Redis-based job queue with retry logic and rate limiting
- **Recipe Engine**: Flexible, configurable automation scripts for different ATS platforms
- **Performance Monitoring**: Real-time metrics and performance tracking
- **Error Handling**: Comprehensive error handling with screenshots and detailed logging

### Supported ATS Platforms
- Greenhouse
- Lever
- Workday
- BambooHR
- iCIMS
- Jobvite
- SmartRecruiters
- LinkedIn Jobs
- Indeed
- Generic form-based applications

### Performance Specifications
- **Throughput**: 100+ applications per hour per worker
- **Concurrency**: Up to 5 concurrent browser sessions
- **Success Rate**: >90% for supported platforms
- **Response Time**: <2 minutes average per application
- **Retry Logic**: Intelligent retry with exponential backoff

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│ Stagehand Worker│───▶│  Browser Pool   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                        ┌─────────────────┐
                        │  Recipe Engine  │
                        │                 │
                        └─────────────────┘
                                │
                                ▼
                        ┌─────────────────┐
                        │ Application DB  │
                        │                 │
                        └─────────────────┘
```

## Getting Started

### Prerequisites
- Node.js 18+
- Redis 6+
- PostgreSQL 13+
- Docker (optional)

### Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Server Configuration
PORT=3003
NODE_ENV=development
SERVICE_NAME=karmsakha-stagehand-worker

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/karmsakha_db

# Redis
REDIS_URL=redis://localhost:6379
REDIS_QUEUE_NAME=job-applications

# Browser Automation
HEADLESS_MODE=true
BROWSER_TIMEOUT=60000
CONCURRENT_BROWSERS=5
MAX_RETRY_ATTEMPTS=3
STEALTH_MODE=true

# Rate Limiting
MAX_APPLICATIONS_PER_HOUR=100
APPLICATION_DELAY_MS=1000

# Security
JWT_SECRET=your-jwt-secret-key
API_KEY=your-api-key
```

### Installation

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Start development server
npm run dev

# Start production server
npm start
```

### Docker Deployment

```bash
# Build image
docker build -t stagehand-worker .

# Run container
docker run -d \
  --name stagehand-worker \
  -p 3003:3003 \
  -p 9003:9003 \
  --env-file .env \
  stagehand-worker
```

## API Documentation

### Application Management

#### Create Application
```http
POST /api/v1/applications
Content-Type: application/json

{
  "user_id": "user-uuid",
  "job_id": "job-uuid", 
  "priority": "normal",
  "custom_cover_letter": "Optional custom cover letter",
  "scheduled_at": "2024-01-01T10:00:00Z"
}
```

#### Get User Applications
```http
GET /api/v1/applications/user/{userId}?limit=50&offset=0
```

#### Get Applications by Status
```http
GET /api/v1/applications/status/{status}?limit=100&offset=0
```

### Queue Management

#### Get Queue Statistics
```http
GET /api/v1/queue/stats
```

#### Pause/Resume Processing
```http
POST /api/v1/queue/pause
POST /api/v1/queue/resume
```

### Recipe Management

#### List Recipes
```http
GET /api/v1/recipes
```

#### Get Recipe Details
```http
GET /api/v1/recipes/{id}
```

#### Create Custom Recipe
```http
POST /api/v1/recipes
Content-Type: application/json

{
  "id": "custom-recipe",
  "name": "Custom ATS Recipe",
  "ats_platform": "custom",
  "url_pattern": "*company.com/careers*",
  "steps": [...],
  "field_mappings": {...}
}
```

### Health & Monitoring

#### Health Checks
```http
GET /health          # Basic health check
GET /health/ready    # Readiness check
GET /health/live     # Liveness check
```

#### Metrics
```http
GET /metrics         # Prometheus metrics
```

#### Statistics
```http
GET /api/v1/stats/applications
GET /api/v1/stats/browser
GET /api/v1/stats/service
```

## Recipe Development

### Recipe Structure

A recipe defines how to automate applications for a specific ATS platform:

```typescript
{
  "id": "greenhouse-default",
  "name": "Greenhouse Default Application",
  "ats_platform": "greenhouse",
  "url_pattern": "*greenhouse.io*",
  "steps": [
    {
      "id": "fill-name",
      "name": "Fill first name",
      "type": "type",
      "selector": "#first_name",
      "value": "{{firstName}}",
      "required": true
    }
  ],
  "field_mappings": {
    "first_name": "{{firstName}}",
    "last_name": "{{lastName}}",
    "email": "{{email}}"
  },
  "success_indicators": [
    "text:thank you for your application"
  ],
  "failure_indicators": [
    "text:error",
    ".error-message"
  ]
}
```

### Step Types

- **navigate**: Navigate to URL
- **click**: Click element
- **type**: Type text into input
- **select**: Select option from dropdown
- **upload**: Upload file
- **wait**: Wait for specified time
- **submit**: Submit form
- **screenshot**: Take screenshot
- **extract**: Extract text from element
- **validate**: Validate element exists

### Variables

Available variables in recipes:
- `{{firstName}}`, `{{lastName}}`, `{{fullName}}`
- `{{email}}`, `{{phone}}`
- `{{location}}`, `{{country}}`
- `{{currentTitle}}`, `{{experienceYears}}`
- `{{skills}}`, `{{resumePath}}`
- `{{jobTitle}}`, `{{companyName}}`

## Monitoring & Alerts

### Prometheus Metrics

The service exposes detailed metrics for monitoring:

```
# Application metrics
stagehand_worker_applications_started_total
stagehand_worker_applications_completed_total
stagehand_worker_application_duration_seconds

# Browser metrics
stagehand_worker_browser_sessions_created
stagehand_worker_browser_actions_total

# Queue metrics
stagehand_worker_queue_jobs_waiting
stagehand_worker_queue_jobs_active
```

### Slack Notifications

Configure Slack webhook for real-time alerts:

```bash
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

Alerts include:
- Application failures
- Performance degradation
- System errors
- Queue backlogs

## Performance Tuning

### Browser Pool Configuration

```bash
# Increase concurrent browsers for higher throughput
CONCURRENT_BROWSERS=10

# Adjust timeouts for slower networks
BROWSER_TIMEOUT=120000

# Enable stealth mode for better success rates
STEALTH_MODE=true
```

### Rate Limiting

```bash
# Platform-specific rate limits
MAX_APPLICATIONS_PER_HOUR=100
APPLICATION_DELAY_MS=1000

# User rate limiting (prevent abuse)
USER_MAX_APPLICATIONS_PER_HOUR=50
```

### Memory Management

```bash
# Docker memory limits
docker run --memory=2g --cpus=2 stagehand-worker
```

## Troubleshooting

### Common Issues

1. **Browser Launch Failures**
   ```bash
   # Install Chrome dependencies
   apt-get update && apt-get install -y chromium
   ```

2. **Rate Limiting**
   ```bash
   # Check rate limit status
   curl http://localhost:3003/api/v1/queue/stats
   ```

3. **Memory Issues**
   ```bash
   # Monitor memory usage
   curl http://localhost:3003/api/v1/stats/service
   ```

### Debug Mode

Enable detailed logging:

```bash
LOG_LEVEL=debug
ENABLE_DETAILED_LOGGING=true
SCREENSHOT_ON_ERROR=true
```

### Recovery Procedures

1. **Queue Backup**: Applications are persisted in PostgreSQL
2. **Browser Recovery**: Automatic browser pool management
3. **Service Recovery**: Health checks and auto-restart

## Security Considerations

### Data Protection
- User credentials encrypted at rest
- TLS encryption for all communications
- No sensitive data in logs or screenshots

### Access Control
- API key authentication
- JWT tokens for user sessions
- Rate limiting per user/IP

### Compliance
- GDPR compliant data handling
- Audit trail for all applications
- Data retention policies

## Development

### Running Tests

```bash
npm test                # Run all tests
npm run test:watch     # Watch mode
npm run test:coverage  # Coverage report
```

### Code Quality

```bash
npm run lint           # ESLint
npm run typecheck      # TypeScript check
```

### Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request

## Support

### Documentation
- API Reference: `/docs/api`
- Recipe Guide: `/docs/recipes`
- Deployment Guide: `/docs/deployment`

### Monitoring Dashboards
- Grafana: Port 3000
- Prometheus: Port 9090
- Application UI: Port 3003

### Contact
- Technical Support: <EMAIL>
- Documentation: docs.karmsakha.com
- Status Page: status.karmsakha.com