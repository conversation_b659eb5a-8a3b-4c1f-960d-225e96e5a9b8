import { z } from 'zod';

// User Profile Types
export const UserProfileSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().optional(),
  location: z.string().optional(),
  country: z.string(),
  resume_url: z.string().url().optional(),
  cover_letter_template: z.string().optional(),
  skills: z.array(z.string()).default([]),
  experience_years: z.number().min(0).optional(),
  current_title: z.string().optional(),
  linkedin_url: z.string().url().optional(),
  github_url: z.string().url().optional(),
  portfolio_url: z.string().url().optional(),
  preferences: z.record(z.any()).default({}),
  credentials: z.record(z.string()).default({}), // Encrypted ATS credentials
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

export type UserProfile = z.infer<typeof UserProfileSchema>;

// Job Types
export const JobSchema = z.object({
  id: z.string().uuid(),
  external_id: z.string(),
  source: z.string(),
  url: z.string().url(),
  title: z.string(),
  company: z.string(),
  location: z.string().optional(),
  country: z.string(),
  is_remote: z.boolean(),
  employment_type: z.string().optional(),
  experience_level: z.string().optional(),
  salary_min: z.number().optional(),
  salary_max: z.number().optional(),
  currency: z.string().optional(),
  description: z.string().optional(),
  requirements: z.string().optional(),
  skills: z.array(z.string()).default([]),
  ats_platform: z.string().optional(),
  apply_url: z.string().url().optional(),
  posted_at: z.string().datetime(),
  expires_at: z.string().datetime().optional(),
  is_active: z.boolean(),
  ranking_score: z.number().min(0).max(1).optional(),
  similarity_score: z.number().min(0).max(1).optional(),
  application_method: z.enum(['direct', 'ats', 'email', 'external']).default('direct'),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

export type Job = z.infer<typeof JobSchema>;

// Application Status Types
export const ApplicationStatusEnum = z.enum([
  'pending',
  'in_progress',
  'submitted',
  'acknowledged',
  'under_review',
  'interview_scheduled',
  'interview_completed',
  'offer_received',
  'rejected',
  'withdrawn',
  'expired',
  'failed',
]);

export type ApplicationStatus = z.infer<typeof ApplicationStatusEnum>;

// Application Types
export const ApplicationSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  job_id: z.string().uuid(),
  status: ApplicationStatusEnum,
  application_url: z.string().url().optional(),
  confirmation_number: z.string().optional(),
  cover_letter_text: z.string().optional(),
  resume_version: z.string().optional(),
  applied_at: z.string().datetime().optional(),
  last_updated: z.string().datetime(),
  retry_count: z.number().min(0).default(0),
  error_message: z.string().optional(),
  screenshot_urls: z.array(z.string().url()).default([]),
  metadata: z.record(z.any()).default({}),
  ats_application_id: z.string().optional(),
  automation_log: z.array(z.object({
    timestamp: z.string().datetime(),
    action: z.string(),
    status: z.string(),
    details: z.string().optional(),
    screenshot_url: z.string().url().optional(),
  })).default([]),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

export type Application = z.infer<typeof ApplicationSchema>;

// Application Request Types
export const ApplicationRequestSchema = z.object({
  user_id: z.string().uuid(),
  job_id: z.string().uuid(),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  custom_cover_letter: z.string().optional(),
  custom_resume_url: z.string().url().optional(),
  scheduled_at: z.string().datetime().optional(),
  metadata: z.record(z.any()).default({}),
});

export type ApplicationRequest = z.infer<typeof ApplicationRequestSchema>;

// ATS Platform Types
export const ATSPlatformEnum = z.enum([
  'greenhouse',
  'lever',
  'workday',
  'bamboohr',
  'icims',
  'jobvite',
  'smartrecruiters',
  'cornerstone',
  'taleo',
  'successfactors',
  'recruiterbox',
  'breezy',
  'jazzhire',
  'personio',
  'teamtailor',
  'recruitee',
  'manatal',
  'zoho',
  'freshteam',
  'linkedin',
  'indeed',
  'generic',
]);

export type ATSPlatform = z.infer<typeof ATSPlatformEnum>;

// Browser Automation Types
export const BrowserContextSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  browser_session_id: z.string(),
  user_agent: z.string(),
  viewport: z.object({
    width: z.number(),
    height: z.number(),
  }),
  proxy: z.object({
    server: z.string(),
    username: z.string().optional(),
    password: z.string().optional(),
  }).optional(),
  created_at: z.string().datetime(),
  last_used: z.string().datetime(),
  is_active: z.boolean(),
});

export type BrowserContext = z.infer<typeof BrowserContextSchema>;

// Application Recipe Types
export const RecipeStepSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['navigate', 'click', 'type', 'select', 'upload', 'wait', 'submit', 'screenshot', 'extract', 'validate']),
  selector: z.string().optional(),
  value: z.string().optional(),
  timeout: z.number().min(1000).max(60000).default(5000),
  required: z.boolean().default(true),
  retry_on_fail: z.boolean().default(true),
  wait_after: z.number().min(0).max(10000).default(500),
  condition: z.object({
    type: z.enum(['element_visible', 'element_clickable', 'text_present', 'url_contains', 'custom']),
    value: z.string().optional(),
    timeout: z.number().default(5000),
  }).optional(),
});

export type RecipeStep = z.infer<typeof RecipeStepSchema>;

export const ApplicationRecipeSchema = z.object({
  id: z.string(),
  name: z.string(),
  ats_platform: ATSPlatformEnum,
  company: z.string().optional(),
  url_pattern: z.string(),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),
  steps: z.array(RecipeStepSchema),
  field_mappings: z.record(z.string()),
  prerequisites: z.array(z.string()).default([]),
  success_indicators: z.array(z.string()),
  failure_indicators: z.array(z.string()),
  estimated_duration: z.number().min(10).max(600).default(60), // seconds
  retry_strategy: z.object({
    max_attempts: z.number().min(1).max(5).default(3),
    delay_between_attempts: z.number().min(1000).max(30000).default(5000),
    exponential_backoff: z.boolean().default(true),
  }),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  is_active: z.boolean().default(true),
});

export type ApplicationRecipe = z.infer<typeof ApplicationRecipeSchema>;

// Queue Job Types
export const QueueJobDataSchema = z.object({
  application_request: ApplicationRequestSchema,
  job: JobSchema,
  user: UserProfileSchema,
  recipe_id: z.string().optional(),
  priority: z.number().min(1).max(10).default(5),
  retry_count: z.number().min(0).default(0),
  scheduled_at: z.string().datetime().optional(),
  metadata: z.record(z.any()).default({}),
});

export type QueueJobData = z.infer<typeof QueueJobDataSchema>;

// API Response Types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.any().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
  }).optional(),
  timestamp: z.string().datetime(),
  request_id: z.string().uuid().optional(),
});

export type ApiResponse<T = any> = Omit<z.infer<typeof ApiResponseSchema>, 'data'> & {
  data?: T;
};

// Metrics Types
export const ApplicationMetricsSchema = z.object({
  total_applications: z.number().min(0),
  successful_applications: z.number().min(0),
  failed_applications: z.number().min(0),
  pending_applications: z.number().min(0),
  success_rate: z.number().min(0).max(1),
  average_application_time: z.number().min(0), // seconds
  applications_per_hour: z.number().min(0),
  ats_platform_breakdown: z.record(z.number()),
  error_breakdown: z.record(z.number()),
  timestamp: z.string().datetime(),
});

export type ApplicationMetrics = z.infer<typeof ApplicationMetricsSchema>;

// Webhook Types
export const WebhookEventSchema = z.object({
  event_type: z.enum(['application_submitted', 'application_failed', 'application_acknowledged', 'status_updated']),
  application_id: z.string().uuid(),
  user_id: z.string().uuid(),
  job_id: z.string().uuid(),
  status: ApplicationStatusEnum,
  timestamp: z.string().datetime(),
  data: z.record(z.any()).default({}),
});

export type WebhookEvent = z.infer<typeof WebhookEventSchema>;

// Error Types
export class ApplicationError extends Error {
  public readonly code: string;
  public readonly details?: any;
  public readonly retryable: boolean;

  constructor(message: string, code: string, details?: any, retryable: boolean = false) {
    super(message);
    this.name = 'ApplicationError';
    this.code = code;
    this.details = details;
    this.retryable = retryable;
  }
}

export class ATSError extends ApplicationError {
  constructor(message: string, atsPlatform: string, details?: any, retryable: boolean = true) {
    super(message, `ATS_ERROR_${atsPlatform.toUpperCase()}`, details, retryable);
    this.name = 'ATSError';
  }
}

export class BrowserError extends ApplicationError {
  constructor(message: string, details?: any, retryable: boolean = true) {
    super(message, 'BROWSER_ERROR', details, retryable);
    this.name = 'BrowserError';
  }
}

export class ValidationError extends ApplicationError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details, false);
    this.name = 'ValidationError';
  }
}

export class RateLimitError extends ApplicationError {
  constructor(message: string, details?: any) {
    super(message, 'RATE_LIMIT_ERROR', details, true);
    this.name = 'RateLimitError';
  }
}