import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { register } from 'prom-client';

import config from '@/config';
import { logger } from '@/utils/logger';
import { BrowserManager } from '@/automation/browser-manager';
import { RecipeEngine } from '@/recipes/recipe-engine';
import { ApplicationProcessor } from '@/services/application-processor';
import { DatabaseService } from '@/services/database-service';
import { MetricsService } from '@/services/metrics-service';
import { NotificationService } from '@/services/notification-service';
import { ApplicationRequest, ApiResponse } from '@/models/types';

class StagehandWorkerServer {
  private app: express.Application;
  private server: any;
  private metricsServer: any;
  
  // Services
  private metrics: MetricsService;
  private database: DatabaseService;
  private browserManager: BrowserManager;
  private recipeEngine: RecipeEngine;
  private notifications: NotificationService;
  private processor: ApplicationProcessor;

  private isShuttingDown = false;

  constructor() {
    this.app = express();
    this.setupServices();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupServices(): void {
    this.metrics = new MetricsService();
    this.database = new DatabaseService();
    this.browserManager = new BrowserManager(this.metrics);
    this.recipeEngine = new RecipeEngine(this.browserManager, this.metrics);
    this.notifications = new NotificationService(this.metrics);
    this.processor = new ApplicationProcessor(
      this.browserManager,
      this.recipeEngine,
      this.metrics,
      this.database,
      this.notifications
    );
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // Disable CSP for API
    }));

    // CORS
    this.app.use(cors({
      origin: config.nodeEnv === 'development' ? true : [], // Configure for production
      credentials: true,
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // Limit each IP to 1000 requests per windowMs
      message: 'Too many requests from this IP',
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      logger.debug('Incoming request', {
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoints
    this.app.get('/health', this.handleHealthCheck.bind(this));
    this.app.get('/health/ready', this.handleReadinessCheck.bind(this));
    this.app.get('/health/live', this.handleLivenessCheck.bind(this));

    // Metrics endpoint
    this.app.get('/metrics', this.handleMetrics.bind(this));

    // Application endpoints
    this.app.post('/api/v1/applications', this.handleCreateApplication.bind(this));
    this.app.get('/api/v1/applications/:id', this.handleGetApplication.bind(this));
    this.app.get('/api/v1/applications/user/:userId', this.handleGetUserApplications.bind(this));
    this.app.get('/api/v1/applications/status/:status', this.handleGetApplicationsByStatus.bind(this));

    // Queue management endpoints
    this.app.get('/api/v1/queue/stats', this.handleGetQueueStats.bind(this));
    this.app.post('/api/v1/queue/pause', this.handlePauseQueue.bind(this));
    this.app.post('/api/v1/queue/resume', this.handleResumeQueue.bind(this));

    // Recipe management endpoints
    this.app.get('/api/v1/recipes', this.handleListRecipes.bind(this));
    this.app.get('/api/v1/recipes/:id', this.handleGetRecipe.bind(this));
    this.app.post('/api/v1/recipes', this.handleCreateRecipe.bind(this));

    // Statistics endpoints
    this.app.get('/api/v1/stats/applications', this.handleGetApplicationStats.bind(this));
    this.app.get('/api/v1/stats/browser', this.handleGetBrowserStats.bind(this));
    this.app.get('/api/v1/stats/service', this.handleGetServiceStats.bind(this));

    // 404 handler
    this.app.all('*', (req, res) => {
      this.sendResponse(res, 404, false, 'Endpoint not found');
    });
  }

  private setupErrorHandling(): void {
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error in request', {
        error: error.message,
        stack: error.stack,
        method: req.method,
        url: req.url,
      });

      this.sendResponse(res, 500, false, 'Internal server error', undefined, {
        code: 'INTERNAL_ERROR',
        message: config.nodeEnv === 'development' ? error.message : 'Internal server error',
      });
    });
  }

  async start(): Promise<void> {
    try {
      logger.info('Starting Stagehand Worker server', {
        port: config.port,
        metricsPort: config.metricsPort,
        environment: config.nodeEnv,
      });

      // Initialize services
      await this.metrics.initialize();
      await this.database.initialize();
      await this.browserManager.initialize();
      await this.recipeEngine.initialize();
      await this.notifications.initialize();
      await this.processor.initialize();

      // Start main server
      this.server = this.app.listen(config.port, () => {
        logger.info('Stagehand Worker server started', { port: config.port });
      });

      // Start metrics server
      this.metricsServer = express()
        .get('/metrics', async (req, res) => {
          res.set('Content-Type', register.contentType);
          res.end(await register.metrics());
        })
        .listen(config.metricsPort, () => {
          logger.info('Metrics server started', { port: config.metricsPort });
        });

      // Setup graceful shutdown
      this.setupGracefulShutdown();

      // Start performance monitoring
      this.startPerformanceMonitoring();

    } catch (error) {
      logger.error('Failed to start server', { error: error instanceof Error ? error.message : String(error) });
      await this.shutdown();
      process.exit(1);
    }
  }

  private async handleHealthCheck(req: express.Request, res: express.Response): Promise<void> {
    this.sendResponse(res, 200, true, 'Service is healthy', {
      service: config.serviceName,
      version: '1.0.0',
      environment: config.nodeEnv,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  }

  private async handleReadinessCheck(req: express.Request, res: express.Response): Promise<void> {
    try {
      // Check if all services are ready
      const checks = {
        database: false,
        browserManager: false,
        processor: false,
      };

      // Database check
      try {
        await this.database.getApplicationStats();
        checks.database = true;
      } catch (error) {
        logger.warn('Database readiness check failed', { error: error instanceof Error ? error.message : String(error) });
      }

      // Browser manager check
      try {
        const stats = await this.browserManager.getStats();
        checks.browserManager = stats.totalBrowsers > 0;
      } catch (error) {
        logger.warn('Browser manager readiness check failed', { error: error instanceof Error ? error.message : String(error) });
      }

      // Processor check
      try {
        await this.processor.getQueueStats();
        checks.processor = true;
      } catch (error) {
        logger.warn('Processor readiness check failed', { error: error instanceof Error ? error.message : String(error) });
      }

      const isReady = Object.values(checks).every(check => check);
      const status = isReady ? 200 : 503;

      this.sendResponse(res, status, isReady, isReady ? 'Service is ready' : 'Service is not ready', {
        checks,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      this.sendResponse(res, 503, false, 'Readiness check failed', undefined, {
        code: 'READINESS_CHECK_FAILED',
        message: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private async handleLivenessCheck(req: express.Request, res: express.Response): Promise<void> {
    if (this.isShuttingDown) {
      this.sendResponse(res, 503, false, 'Service is shutting down');
      return;
    }

    this.sendResponse(res, 200, true, 'Service is alive', {
      timestamp: new Date().toISOString(),
    });
  }

  private async handleMetrics(req: express.Request, res: express.Response): Promise<void> {
    try {
      const metrics = await this.metrics.getPrometheusMetrics();
      res.set('Content-Type', register.contentType);
      res.send(metrics);
    } catch (error) {
      logger.error('Failed to get metrics', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).send('Failed to get metrics');
    }
  }

  private async handleCreateApplication(req: express.Request, res: express.Response): Promise<void> {
    try {
      const applicationRequest: ApplicationRequest = req.body;
      
      // Validate required fields
      if (!applicationRequest.user_id || !applicationRequest.job_id) {
        this.sendResponse(res, 400, false, 'user_id and job_id are required');
        return;
      }

      const jobId = await this.processor.addApplicationToQueue(applicationRequest);
      
      this.sendResponse(res, 201, true, 'Application added to queue', {
        queueJobId: jobId,
        userId: applicationRequest.user_id,
        jobId: applicationRequest.job_id,
      });

    } catch (error) {
      logger.error('Failed to create application', { error: error instanceof Error ? error.message : String(error) });
      
      if (error instanceof Error && error.message.includes('not found')) {
        this.sendResponse(res, 404, false, error.message);
      } else if (error instanceof Error && error.message.includes('already exists')) {
        this.sendResponse(res, 409, false, error.message);
      } else {
        this.sendResponse(res, 500, false, 'Failed to create application', undefined, {
          code: 'APPLICATION_CREATION_FAILED',
          message: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  private async handleGetApplication(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { id } = req.params;
      // Implementation would fetch application from database
      this.sendResponse(res, 501, false, 'Not implemented yet');
    } catch (error) {
      this.sendResponse(res, 500, false, 'Failed to get application');
    }
  }

  private async handleGetUserApplications(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { userId } = req.params;
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      const applications = await this.database.getApplicationsByUserId(userId, limit, offset);
      
      this.sendResponse(res, 200, true, 'Applications retrieved successfully', {
        applications,
        count: applications.length,
        limit,
        offset,
      });

    } catch (error) {
      logger.error('Failed to get user applications', { 
        userId: req.params.userId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      this.sendResponse(res, 500, false, 'Failed to get user applications');
    }
  }

  private async handleGetApplicationsByStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { status } = req.params;
      const limit = parseInt(req.query.limit as string) || 100;
      const offset = parseInt(req.query.offset as string) || 0;

      const applications = await this.database.getApplicationsByStatus(status, limit, offset);
      
      this.sendResponse(res, 200, true, 'Applications retrieved successfully', {
        applications,
        status,
        count: applications.length,
        limit,
        offset,
      });

    } catch (error) {
      logger.error('Failed to get applications by status', { 
        status: req.params.status, 
        error: error instanceof Error ? error.message : String(error) 
      });
      this.sendResponse(res, 500, false, 'Failed to get applications by status');
    }
  }

  private async handleGetQueueStats(req: express.Request, res: express.Response): Promise<void> {
    try {
      const stats = await this.processor.getQueueStats();
      this.sendResponse(res, 200, true, 'Queue stats retrieved successfully', stats);
    } catch (error) {
      logger.error('Failed to get queue stats', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to get queue stats');
    }
  }

  private async handlePauseQueue(req: express.Request, res: express.Response): Promise<void> {
    try {
      await this.processor.pauseProcessing();
      this.sendResponse(res, 200, true, 'Queue processing paused');
    } catch (error) {
      logger.error('Failed to pause queue', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to pause queue');
    }
  }

  private async handleResumeQueue(req: express.Request, res: express.Response): Promise<void> {
    try {
      await this.processor.resumeProcessing();
      this.sendResponse(res, 200, true, 'Queue processing resumed');
    } catch (error) {
      logger.error('Failed to resume queue', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to resume queue');
    }
  }

  private async handleListRecipes(req: express.Request, res: express.Response): Promise<void> {
    try {
      const recipes = await this.recipeEngine.listRecipes();
      this.sendResponse(res, 200, true, 'Recipes retrieved successfully', { recipes });
    } catch (error) {
      logger.error('Failed to list recipes', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to list recipes');
    }
  }

  private async handleGetRecipe(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { id } = req.params;
      const recipe = await this.recipeEngine.getRecipe(id);
      
      if (!recipe) {
        this.sendResponse(res, 404, false, 'Recipe not found');
        return;
      }

      this.sendResponse(res, 200, true, 'Recipe retrieved successfully', recipe);
    } catch (error) {
      logger.error('Failed to get recipe', { 
        recipeId: req.params.id, 
        error: error instanceof Error ? error.message : String(error) 
      });
      this.sendResponse(res, 500, false, 'Failed to get recipe');
    }
  }

  private async handleCreateRecipe(req: express.Request, res: express.Response): Promise<void> {
    try {
      const recipe = req.body;
      await this.recipeEngine.addRecipe(recipe);
      this.sendResponse(res, 201, true, 'Recipe created successfully', { recipeId: recipe.id });
    } catch (error) {
      logger.error('Failed to create recipe', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to create recipe');
    }
  }

  private async handleGetApplicationStats(req: express.Request, res: express.Response): Promise<void> {
    try {
      const userId = req.query.userId as string;
      const stats = await this.database.getApplicationStats(userId);
      this.sendResponse(res, 200, true, 'Application stats retrieved successfully', stats);
    } catch (error) {
      logger.error('Failed to get application stats', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to get application stats');
    }
  }

  private async handleGetBrowserStats(req: express.Request, res: express.Response): Promise<void> {
    try {
      const stats = await this.browserManager.getStats();
      this.sendResponse(res, 200, true, 'Browser stats retrieved successfully', stats);
    } catch (error) {
      logger.error('Failed to get browser stats', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to get browser stats');
    }
  }

  private async handleGetServiceStats(req: express.Request, res: express.Response): Promise<void> {
    try {
      const stats = {
        service: config.serviceName,
        version: '1.0.0',
        environment: config.nodeEnv,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        metrics: this.metrics.getStats(),
        applications: this.metrics.getApplicationMetrics(),
      };

      this.sendResponse(res, 200, true, 'Service stats retrieved successfully', stats);
    } catch (error) {
      logger.error('Failed to get service stats', { error: error instanceof Error ? error.message : String(error) });
      this.sendResponse(res, 500, false, 'Failed to get service stats');
    }
  }

  private sendResponse<T>(
    res: express.Response,
    status: number,
    success: boolean,
    message?: string,
    data?: T,
    error?: { code: string; message: string; details?: any }
  ): void {
    const response: ApiResponse<T> = {
      success,
      message,
      data,
      error,
      timestamp: new Date().toISOString(),
    };

    res.status(status).json(response);
  }

  private setupGracefulShutdown(): void {
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];
    
    signals.forEach(signal => {
      process.on(signal, async () => {
        logger.info(`Received ${signal}, starting graceful shutdown`);
        await this.shutdown();
        process.exit(0);
      });
    });

    process.on('uncaughtException', async (error) => {
      logger.error('Uncaught exception', { error: error.message, stack: error.stack });
      await this.notifications.sendSystemAlert('critical', 'Uncaught exception', { error: error.message });
      await this.shutdown();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      await this.notifications.sendSystemAlert('critical', 'Unhandled rejection', { reason });
    });
  }

  private async shutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    logger.info('Starting graceful shutdown');

    try {
      // Close servers
      if (this.server) {
        await new Promise<void>((resolve) => {
          this.server.close(() => resolve());
        });
      }

      if (this.metricsServer) {
        await new Promise<void>((resolve) => {
          this.metricsServer.close(() => resolve());
        });
      }

      // Shutdown services
      await this.processor.shutdown();
      await this.browserManager.cleanup();
      await this.database.cleanup();

      logger.info('Graceful shutdown completed');
    } catch (error) {
      logger.error('Error during shutdown', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  private startPerformanceMonitoring(): void {
    setInterval(async () => {
      try {
        const [queueStats, applicationMetrics] = await Promise.all([
          this.processor.getQueueStats(),
          this.metrics.getApplicationMetrics(),
        ]);

        const performanceStats = {
          applicationsPerHour: applicationMetrics.applications_per_hour,
          successRate: applicationMetrics.success_rate,
          averageProcessingTime: applicationMetrics.average_application_time,
          queueStats,
        };

        await this.notifications.sendPerformanceReport(performanceStats);
      } catch (error) {
        logger.error('Performance monitoring failed', { error: error instanceof Error ? error.message : String(error) });
      }
    }, 60 * 60 * 1000); // Every hour
  }
}

// Start the server
const server = new StagehandWorkerServer();
server.start().catch((error) => {
  logger.error('Failed to start server', { error: error instanceof Error ? error.message : String(error) });
  process.exit(1);
});

export default server;