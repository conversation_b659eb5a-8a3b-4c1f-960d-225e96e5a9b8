import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';

import { BrowserManager } from '@/automation/browser-manager';
import { logger } from '@/utils/logger';
import { MetricsService } from '@/services/metrics-service';
import {
  ApplicationRecipe,
  RecipeStep,
  Job,
  UserProfile,
  Application,
  ApplicationError,
  ATSError,
  BrowserError,
} from '@/models/types';
import config from '@/config';

interface RecipeExecutionContext {
  sessionId: string;
  job: Job;
  user: UserProfile;
  recipe: ApplicationRecipe;
  variables: Record<string, any>;
  screenshots: string[];
  logs: Array<{
    timestamp: string;
    action: string;
    status: string;
    details?: string;
    screenshot_url?: string;
  }>;
}

export class RecipeEngine {
  private browserManager: BrowserManager;
  private metrics: MetricsService;
  private recipes: Map<string, ApplicationRecipe> = new Map();

  constructor(browserManager: BrowserManager, metrics: MetricsService) {
    this.browserManager = browserManager;
    this.metrics = metrics;
  }

  async initialize(): Promise<void> {
    try {
      await this.loadDefaultRecipes();
      logger.info('Recipe engine initialized', { recipeCount: this.recipes.size });
    } catch (error) {
      logger.error('Failed to initialize recipe engine', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async executeRecipe(
    job: Job,
    user: UserProfile,
    recipeId?: string
  ): Promise<Application> {
    const startTime = Date.now();
    let sessionId: string | null = null;

    try {
      // Find appropriate recipe
      const recipe = recipeId ? 
        this.recipes.get(recipeId) : 
        await this.findRecipeForJob(job);

      if (!recipe) {
        throw new ApplicationError(
          `No suitable recipe found for job ${job.id}`,
          'RECIPE_NOT_FOUND',
          { jobId: job.id, atsPlatform: job.ats_platform }
        );
      }

      logger.info('Starting recipe execution', {
        jobId: job.id,
        userId: user.id,
        recipeId: recipe.id,
        atsPlatform: recipe.ats_platform,
      });

      this.metrics.recordApplicationStart(job.id, recipe.ats_platform);
      this.metrics.incrementCounter('recipe_executions_total', { 
        recipe_id: recipe.id, 
        status: 'started' 
      });

      // Create browser session
      sessionId = await this.browserManager.createSession(user.id);

      // Initialize execution context
      const context: RecipeExecutionContext = {
        sessionId,
        job,
        user,
        recipe,
        variables: this.buildInitialVariables(job, user),
        screenshots: [],
        logs: [],
      };

      // Execute recipe steps
      const application = await this.executeSteps(context);

      // Record success metrics
      const duration = Date.now() - startTime;
      this.metrics.recordApplicationComplete(
        job.id,
        recipe.ats_platform,
        application.status,
        duration
      );

      this.metrics.incrementCounter('recipe_executions_total', { 
        recipe_id: recipe.id, 
        status: 'completed' 
      });

      this.metrics.observeHistogram('recipe_execution_duration_seconds', duration / 1000, {
        recipe_id: recipe.id,
      });

      logger.info('Recipe execution completed successfully', {
        jobId: job.id,
        userId: user.id,
        recipeId: recipe.id,
        applicationId: application.id,
        duration: `${duration}ms`,
      });

      return application;

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorCode = error instanceof ApplicationError ? error.code : 'UNKNOWN_ERROR';

      // Record failure metrics
      this.metrics.recordApplicationComplete(
        job.id,
        job.ats_platform || 'unknown',
        'failed',
        duration,
        errorCode
      );

      this.metrics.incrementCounter('recipe_executions_total', { 
        recipe_id: recipeId || 'unknown', 
        status: 'failed' 
      });

      this.metrics.recordError(errorCode, job.ats_platform);

      logger.error('Recipe execution failed', {
        jobId: job.id,
        userId: user.id,
        recipeId: recipeId,
        error: errorMessage,
        errorCode,
        duration: `${duration}ms`,
      });

      // Create failed application record
      const failedApplication: Application = {
        id: uuidv4(),
        user_id: user.id,
        job_id: job.id,
        status: 'failed',
        error_message: errorMessage,
        last_updated: new Date().toISOString(),
        retry_count: 0,
        automation_log: [],
        screenshot_urls: [],
        metadata: { errorCode, duration },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      throw error;

    } finally {
      // Clean up browser session
      if (sessionId) {
        try {
          await this.browserManager.closeSession(sessionId);
        } catch (error) {
          logger.error('Failed to close browser session', { sessionId, error: error instanceof Error ? error.message : String(error) });
        }
      }
    }
  }

  async addRecipe(recipe: ApplicationRecipe): Promise<void> {
    this.recipes.set(recipe.id, recipe);
    logger.info('Recipe added', { recipeId: recipe.id, atsPlatform: recipe.ats_platform });
  }

  async getRecipe(recipeId: string): Promise<ApplicationRecipe | undefined> {
    return this.recipes.get(recipeId);
  }

  async listRecipes(): Promise<ApplicationRecipe[]> {
    return Array.from(this.recipes.values());
  }

  async findRecipeForJob(job: Job): Promise<ApplicationRecipe | null> {
    // Try to find recipe by ATS platform first
    if (job.ats_platform) {
      for (const recipe of this.recipes.values()) {
        if (recipe.ats_platform === job.ats_platform && recipe.is_active) {
          // Check if URL pattern matches
          if (this.urlMatchesPattern(job.url, recipe.url_pattern)) {
            return recipe;
          }
        }
      }
    }

    // Try to find recipe by URL pattern
    for (const recipe of this.recipes.values()) {
      if (recipe.is_active && this.urlMatchesPattern(job.url, recipe.url_pattern)) {
        return recipe;
      }
    }

    // Try to detect ATS platform from URL
    const detectedPlatform = this.detectATSFromUrl(job.url);
    if (detectedPlatform) {
      for (const recipe of this.recipes.values()) {
        if (recipe.ats_platform === detectedPlatform && recipe.is_active) {
          return recipe;
        }
      }
    }

    return null;
  }

  private async executeSteps(context: RecipeExecutionContext): Promise<Application> {
    const application: Application = {
      id: uuidv4(),
      user_id: context.user.id,
      job_id: context.job.id,
      status: 'in_progress',
      last_updated: new Date().toISOString(),
      retry_count: 0,
      automation_log: [],
      screenshot_urls: [],
      metadata: { recipeId: context.recipe.id },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    try {
      // Navigate to job URL
      await this.browserManager.navigateToUrl(context.sessionId, context.job.url);
      await this.logAction(context, 'navigate', 'success', `Navigated to ${context.job.url}`);

      // Wait a moment for page to load
      await this.delay(2000);

      // Execute recipe steps
      for (let i = 0; i < context.recipe.steps.length; i++) {
        const step = context.recipe.steps[i];
        
        try {
          await this.executeStep(context, step, i);
          await this.logAction(context, step.type, 'success', `Step ${i + 1}: ${step.name}`);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          await this.logAction(context, step.type, 'error', `Step ${i + 1} failed: ${errorMessage}`);

          if (step.required && !step.retry_on_fail) {
            throw new ATSError(
              `Required step failed: ${step.name}`,
              context.recipe.ats_platform,
              { stepIndex: i, stepName: step.name, error: errorMessage }
            );
          }

          if (step.retry_on_fail) {
            logger.warn('Retrying failed step', { stepIndex: i, stepName: step.name });
            try {
              await this.delay(1000);
              await this.executeStep(context, step, i);
              await this.logAction(context, step.type, 'success', `Step ${i + 1} retry succeeded: ${step.name}`);
            } catch (retryError) {
              const retryErrorMessage = retryError instanceof Error ? retryError.message : String(retryError);
              await this.logAction(context, step.type, 'error', `Step ${i + 1} retry failed: ${retryErrorMessage}`);
              
              if (step.required) {
                throw new ATSError(
                  `Required step failed after retry: ${step.name}`,
                  context.recipe.ats_platform,
                  { stepIndex: i, stepName: step.name, error: retryErrorMessage }
                );
              }
            }
          }
        }

        // Wait after step if specified
        if (step.wait_after > 0) {
          await this.delay(step.wait_after);
        }
      }

      // Check for success indicators
      const success = await this.checkSuccessIndicators(context);
      
      if (success) {
        application.status = 'submitted';
        application.applied_at = new Date().toISOString();
        
        // Try to extract confirmation number or application ID
        const confirmationNumber = await this.extractConfirmationNumber(context);
        if (confirmationNumber) {
          application.confirmation_number = confirmationNumber;
        }

        await this.logAction(context, 'completion', 'success', 'Application submitted successfully');
      } else {
        // Check for failure indicators
        const failureReason = await this.checkFailureIndicators(context);
        throw new ATSError(
          failureReason || 'Application submission failed - success indicators not found',
          context.recipe.ats_platform
        );
      }

      // Take final screenshot
      const finalScreenshot = await this.takeScreenshot(context, 'final');
      if (finalScreenshot) {
        context.screenshots.push(finalScreenshot);
      }

      // Update application with final data
      application.automation_log = context.logs;
      application.screenshot_urls = context.screenshots;
      application.updated_at = new Date().toISOString();

      return application;

    } catch (error) {
      // Take error screenshot
      try {
        const errorScreenshot = await this.takeScreenshot(context, 'error');
        if (errorScreenshot) {
          context.screenshots.push(errorScreenshot);
        }
      } catch (screenshotError) {
        logger.warn('Failed to take error screenshot', { error: screenshotError instanceof Error ? screenshotError.message : String(screenshotError) });
      }

      // Update application with error data
      application.status = 'failed';
      application.error_message = error instanceof Error ? error.message : String(error);
      application.automation_log = context.logs;
      application.screenshot_urls = context.screenshots;
      application.updated_at = new Date().toISOString();

      throw error;
    }
  }

  private async executeStep(context: RecipeExecutionContext, step: RecipeStep, stepIndex: number): Promise<void> {
    const startTime = Date.now();

    try {
      logger.debug('Executing step', { 
        stepIndex, 
        stepName: step.name, 
        stepType: step.type,
        sessionId: context.sessionId 
      });

      // Check condition if specified
      if (step.condition) {
        const conditionMet = await this.checkCondition(context, step.condition);
        if (!conditionMet) {
          throw new BrowserError(`Step condition not met: ${step.condition.type}`);
        }
      }

      // Execute step based on type
      switch (step.type) {
        case 'navigate':
          if (!step.value) throw new BrowserError('Navigate step requires URL value');
          await this.browserManager.navigateToUrl(context.sessionId, this.resolveVariable(step.value, context.variables));
          break;

        case 'click':
          if (!step.selector) throw new BrowserError('Click step requires selector');
          await this.browserManager.clickElement(context.sessionId, step.selector);
          break;

        case 'type':
          if (!step.selector || !step.value) throw new BrowserError('Type step requires selector and value');
          const textValue = this.resolveVariable(step.value, context.variables);
          await this.browserManager.typeText(context.sessionId, step.selector, textValue, { clear: true });
          break;

        case 'select':
          if (!step.selector || !step.value) throw new BrowserError('Select step requires selector and value');
          const selectValue = this.resolveVariable(step.value, context.variables);
          await this.browserManager.selectOption(context.sessionId, step.selector, selectValue);
          break;

        case 'upload':
          if (!step.selector || !step.value) throw new BrowserError('Upload step requires selector and file path');
          const filePath = this.resolveVariable(step.value, context.variables);
          await this.browserManager.uploadFile(context.sessionId, step.selector, filePath);
          break;

        case 'wait':
          const waitTime = step.value ? parseInt(step.value) : step.timeout;
          await this.delay(waitTime);
          break;

        case 'submit':
          if (step.selector) {
            await this.browserManager.clickElement(context.sessionId, step.selector);
          } else {
            // Try common submit selectors
            const commonSubmitSelectors = [
              'button[type="submit"]',
              'input[type="submit"]',
              '.submit-button',
              '#submit',
              '[data-testid="submit"]',
            ];
            
            let submitted = false;
            for (const selector of commonSubmitSelectors) {
              try {
                await this.browserManager.clickElement(context.sessionId, selector);
                submitted = true;
                break;
              } catch (error) {
                // Continue to next selector
              }
            }
            
            if (!submitted) {
              throw new BrowserError('Could not find submit button');
            }
          }
          break;

        case 'screenshot':
          const screenshotPath = await this.takeScreenshot(context, `step-${stepIndex}`);
          if (screenshotPath) {
            context.screenshots.push(screenshotPath);
          }
          break;

        case 'extract':
          if (!step.selector) throw new BrowserError('Extract step requires selector');
          const extractedText = await this.browserManager.extractText(context.sessionId, step.selector);
          if (step.value) {
            context.variables[step.value] = extractedText;
          }
          break;

        case 'validate':
          if (!step.selector) throw new BrowserError('Validate step requires selector');
          await this.browserManager.waitForSelector(context.sessionId, step.selector, step.timeout);
          break;

        default:
          throw new BrowserError(`Unknown step type: ${step.type}`);
      }

      const duration = Date.now() - startTime;
      this.metrics.recordBrowserAction(step.type, true, duration);

    } catch (error) {
      const duration = Date.now() - startTime;
      this.metrics.recordBrowserAction(step.type, false, duration);
      throw error;
    }
  }

  private async checkCondition(context: RecipeExecutionContext, condition: RecipeStep['condition']): Promise<boolean> {
    if (!condition) return true;

    try {
      const page = await this.browserManager.getSession(context.sessionId);

      switch (condition.type) {
        case 'element_visible':
          if (!condition.value) return false;
          try {
            await page.waitForSelector(condition.value, { visible: true, timeout: condition.timeout });
            return true;
          } catch {
            return false;
          }

        case 'element_clickable':
          if (!condition.value) return false;
          try {
            await page.waitForSelector(condition.value, { visible: true, timeout: condition.timeout });
            const element = await page.$(condition.value);
            return element !== null;
          } catch {
            return false;
          }

        case 'text_present':
          if (!condition.value) return false;
          try {
            await page.waitForFunction(
              (text) => document.body.textContent?.includes(text),
              { timeout: condition.timeout },
              condition.value
            );
            return true;
          } catch {
            return false;
          }

        case 'url_contains':
          if (!condition.value) return false;
          return page.url().includes(condition.value);

        default:
          return true;
      }
    } catch (error) {
      logger.warn('Condition check failed', { condition: condition.type, error: error instanceof Error ? error.message : String(error) });
      return false;
    }
  }

  private async checkSuccessIndicators(context: RecipeExecutionContext): Promise<boolean> {
    const page = await this.browserManager.getSession(context.sessionId);
    
    for (const indicator of context.recipe.success_indicators) {
      try {
        if (indicator.startsWith('text:')) {
          const text = indicator.substring(5);
          const found = await page.evaluate((searchText) => {
            return document.body.textContent?.toLowerCase().includes(searchText.toLowerCase()) || false;
          }, text);
          if (found) return true;
        } else if (indicator.startsWith('url:')) {
          const urlPart = indicator.substring(4);
          if (page.url().includes(urlPart)) return true;
        } else {
          // Treat as CSS selector
          try {
            await page.waitForSelector(indicator, { visible: true, timeout: 5000 });
            return true;
          } catch {
            // Continue to next indicator
          }
        }
      } catch (error) {
        logger.debug('Success indicator check failed', { indicator, error: error instanceof Error ? error.message : String(error) });
      }
    }

    return false;
  }

  private async checkFailureIndicators(context: RecipeExecutionContext): Promise<string | null> {
    const page = await this.browserManager.getSession(context.sessionId);
    
    for (const indicator of context.recipe.failure_indicators) {
      try {
        if (indicator.startsWith('text:')) {
          const text = indicator.substring(5);
          const found = await page.evaluate((searchText) => {
            return document.body.textContent?.toLowerCase().includes(searchText.toLowerCase()) || false;
          }, text);
          if (found) return `Failure text found: ${text}`;
        } else if (indicator.startsWith('url:')) {
          const urlPart = indicator.substring(4);
          if (page.url().includes(urlPart)) return `Failure URL detected: ${urlPart}`;
        } else {
          // Treat as CSS selector
          try {
            await page.waitForSelector(indicator, { visible: true, timeout: 2000 });
            return `Failure element found: ${indicator}`;
          } catch {
            // Continue to next indicator
          }
        }
      } catch (error) {
        logger.debug('Failure indicator check failed', { indicator, error: error instanceof Error ? error.message : String(error) });
      }
    }

    return null;
  }

  private async extractConfirmationNumber(context: RecipeExecutionContext): Promise<string | null> {
    const page = await this.browserManager.getSession(context.sessionId);
    
    const confirmationSelectors = [
      '[data-testid="confirmation-number"]',
      '.confirmation-number',
      '.application-id',
      '#confirmation',
      '#application-id',
    ];

    for (const selector of confirmationSelectors) {
      try {
        const element = await page.$(selector);
        if (element) {
          const text = await element.evaluate(el => el.textContent || '');
          const match = text.match(/[A-Z0-9-]{8,}/);
          if (match) {
            return match[0];
          }
        }
      } catch {
        // Continue to next selector
      }
    }

    // Try to find confirmation number in page text
    try {
      const pageText = await page.evaluate(() => document.body.textContent || '');
      const patterns = [
        /application\s+(?:id|number):\s*([A-Z0-9-]{8,})/i,
        /confirmation\s+(?:id|number):\s*([A-Z0-9-]{8,})/i,
        /reference\s+(?:id|number):\s*([A-Z0-9-]{8,})/i,
      ];

      for (const pattern of patterns) {
        const match = pageText.match(pattern);
        if (match) {
          return match[1];
        }
      }
    } catch (error) {
      logger.debug('Failed to extract confirmation number from page text', { error: error instanceof Error ? error.message : String(error) });
    }

    return null;
  }

  private buildInitialVariables(job: Job, user: UserProfile): Record<string, any> {
    return {
      // User variables
      firstName: user.name.split(' ')[0] || '',
      lastName: user.name.split(' ').slice(1).join(' ') || '',
      fullName: user.name,
      email: user.email,
      phone: user.phone || '',
      location: user.location || '',
      country: user.country,
      linkedinUrl: user.linkedin_url || '',
      githubUrl: user.github_url || '',
      portfolioUrl: user.portfolio_url || '',
      currentTitle: user.current_title || '',
      experienceYears: user.experience_years || 0,
      
      // Job variables
      jobTitle: job.title,
      companyName: job.company,
      jobLocation: job.location || '',
      jobUrl: job.url,
      
      // File paths
      resumePath: user.resume_url ? this.downloadFile(user.resume_url) : '',
      coverLetterPath: this.generateCoverLetterPath(user, job),
      
      // Application variables
      applicationDate: new Date().toLocaleDateString(),
      
      // Skills as comma-separated string
      skills: user.skills.join(', '),
    };
  }

  private resolveVariable(value: string, variables: Record<string, any>): string {
    return value.replace(/\{\{(\w+)\}\}/g, (match, variableName) => {
      return variables[variableName] || match;
    });
  }

  private downloadFile(url: string): string {
    // TODO: Implement file download logic
    // For now, return placeholder path
    return path.join(config.storage.resumePath, 'resume.pdf');
  }

  private generateCoverLetterPath(user: UserProfile, job: Job): string {
    // TODO: Implement cover letter generation logic
    // For now, return placeholder path
    return path.join(config.storage.coverLetterPath, 'cover-letter.pdf');
  }

  private async takeScreenshot(context: RecipeExecutionContext, suffix: string): Promise<string | null> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${context.job.id}-${suffix}-${timestamp}.png`;
      const screenshotPath = path.join(config.storage.screenshotPath, filename);
      
      await this.browserManager.takeScreenshot(context.sessionId, screenshotPath);
      return screenshotPath;
    } catch (error) {
      logger.warn('Failed to take screenshot', { suffix, error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  private async logAction(
    context: RecipeExecutionContext,
    action: string,
    status: string,
    details?: string,
    screenshotUrl?: string
  ): Promise<void> {
    const logEntry = {
      timestamp: new Date().toISOString(),
      action,
      status,
      details,
      screenshot_url: screenshotUrl,
    };

    context.logs.push(logEntry);
    
    logger.debug('Recipe action logged', {
      jobId: context.job.id,
      userId: context.user.id,
      action,
      status,
      details,
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private urlMatchesPattern(url: string, pattern: string): boolean {
    try {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(url);
    } catch {
      return url.includes(pattern);
    }
  }

  private detectATSFromUrl(url: string): string | null {
    const atsPatterns: Record<string, string[]> = {
      greenhouse: ['greenhouse.io', 'boards.greenhouse.io'],
      lever: ['lever.co', 'jobs.lever.co'],
      workday: ['workday.com', 'myworkdayjobs.com'],
      bamboohr: ['bamboohr.com'],
      icims: ['icims.com'],
      jobvite: ['jobvite.com'],
      smartrecruiters: ['smartrecruiters.com'],
      taleo: ['taleo.net'],
      successfactors: ['successfactors.com'],
      linkedin: ['linkedin.com/jobs'],
      indeed: ['indeed.com'],
    };

    for (const [platform, patterns] of Object.entries(atsPatterns)) {
      if (patterns.some(pattern => url.includes(pattern))) {
        return platform;
      }
    }

    return null;
  }

  private async loadDefaultRecipes(): Promise<void> {
    // Load default recipes for major ATS platforms
    const defaultRecipes = await this.getDefaultRecipes();
    
    for (const recipe of defaultRecipes) {
      this.recipes.set(recipe.id, recipe);
    }

    logger.info('Loaded default recipes', { count: defaultRecipes.length });
  }

  private async getDefaultRecipes(): Promise<ApplicationRecipe[]> {
    // This would typically load from files or database
    // For now, return hardcoded recipes
    return [
      await this.createGreenhouseRecipe(),
      await this.createLeverRecipe(),
      await this.createWorkdayRecipe(),
      await this.createLinkedInRecipe(),
      await this.createGenericRecipe(),
    ];
  }

  // Recipe creation methods for different ATS platforms would be implemented here
  // Due to length constraints, I'll include just the structure
  private async createGreenhouseRecipe(): Promise<ApplicationRecipe> {
    return {
      id: 'greenhouse-default',
      name: 'Greenhouse Default Application',
      ats_platform: 'greenhouse',
      url_pattern: '*greenhouse.io*',
      description: 'Default recipe for Greenhouse ATS applications',
      version: '1.0.0',
      steps: [
        {
          id: 'wait-for-form',
          name: 'Wait for application form',
          type: 'wait',
          selector: '.application-form, #application_form',
          timeout: 10000,
          required: true,
          retry_on_fail: true,
          wait_after: 1000,
        },
        // Additional steps would be defined here
      ],
      field_mappings: {
        'first_name': '{{firstName}}',
        'last_name': '{{lastName}}',
        'email': '{{email}}',
        'phone': '{{phone}}',
      },
      prerequisites: [],
      success_indicators: [
        'text:thank you for your application',
        'text:application submitted',
        '.success-message',
      ],
      failure_indicators: [
        'text:error',
        'text:required field',
        '.error-message',
      ],
      estimated_duration: 120,
      retry_strategy: {
        max_attempts: 3,
        delay_between_attempts: 5000,
        exponential_backoff: true,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
    };
  }

  private async createLeverRecipe(): Promise<ApplicationRecipe> {
    // Similar structure for Lever
    return {
      id: 'lever-default',
      name: 'Lever Default Application',
      ats_platform: 'lever',
      url_pattern: '*lever.co*',
      description: 'Default recipe for Lever ATS applications',
      version: '1.0.0',
      steps: [],
      field_mappings: {},
      prerequisites: [],
      success_indicators: ['text:application submitted'],
      failure_indicators: ['text:error'],
      estimated_duration: 100,
      retry_strategy: {
        max_attempts: 3,
        delay_between_attempts: 5000,
        exponential_backoff: true,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
    };
  }

  private async createWorkdayRecipe(): Promise<ApplicationRecipe> {
    // Similar structure for Workday
    return {
      id: 'workday-default',
      name: 'Workday Default Application',
      ats_platform: 'workday',
      url_pattern: '*workday.com*',
      description: 'Default recipe for Workday ATS applications',
      version: '1.0.0',
      steps: [],
      field_mappings: {},
      prerequisites: [],
      success_indicators: ['text:application submitted'],
      failure_indicators: ['text:error'],
      estimated_duration: 180,
      retry_strategy: {
        max_attempts: 3,
        delay_between_attempts: 5000,
        exponential_backoff: true,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
    };
  }

  private async createLinkedInRecipe(): Promise<ApplicationRecipe> {
    // Similar structure for LinkedIn
    return {
      id: 'linkedin-default',
      name: 'LinkedIn Default Application',
      ats_platform: 'linkedin',
      url_pattern: '*linkedin.com/jobs*',
      description: 'Default recipe for LinkedIn job applications',
      version: '1.0.0',
      steps: [],
      field_mappings: {},
      prerequisites: [],
      success_indicators: ['text:application sent'],
      failure_indicators: ['text:error'],
      estimated_duration: 60,
      retry_strategy: {
        max_attempts: 3,
        delay_between_attempts: 5000,
        exponential_backoff: true,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
    };
  }

  private async createGenericRecipe(): Promise<ApplicationRecipe> {
    // Generic fallback recipe
    return {
      id: 'generic-default',
      name: 'Generic Application Form',
      ats_platform: 'generic',
      url_pattern: '*',
      description: 'Generic recipe for unknown ATS platforms',
      version: '1.0.0',
      steps: [],
      field_mappings: {},
      prerequisites: [],
      success_indicators: ['text:thank you', 'text:submitted'],
      failure_indicators: ['text:error', 'text:required'],
      estimated_duration: 150,
      retry_strategy: {
        max_attempts: 2,
        delay_between_attempts: 10000,
        exponential_backoff: false,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
    };
  }
}