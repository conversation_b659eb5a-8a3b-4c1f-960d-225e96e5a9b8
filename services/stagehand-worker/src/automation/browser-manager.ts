import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, PuppeteerLaunchOptions } from 'puppeteer';
import puppeteerExtra from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import AdblockerPlugin from 'puppeteer-extra-plugin-adblocker';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs/promises';
import * as path from 'path';

import config from '@/config';
import { logger } from '@/utils/logger';
import { BrowserError } from '@/models/types';
import { MetricsService } from '@/services/metrics-service';

interface BrowserInstance {
  id: string;
  browser: Browser;
  activeSessions: number;
  lastUsed: Date;
  userDataDir: string;
}

interface PageSession {
  id: string;
  page: Page;
  browserId: string;
  userId?: string;
  createdAt: Date;
  lastActivity: Date;
  isActive: boolean;
}

export class BrowserManager {
  private browsers: Map<string, BrowserInstance> = new Map();
  private sessions: Map<string, PageSession> = new Map();
  private metrics: MetricsService;
  private isInitialized = false;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(metrics: MetricsService) {
    this.metrics = metrics;
    
    // Configure Puppeteer Extra with plugins
    if (config.stealthMode) {
      puppeteerExtra.use(StealthPlugin());
    }
    
    // Use adblocker to improve performance and avoid detection
    puppeteerExtra.use(AdblockerPlugin({ blockTrackers: true }));
  }

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing browser manager', {
        concurrentBrowsers: config.concurrentBrowsers,
        headless: config.headlessMode,
        stealthMode: config.stealthMode,
      });

      // Ensure user data directory exists
      await this.ensureUserDataDir();

      // Pre-warm browser pool
      await this.prewarmBrowserPool();

      // Start cleanup interval
      this.startCleanupInterval();

      this.isInitialized = true;
      logger.info('Browser manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize browser manager', { error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Failed to initialize browser manager', { error });
    }
  }

  async createSession(userId?: string): Promise<string> {
    if (!this.isInitialized) {
      throw new BrowserError('Browser manager not initialized');
    }

    try {
      const browserId = await this.getAvailableBrowser();
      const browser = this.browsers.get(browserId);
      
      if (!browser) {
        throw new BrowserError('No available browser found');
      }

      const page = await browser.browser.newPage();
      
      // Configure page settings
      await this.configurePage(page);

      const sessionId = uuidv4();
      const session: PageSession = {
        id: sessionId,
        page,
        browserId,
        userId,
        createdAt: new Date(),
        lastActivity: new Date(),
        isActive: true,
      };

      this.sessions.set(sessionId, session);
      browser.activeSessions++;
      browser.lastUsed = new Date();

      this.metrics.incrementCounter('browser_sessions_created');
      logger.debug('Created browser session', { sessionId, browserId, userId });

      return sessionId;
    } catch (error) {
      this.metrics.incrementCounter('browser_session_creation_failed');
      logger.error('Failed to create browser session', { error: error instanceof Error ? error.message : String(error), userId });
      throw new BrowserError('Failed to create browser session', { error, userId });
    }
  }

  async getSession(sessionId: string): Promise<Page> {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.isActive) {
      throw new BrowserError('Session not found or inactive', { sessionId });
    }

    session.lastActivity = new Date();
    return session.page;
  }

  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      logger.warn('Attempted to close non-existent session', { sessionId });
      return;
    }

    try {
      await session.page.close();
      session.isActive = false;

      const browser = this.browsers.get(session.browserId);
      if (browser) {
        browser.activeSessions = Math.max(0, browser.activeSessions - 1);
      }

      this.sessions.delete(sessionId);
      this.metrics.incrementCounter('browser_sessions_closed');
      
      logger.debug('Closed browser session', { sessionId, browserId: session.browserId });
    } catch (error) {
      logger.error('Error closing browser session', { sessionId, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Failed to close session', { sessionId, error });
    }
  }

  async takeScreenshot(sessionId: string, path?: string): Promise<string> {
    const page = await this.getSession(sessionId);
    
    try {
      const screenshotPath = path || this.generateScreenshotPath();
      
      // Ensure screenshot directory exists
      await fs.mkdir(path || config.storage.screenshotPath, { recursive: true });
      
      await page.screenshot({
        path: screenshotPath,
        fullPage: true,
        type: 'png',
      });

      this.metrics.incrementCounter('screenshots_taken');
      logger.debug('Screenshot taken', { sessionId, path: screenshotPath });
      
      return screenshotPath;
    } catch (error) {
      logger.error('Failed to take screenshot', { sessionId, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Failed to take screenshot', { sessionId, error });
    }
  }

  async navigateToUrl(sessionId: string, url: string, options?: { waitUntil?: 'load' | 'domcontentloaded' | 'networkidle0' | 'networkidle2' }): Promise<void> {
    const page = await this.getSession(sessionId);
    
    try {
      logger.debug('Navigating to URL', { sessionId, url });
      
      await page.goto(url, {
        waitUntil: options?.waitUntil || 'networkidle2',
        timeout: config.browserTimeout,
      });

      this.metrics.incrementCounter('page_navigations');
    } catch (error) {
      logger.error('Navigation failed', { sessionId, url, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Navigation failed', { sessionId, url, error });
    }
  }

  async waitForSelector(sessionId: string, selector: string, timeout?: number): Promise<void> {
    const page = await this.getSession(sessionId);
    
    try {
      await page.waitForSelector(selector, {
        timeout: timeout || config.browserTimeout,
        visible: true,
      });
    } catch (error) {
      logger.error('Wait for selector failed', { sessionId, selector, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Wait for selector failed', { sessionId, selector, error });
    }
  }

  async clickElement(sessionId: string, selector: string): Promise<void> {
    const page = await this.getSession(sessionId);
    
    try {
      await page.waitForSelector(selector, { visible: true, timeout: 5000 });
      await page.click(selector);
      
      // Small delay after click
      await page.waitForTimeout(500);
    } catch (error) {
      logger.error('Click element failed', { sessionId, selector, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Click element failed', { sessionId, selector, error });
    }
  }

  async typeText(sessionId: string, selector: string, text: string, options?: { delay?: number; clear?: boolean }): Promise<void> {
    const page = await this.getSession(sessionId);
    
    try {
      await page.waitForSelector(selector, { visible: true, timeout: 5000 });
      
      if (options?.clear) {
        await page.click(selector, { clickCount: 3 }); // Select all
        await page.keyboard.press('Backspace');
      }
      
      await page.type(selector, text, { delay: options?.delay || 100 });
    } catch (error) {
      logger.error('Type text failed', { sessionId, selector, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Type text failed', { sessionId, selector, error });
    }
  }

  async selectOption(sessionId: string, selector: string, value: string): Promise<void> {
    const page = await this.getSession(sessionId);
    
    try {
      await page.waitForSelector(selector, { visible: true, timeout: 5000 });
      await page.select(selector, value);
    } catch (error) {
      logger.error('Select option failed', { sessionId, selector, value, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Select option failed', { sessionId, selector, value, error });
    }
  }

  async uploadFile(sessionId: string, selector: string, filePath: string): Promise<void> {
    const page = await this.getSession(sessionId);
    
    try {
      const fileInput = await page.waitForSelector(selector, { timeout: 5000 });
      if (!fileInput) {
        throw new Error('File input element not found');
      }
      
      await fileInput.uploadFile(filePath);
    } catch (error) {
      logger.error('File upload failed', { sessionId, selector, filePath, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('File upload failed', { sessionId, selector, filePath, error });
    }
  }

  async extractText(sessionId: string, selector: string): Promise<string> {
    const page = await this.getSession(sessionId);
    
    try {
      const element = await page.waitForSelector(selector, { timeout: 5000 });
      if (!element) {
        throw new Error('Element not found');
      }
      
      const text = await element.evaluate(el => el.textContent || '');
      return text.trim();
    } catch (error) {
      logger.error('Text extraction failed', { sessionId, selector, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Text extraction failed', { sessionId, selector, error });
    }
  }

  async getStats(): Promise<{
    totalBrowsers: number;
    activeBrowsers: number;
    totalSessions: number;
    activeSessions: number;
    averageSessionsPerBrowser: number;
  }> {
    const activeBrowsers = Array.from(this.browsers.values()).filter(b => b.activeSessions > 0).length;
    const activeSessions = Array.from(this.sessions.values()).filter(s => s.isActive).length;
    
    return {
      totalBrowsers: this.browsers.size,
      activeBrowsers,
      totalSessions: this.sessions.size,
      activeSessions,
      averageSessionsPerBrowser: this.browsers.size > 0 ? activeSessions / this.browsers.size : 0,
    };
  }

  async cleanup(): Promise<void> {
    logger.info('Starting browser manager cleanup');
    
    try {
      // Close all sessions
      for (const sessionId of this.sessions.keys()) {
        await this.closeSession(sessionId);
      }

      // Close all browsers
      for (const [browserId, browser] of this.browsers) {
        try {
          await browser.browser.close();
          logger.debug('Closed browser', { browserId });
        } catch (error) {
          logger.error('Error closing browser', { browserId, error: error instanceof Error ? error.message : String(error) });
        }
      }

      this.browsers.clear();
      this.sessions.clear();

      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      this.isInitialized = false;
      logger.info('Browser manager cleanup completed');
    } catch (error) {
      logger.error('Error during browser manager cleanup', { error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Cleanup failed', { error });
    }
  }

  private async prewarmBrowserPool(): Promise<void> {
    const promises = [];
    
    for (let i = 0; i < config.concurrentBrowsers; i++) {
      promises.push(this.createBrowser());
    }

    await Promise.all(promises);
    logger.info('Browser pool prewarmed', { count: config.concurrentBrowsers });
  }

  private async createBrowser(): Promise<string> {
    const browserId = uuidv4();
    const userDataDir = path.join(config.userDataDir, browserId);
    
    try {
      await fs.mkdir(userDataDir, { recursive: true });

      const launchOptions: PuppeteerLaunchOptions = {
        headless: config.headlessMode,
        userDataDir,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=TranslateUI',
          '--disable-ipc-flooding-protection',
          '--window-size=1920,1080',
        ],
        defaultViewport: {
          width: 1920,
          height: 1080,
        },
        timeout: config.browserTimeout,
      };

      // Add proxy configuration if enabled
      if (config.proxy.enabled && config.proxy.host && config.proxy.port) {
        launchOptions.args?.push(`--proxy-server=${config.proxy.host}:${config.proxy.port}`);
      }

      const browser = await (config.stealthMode ? puppeteerExtra : puppeteer).launch(launchOptions);

      const instance: BrowserInstance = {
        id: browserId,
        browser,
        activeSessions: 0,
        lastUsed: new Date(),
        userDataDir,
      };

      this.browsers.set(browserId, instance);
      this.metrics.incrementCounter('browsers_created');
      
      logger.debug('Created browser instance', { browserId });
      return browserId;
    } catch (error) {
      logger.error('Failed to create browser', { browserId, error: error instanceof Error ? error.message : String(error) });
      throw new BrowserError('Failed to create browser', { browserId, error });
    }
  }

  private async getAvailableBrowser(): Promise<string> {
    // Find browser with least active sessions
    let selectedBrowser: BrowserInstance | null = null;
    
    for (const browser of this.browsers.values()) {
      if (!selectedBrowser || browser.activeSessions < selectedBrowser.activeSessions) {
        selectedBrowser = browser;
      }
    }

    if (!selectedBrowser) {
      // Create new browser if none available
      return await this.createBrowser();
    }

    return selectedBrowser.id;
  }

  private async configurePage(page: Page): Promise<void> {
    // Set user agent
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    );

    // Set viewport
    await page.setViewport({
      width: 1920,
      height: 1080,
      deviceScaleFactor: 1,
    });

    // Block unnecessary resources to improve performance
    await page.setRequestInterception(true);
    page.on('request', (request) => {
      const resourceType = request.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        request.abort();
      } else {
        request.continue();
      }
    });

    // Set default timeouts
    page.setDefaultTimeout(config.browserTimeout);
    page.setDefaultNavigationTimeout(config.browserTimeout);

    // Handle console logs in debug mode
    if (config.enableDetailedLogging) {
      page.on('console', (msg) => {
        logger.debug('Browser console', { 
          type: msg.type(), 
          text: msg.text().substring(0, 500) // Limit log length
        });
      });
    }
  }

  private generateScreenshotPath(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `screenshot-${timestamp}-${uuidv4().substring(0, 8)}.png`;
    return path.join(config.storage.screenshotPath, filename);
  }

  private async ensureUserDataDir(): Promise<void> {
    try {
      await fs.mkdir(config.userDataDir, { recursive: true });
    } catch (error) {
      logger.error('Failed to create user data directory', { 
        path: config.userDataDir, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new BrowserError('Failed to create user data directory', { path: config.userDataDir, error });
    }
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(async () => {
      await this.performPeriodicCleanup();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  private async performPeriodicCleanup(): Promise<void> {
    const now = new Date();
    const sessionTimeout = 30 * 60 * 1000; // 30 minutes
    const browserTimeout = 60 * 60 * 1000; // 1 hour

    // Clean up inactive sessions
    for (const [sessionId, session] of this.sessions) {
      if (now.getTime() - session.lastActivity.getTime() > sessionTimeout) {
        logger.debug('Cleaning up inactive session', { sessionId, lastActivity: session.lastActivity });
        await this.closeSession(sessionId);
      }
    }

    // Clean up unused browsers
    for (const [browserId, browser] of this.browsers) {
      if (browser.activeSessions === 0 && now.getTime() - browser.lastUsed.getTime() > browserTimeout) {
        logger.debug('Cleaning up unused browser', { browserId, lastUsed: browser.lastUsed });
        try {
          await browser.browser.close();
          this.browsers.delete(browserId);
          this.metrics.incrementCounter('browsers_cleaned_up');
        } catch (error) {
          logger.error('Error cleaning up browser', { browserId, error: error instanceof Error ? error.message : String(error) });
        }
      }
    }
  }
}