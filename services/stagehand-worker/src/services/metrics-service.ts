import { register, Counter, Histogram, Gauge, collectDefaultMetrics } from 'prom-client';
import config from '@/config';
import { logger } from '@/utils/logger';
import { ApplicationMetrics, ApplicationStatus } from '@/models/types';

export class MetricsService {
  private counters: Map<string, Counter> = new Map();
  private histograms: Map<string, Histogram> = new Map();
  private gauges: Map<string, Gauge> = new Map();
  private isInitialized = false;

  // Application-specific metrics
  private applicationMetrics: ApplicationMetrics = {
    total_applications: 0,
    successful_applications: 0,
    failed_applications: 0,
    pending_applications: 0,
    success_rate: 0,
    average_application_time: 0,
    applications_per_hour: 0,
    ats_platform_breakdown: {},
    error_breakdown: {},
    timestamp: new Date().toISOString(),
  };

  private applicationTimes: number[] = [];
  private applicationCountByHour: Map<string, number> = new Map();

  constructor() {
    // Initialize default metrics collection
    collectDefaultMetrics({
      register,
      prefix: 'stagehand_worker_',
      gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5],
    });
  }

  async initialize(): Promise<void> {
    try {
      this.setupCounters();
      this.setupHistograms();
      this.setupGauges();
      
      this.isInitialized = true;
      logger.info('Metrics service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize metrics service', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  incrementCounter(name: string, labels?: Record<string, string>, value: number = 1): void {
    const counter = this.counters.get(name);
    if (counter) {
      if (labels) {
        counter.inc(labels, value);
      } else {
        counter.inc(value);
      }
    } else {
      logger.warn('Counter not found', { name });
    }
  }

  observeHistogram(name: string, value: number, labels?: Record<string, string>): void {
    const histogram = this.histograms.get(name);
    if (histogram) {
      if (labels) {
        histogram.observe(labels, value);
      } else {
        histogram.observe(value);
      }
    } else {
      logger.warn('Histogram not found', { name });
    }
  }

  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    const gauge = this.gauges.get(name);
    if (gauge) {
      if (labels) {
        gauge.set(labels, value);
      } else {
        gauge.set(value);
      }
    } else {
      logger.warn('Gauge not found', { name });
    }
  }

  recordApplicationStart(jobId: string, atsPlatform: string): void {
    this.incrementCounter('applications_started_total', { ats_platform: atsPlatform });
    this.incrementCounter('applications_by_platform_total', { platform: atsPlatform });
    
    // Track pending applications
    this.applicationMetrics.pending_applications++;
    this.updateApplicationsGauge();
  }

  recordApplicationComplete(
    jobId: string, 
    atsPlatform: string, 
    status: ApplicationStatus, 
    durationMs: number,
    errorCode?: string
  ): void {
    // Update counters
    this.incrementCounter('applications_completed_total', { 
      ats_platform: atsPlatform, 
      status: status 
    });

    // Record timing
    this.observeHistogram('application_duration_seconds', durationMs / 1000, { 
      ats_platform: atsPlatform 
    });

    // Update application metrics
    this.applicationMetrics.total_applications++;
    this.applicationMetrics.pending_applications = Math.max(0, this.applicationMetrics.pending_applications - 1);

    if (status === 'submitted' || status === 'acknowledged') {
      this.applicationMetrics.successful_applications++;
    } else if (status === 'failed') {
      this.applicationMetrics.failed_applications++;
      if (errorCode) {
        this.applicationMetrics.error_breakdown[errorCode] = 
          (this.applicationMetrics.error_breakdown[errorCode] || 0) + 1;
      }
    }

    // Update ATS platform breakdown
    this.applicationMetrics.ats_platform_breakdown[atsPlatform] = 
      (this.applicationMetrics.ats_platform_breakdown[atsPlatform] || 0) + 1;

    // Track application times for average calculation
    this.applicationTimes.push(durationMs);
    if (this.applicationTimes.length > 1000) {
      this.applicationTimes = this.applicationTimes.slice(-1000); // Keep last 1000 entries
    }

    // Update hourly application count
    this.updateHourlyApplicationCount();

    // Recalculate derived metrics
    this.updateDerivedMetrics();
    this.updateApplicationsGauge();
  }

  recordBrowserAction(action: string, success: boolean, durationMs: number): void {
    this.incrementCounter('browser_actions_total', { 
      action, 
      status: success ? 'success' : 'failure' 
    });

    this.observeHistogram('browser_action_duration_seconds', durationMs / 1000, { action });
  }

  recordQueueMetrics(queueName: string, waiting: number, active: number, completed: number, failed: number): void {
    this.setGauge('queue_jobs_waiting', waiting, { queue: queueName });
    this.setGauge('queue_jobs_active', active, { queue: queueName });
    this.setGauge('queue_jobs_completed', completed, { queue: queueName });
    this.setGauge('queue_jobs_failed', failed, { queue: queueName });
  }

  recordRateLimit(atsPlatform: string): void {
    this.incrementCounter('rate_limits_hit_total', { ats_platform: atsPlatform });
  }

  recordError(errorType: string, atsPlatform?: string): void {
    const labels: Record<string, string> = { error_type: errorType };
    if (atsPlatform) {
      labels.ats_platform = atsPlatform;
    }
    this.incrementCounter('errors_total', labels);
  }

  getApplicationMetrics(): ApplicationMetrics {
    return { ...this.applicationMetrics };
  }

  async getPrometheusMetrics(): Promise<string> {
    return register.metrics();
  }

  getStats(): Record<string, any> {
    return {
      application_metrics: this.getApplicationMetrics(),
      counters: Array.from(this.counters.keys()),
      histograms: Array.from(this.histograms.keys()),
      gauges: Array.from(this.gauges.keys()),
      is_initialized: this.isInitialized,
    };
  }

  private setupCounters(): void {
    const counterConfigs = [
      {
        name: 'applications_started_total',
        help: 'Total number of applications started',
        labelNames: ['ats_platform'],
      },
      {
        name: 'applications_completed_total',
        help: 'Total number of applications completed',
        labelNames: ['ats_platform', 'status'],
      },
      {
        name: 'applications_by_platform_total',
        help: 'Total applications by ATS platform',
        labelNames: ['platform'],
      },
      {
        name: 'browser_sessions_created',
        help: 'Total browser sessions created',
        labelNames: [],
      },
      {
        name: 'browser_sessions_closed',
        help: 'Total browser sessions closed',
        labelNames: [],
      },
      {
        name: 'browser_session_creation_failed',
        help: 'Total failed browser session creations',
        labelNames: [],
      },
      {
        name: 'browsers_created',
        help: 'Total browsers created',
        labelNames: [],
      },
      {
        name: 'browsers_cleaned_up',
        help: 'Total browsers cleaned up',
        labelNames: [],
      },
      {
        name: 'screenshots_taken',
        help: 'Total screenshots taken',
        labelNames: [],
      },
      {
        name: 'page_navigations',
        help: 'Total page navigations',
        labelNames: [],
      },
      {
        name: 'browser_actions_total',
        help: 'Total browser actions performed',
        labelNames: ['action', 'status'],
      },
      {
        name: 'rate_limits_hit_total',
        help: 'Total rate limits encountered',
        labelNames: ['ats_platform'],
      },
      {
        name: 'errors_total',
        help: 'Total errors encountered',
        labelNames: ['error_type', 'ats_platform'],
      },
      {
        name: 'recipe_executions_total',
        help: 'Total recipe executions',
        labelNames: ['recipe_id', 'status'],
      },
      {
        name: 'webhooks_sent_total',
        help: 'Total webhooks sent',
        labelNames: ['event_type', 'status'],
      },
    ];

    for (const config of counterConfigs) {
      const counter = new Counter({
        name: `stagehand_worker_${config.name}`,
        help: config.help,
        labelNames: config.labelNames,
        registers: [register],
      });
      this.counters.set(config.name, counter);
    }
  }

  private setupHistograms(): void {
    const histogramConfigs = [
      {
        name: 'application_duration_seconds',
        help: 'Duration of job applications in seconds',
        labelNames: ['ats_platform'],
        buckets: [1, 5, 10, 30, 60, 120, 300, 600],
      },
      {
        name: 'browser_action_duration_seconds',
        help: 'Duration of browser actions in seconds',
        labelNames: ['action'],
        buckets: [0.1, 0.5, 1, 2, 5, 10, 30],
      },
      {
        name: 'recipe_execution_duration_seconds',
        help: 'Duration of recipe executions in seconds',
        labelNames: ['recipe_id'],
        buckets: [5, 10, 30, 60, 120, 300, 600],
      },
      {
        name: 'queue_job_processing_duration_seconds',
        help: 'Duration of queue job processing in seconds',
        labelNames: ['job_type'],
        buckets: [1, 5, 10, 30, 60, 120, 300],
      },
    ];

    for (const config of histogramConfigs) {
      const histogram = new Histogram({
        name: `stagehand_worker_${config.name}`,
        help: config.help,
        labelNames: config.labelNames,
        buckets: config.buckets,
        registers: [register],
      });
      this.histograms.set(config.name, histogram);
    }
  }

  private setupGauges(): void {
    const gaugeConfigs = [
      {
        name: 'applications_pending',
        help: 'Number of pending applications',
        labelNames: [],
      },
      {
        name: 'applications_success_rate',
        help: 'Success rate of applications (0-1)',
        labelNames: [],
      },
      {
        name: 'applications_per_hour',
        help: 'Number of applications per hour',
        labelNames: [],
      },
      {
        name: 'browser_sessions_active',
        help: 'Number of active browser sessions',
        labelNames: [],
      },
      {
        name: 'browsers_total',
        help: 'Total number of browser instances',
        labelNames: [],
      },
      {
        name: 'queue_jobs_waiting',
        help: 'Number of jobs waiting in queue',
        labelNames: ['queue'],
      },
      {
        name: 'queue_jobs_active',
        help: 'Number of active jobs in queue',
        labelNames: ['queue'],
      },
      {
        name: 'queue_jobs_completed',
        help: 'Number of completed jobs in queue',
        labelNames: ['queue'],
      },
      {
        name: 'queue_jobs_failed',
        help: 'Number of failed jobs in queue',
        labelNames: ['queue'],
      },
      {
        name: 'average_application_time_seconds',
        help: 'Average application time in seconds',
        labelNames: [],
      },
    ];

    for (const config of gaugeConfigs) {
      const gauge = new Gauge({
        name: `stagehand_worker_${config.name}`,
        help: config.help,
        labelNames: config.labelNames,
        registers: [register],
      });
      this.gauges.set(config.name, gauge);
    }
  }

  private updateDerivedMetrics(): void {
    // Update success rate
    if (this.applicationMetrics.total_applications > 0) {
      this.applicationMetrics.success_rate = 
        this.applicationMetrics.successful_applications / this.applicationMetrics.total_applications;
    }

    // Update average application time
    if (this.applicationTimes.length > 0) {
      const sum = this.applicationTimes.reduce((a, b) => a + b, 0);
      this.applicationMetrics.average_application_time = sum / this.applicationTimes.length / 1000; // Convert to seconds
    }

    // Update timestamp
    this.applicationMetrics.timestamp = new Date().toISOString();

    // Update gauges
    this.setGauge('applications_success_rate', this.applicationMetrics.success_rate);
    this.setGauge('applications_per_hour', this.applicationMetrics.applications_per_hour);
    this.setGauge('average_application_time_seconds', this.applicationMetrics.average_application_time);
  }

  private updateApplicationsGauge(): void {
    this.setGauge('applications_pending', this.applicationMetrics.pending_applications);
  }

  private updateHourlyApplicationCount(): void {
    const currentHour = new Date().toISOString().substring(0, 13); // YYYY-MM-DDTHH
    const currentCount = this.applicationCountByHour.get(currentHour) || 0;
    this.applicationCountByHour.set(currentHour, currentCount + 1);

    // Clean up old hours (keep last 24 hours)
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().substring(0, 13);
    for (const [hour] of this.applicationCountByHour) {
      if (hour < cutoffTime) {
        this.applicationCountByHour.delete(hour);
      }
    }

    // Calculate applications per hour (average of last hour)
    this.applicationMetrics.applications_per_hour = this.applicationCountByHour.get(currentHour) || 0;
  }
}