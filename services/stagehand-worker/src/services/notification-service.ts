import axios, { AxiosError } from 'axios';
import config from '@/config';
import { logger } from '@/utils/logger';
import { MetricsService } from '@/services/metrics-service';
import { 
  Application, 
  WebhookEvent, 
  ApplicationError 
} from '@/models/types';

interface SlackMessage {
  text: string;
  attachments?: Array<{
    color: string;
    fields: Array<{
      title: string;
      value: string;
      short: boolean;
    }>;
  }>;
}

export class NotificationService {
  private metrics: MetricsService;

  constructor(metrics: MetricsService) {
    this.metrics = metrics;
  }

  async initialize(): Promise<void> {
    logger.info('Notification service initialized');
  }

  async sendApplicationNotification(
    application: Application,
    type: 'success' | 'failure' | 'status_update'
  ): Promise<void> {
    try {
      const webhookEvent = this.createWebhookEvent(application, type);

      // Send webhook notification
      if (config.notifications.webhookUrl) {
        await this.sendWebhook(webhookEvent);
      }

      // Send Slack notification
      if (config.notifications.slackWebhookUrl) {
        await this.sendSlackNotification(application, type);
      }

      logger.debug('Application notification sent successfully', {
        applicationId: application.id,
        type,
        status: application.status,
      });

    } catch (error) {
      logger.error('Failed to send application notification', {
        applicationId: application.id,
        type,
        error: error instanceof Error ? error.message : String(error),
      });
      
      this.metrics.incrementCounter('webhooks_sent_total', { 
        event_type: type, 
        status: 'failed' 
      });

      // Don't throw error to avoid breaking the main application flow
    }
  }

  async sendBatchStatusUpdate(applications: Application[]): Promise<void> {
    const promises = applications.map(app => 
      this.sendApplicationNotification(app, 'status_update')
    );

    try {
      await Promise.allSettled(promises);
      logger.info('Batch status updates sent', { count: applications.length });
    } catch (error) {
      logger.error('Failed to send batch status updates', {
        count: applications.length,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  async sendSystemAlert(
    level: 'info' | 'warning' | 'error' | 'critical',
    message: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const alert = {
        timestamp: new Date().toISOString(),
        service: config.serviceName,
        level,
        message,
        metadata: metadata || {},
        environment: config.nodeEnv,
      };

      // Send to webhook if configured
      if (config.notifications.webhookUrl) {
        await this.sendSystemWebhook(alert);
      }

      // Send to Slack if critical or error
      if (config.notifications.slackWebhookUrl && ['error', 'critical'].includes(level)) {
        await this.sendSlackAlert(alert);
      }

      logger.debug('System alert sent', { level, message });

    } catch (error) {
      logger.error('Failed to send system alert', {
        level,
        message,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  async sendPerformanceReport(stats: {
    applicationsPerHour: number;
    successRate: number;
    averageProcessingTime: number;
    queueStats: any;
  }): Promise<void> {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        service: config.serviceName,
        type: 'performance_report',
        period: 'hourly',
        stats,
      };

      if (config.notifications.webhookUrl) {
        await this.sendReportWebhook(report);
      }

      // Send Slack report if performance is concerning
      if (config.notifications.slackWebhookUrl) {
        const shouldAlert = 
          stats.successRate < 0.9 || 
          stats.applicationsPerHour < config.maxApplicationsPerHour * 0.5 ||
          stats.averageProcessingTime > 180; // 3 minutes

        if (shouldAlert) {
          await this.sendSlackPerformanceAlert(report);
        }
      }

      logger.debug('Performance report sent', { stats });

    } catch (error) {
      logger.error('Failed to send performance report', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private createWebhookEvent(application: Application, type: string): WebhookEvent {
    let eventType: WebhookEvent['event_type'];

    switch (type) {
      case 'success':
        eventType = application.status === 'submitted' ? 'application_submitted' : 'application_acknowledged';
        break;
      case 'failure':
        eventType = 'application_failed';
        break;
      default:
        eventType = 'status_updated';
    }

    return {
      event_type: eventType,
      application_id: application.id,
      user_id: application.user_id,
      job_id: application.job_id,
      status: application.status,
      timestamp: new Date().toISOString(),
      data: {
        confirmation_number: application.confirmation_number,
        error_message: application.error_message,
        retry_count: application.retry_count,
        applied_at: application.applied_at,
        metadata: application.metadata,
      },
    };
  }

  private async sendWebhook(event: WebhookEvent): Promise<void> {
    if (!config.notifications.webhookUrl) {
      return;
    }

    try {
      const response = await axios.post(config.notifications.webhookUrl, event, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `${config.serviceName}/${config.serviceName}`,
        },
      });

      if (response.status >= 200 && response.status < 300) {
        this.metrics.incrementCounter('webhooks_sent_total', { 
          event_type: event.event_type, 
          status: 'success' 
        });
      } else {
        throw new Error(`Webhook returned status ${response.status}`);
      }

    } catch (error) {
      this.metrics.incrementCounter('webhooks_sent_total', { 
        event_type: event.event_type, 
        status: 'failed' 
      });

      if (error instanceof AxiosError) {
        logger.error('Webhook request failed', {
          eventType: event.event_type,
          status: error.response?.status,
          statusText: error.response?.statusText,
          error: error.message,
        });
      } else {
        logger.error('Webhook failed', {
          eventType: event.event_type,
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  private async sendSlackNotification(
    application: Application,
    type: 'success' | 'failure' | 'status_update'
  ): Promise<void> {
    if (!config.notifications.slackWebhookUrl) {
      return;
    }

    const color = type === 'success' ? 'good' : type === 'failure' ? 'danger' : 'warning';
    const emoji = type === 'success' ? '✅' : type === 'failure' ? '❌' : '🔄';
    
    let title = '';
    let description = '';

    switch (type) {
      case 'success':
        title = `${emoji} Application Submitted Successfully`;
        description = `Application for job has been submitted successfully.`;
        break;
      case 'failure':
        title = `${emoji} Application Failed`;
        description = `Application for job failed to submit.`;
        break;
      case 'status_update':
        title = `${emoji} Application Status Updated`;
        description = `Application status changed to ${application.status}.`;
        break;
    }

    const message: SlackMessage = {
      text: title,
      attachments: [
        {
          color,
          fields: [
            {
              title: 'Application ID',
              value: application.id,
              short: true,
            },
            {
              title: 'User ID',
              value: application.user_id,
              short: true,
            },
            {
              title: 'Job ID',
              value: application.job_id,
              short: true,
            },
            {
              title: 'Status',
              value: application.status,
              short: true,
            },
            {
              title: 'Applied At',
              value: application.applied_at || 'N/A',
              short: true,
            },
            {
              title: 'Retry Count',
              value: application.retry_count.toString(),
              short: true,
            },
          ],
        },
      ],
    };

    // Add confirmation number if available
    if (application.confirmation_number) {
      message.attachments![0].fields.push({
        title: 'Confirmation Number',
        value: application.confirmation_number,
        short: true,
      });
    }

    // Add error message if failed
    if (type === 'failure' && application.error_message) {
      message.attachments![0].fields.push({
        title: 'Error Message',
        value: application.error_message.substring(0, 200), // Limit length
        short: false,
      });
    }

    await this.sendSlackMessage(message);
  }

  private async sendSlackAlert(alert: {
    timestamp: string;
    service: string;
    level: string;
    message: string;
    metadata: Record<string, any>;
    environment: string;
  }): Promise<void> {
    const color = alert.level === 'critical' ? 'danger' : alert.level === 'error' ? 'warning' : 'good';
    const emoji = alert.level === 'critical' ? '🚨' : alert.level === 'error' ? '⚠️' : 'ℹ️';

    const message: SlackMessage = {
      text: `${emoji} ${alert.level.toUpperCase()}: ${alert.message}`,
      attachments: [
        {
          color,
          fields: [
            {
              title: 'Service',
              value: alert.service,
              short: true,
            },
            {
              title: 'Environment',
              value: alert.environment,
              short: true,
            },
            {
              title: 'Level',
              value: alert.level,
              short: true,
            },
            {
              title: 'Timestamp',
              value: alert.timestamp,
              short: true,
            },
          ],
        },
      ],
    };

    // Add metadata fields
    if (Object.keys(alert.metadata).length > 0) {
      for (const [key, value] of Object.entries(alert.metadata)) {
        if (message.attachments![0].fields.length < 10) { // Slack limit
          message.attachments![0].fields.push({
            title: key,
            value: String(value).substring(0, 100), // Limit length
            short: true,
          });
        }
      }
    }

    await this.sendSlackMessage(message);
  }

  private async sendSlackPerformanceAlert(report: {
    timestamp: string;
    service: string;
    type: string;
    period: string;
    stats: any;
  }): Promise<void> {
    const message: SlackMessage = {
      text: '📊 Performance Alert - Stagehand Worker',
      attachments: [
        {
          color: 'warning',
          fields: [
            {
              title: 'Applications/Hour',
              value: report.stats.applicationsPerHour.toString(),
              short: true,
            },
            {
              title: 'Success Rate',
              value: `${(report.stats.successRate * 100).toFixed(1)}%`,
              short: true,
            },
            {
              title: 'Avg Processing Time',
              value: `${report.stats.averageProcessingTime.toFixed(1)}s`,
              short: true,
            },
            {
              title: 'Queue Waiting',
              value: report.stats.queueStats.waiting?.toString() || '0',
              short: true,
            },
            {
              title: 'Queue Active',
              value: report.stats.queueStats.active?.toString() || '0',
              short: true,
            },
            {
              title: 'Queue Failed',
              value: report.stats.queueStats.failed?.toString() || '0',
              short: true,
            },
          ],
        },
      ],
    };

    await this.sendSlackMessage(message);
  }

  private async sendSlackMessage(message: SlackMessage): Promise<void> {
    if (!config.notifications.slackWebhookUrl) {
      return;
    }

    try {
      const response = await axios.post(config.notifications.slackWebhookUrl, message, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status >= 200 && response.status < 300) {
        this.metrics.incrementCounter('slack_messages_sent_total', { status: 'success' });
      } else {
        throw new Error(`Slack webhook returned status ${response.status}`);
      }

    } catch (error) {
      this.metrics.incrementCounter('slack_messages_sent_total', { status: 'failed' });
      
      logger.error('Slack notification failed', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async sendSystemWebhook(alert: any): Promise<void> {
    if (!config.notifications.webhookUrl) {
      return;
    }

    try {
      await axios.post(config.notifications.webhookUrl, {
        type: 'system_alert',
        ...alert,
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `${config.serviceName}/system-alerts`,
        },
      });

    } catch (error) {
      logger.error('System webhook failed', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private async sendReportWebhook(report: any): Promise<void> {
    if (!config.notifications.webhookUrl) {
      return;
    }

    try {
      await axios.post(config.notifications.webhookUrl, {
        type: 'performance_report',
        ...report,
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `${config.serviceName}/performance-reports`,
        },
      });

    } catch (error) {
      logger.error('Report webhook failed', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
}