import { Pool, PoolClient } from 'pg';
import config from '@/config';
import { logger } from '@/utils/logger';
import { 
  Job, 
  UserProfile, 
  Application, 
  ApplicationRecipe,
  ApplicationError 
} from '@/models/types';

export class DatabaseService {
  private pool: Pool;
  private isInitialized = false;

  constructor() {
    this.pool = new Pool({
      connectionString: config.databaseUrl,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
  }

  async initialize(): Promise<void> {
    try {
      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();

      this.isInitialized = true;
      logger.info('Database service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database service', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Database initialization failed', 'DATABASE_ERROR', { error });
    }
  }

  async getJobById(jobId: string): Promise<Job | null> {
    try {
      const query = `
        SELECT 
          id, external_id, source, url, title, company, location, country,
          is_remote, employment_type, experience_level, salary_min, salary_max,
          currency, description, requirements, skills, ats_platform, apply_url,
          posted_at, expires_at, is_active, ranking_score, similarity_score,
          application_method, created_at, updated_at
        FROM jobs 
        WHERE id = $1 AND is_active = true
      `;

      const result = await this.pool.query(query, [jobId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapJobRow(result.rows[0]);
    } catch (error) {
      logger.error('Failed to get job by ID', { 
        jobId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get job', 'DATABASE_ERROR', { jobId, error });
    }
  }

  async getUserById(userId: string): Promise<UserProfile | null> {
    try {
      const query = `
        SELECT 
          id, name, email, phone, location, country, resume_url, 
          cover_letter_template, skills, experience_years, current_title,
          linkedin_url, github_url, portfolio_url, preferences, credentials,
          created_at, updated_at
        FROM users 
        WHERE id = $1
      `;

      const result = await this.pool.query(query, [userId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapUserRow(result.rows[0]);
    } catch (error) {
      logger.error('Failed to get user by ID', { 
        userId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get user', 'DATABASE_ERROR', { userId, error });
    }
  }

  async getApplicationByUserAndJob(userId: string, jobId: string): Promise<Application | null> {
    try {
      const query = `
        SELECT 
          id, user_id, job_id, status, application_url, confirmation_number,
          cover_letter_text, resume_version, applied_at, last_updated,
          retry_count, error_message, screenshot_urls, metadata,
          ats_application_id, automation_log, created_at, updated_at
        FROM applications 
        WHERE user_id = $1 AND job_id = $2
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const result = await this.pool.query(query, [userId, jobId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapApplicationRow(result.rows[0]);
    } catch (error) {
      logger.error('Failed to get application by user and job', { 
        userId, 
        jobId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get application', 'DATABASE_ERROR', { userId, jobId, error });
    }
  }

  async createApplication(application: Application): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      const query = `
        INSERT INTO applications (
          id, user_id, job_id, status, application_url, confirmation_number,
          cover_letter_text, resume_version, applied_at, last_updated,
          retry_count, error_message, screenshot_urls, metadata,
          ats_application_id, automation_log, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
        )
      `;

      const values = [
        application.id,
        application.user_id,
        application.job_id,
        application.status,
        application.application_url,
        application.confirmation_number,
        application.cover_letter_text,
        application.resume_version,
        application.applied_at,
        application.last_updated,
        application.retry_count,
        application.error_message,
        JSON.stringify(application.screenshot_urls),
        JSON.stringify(application.metadata),
        application.ats_application_id,
        JSON.stringify(application.automation_log),
        application.created_at,
        application.updated_at,
      ];

      await client.query(query, values);
      await client.query('COMMIT');

      logger.debug('Application created successfully', { applicationId: application.id });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to create application', { 
        applicationId: application.id,
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to create application', 'DATABASE_ERROR', { 
        applicationId: application.id, 
        error 
      });
    } finally {
      client.release();
    }
  }

  async updateApplication(application: Application): Promise<void> {
    try {
      const query = `
        UPDATE applications SET 
          status = $2,
          application_url = $3,
          confirmation_number = $4,
          cover_letter_text = $5,
          resume_version = $6,
          applied_at = $7,
          last_updated = $8,
          retry_count = $9,
          error_message = $10,
          screenshot_urls = $11,
          metadata = $12,
          ats_application_id = $13,
          automation_log = $14,
          updated_at = $15
        WHERE id = $1
      `;

      const values = [
        application.id,
        application.status,
        application.application_url,
        application.confirmation_number,
        application.cover_letter_text,
        application.resume_version,
        application.applied_at,
        application.last_updated,
        application.retry_count,
        application.error_message,
        JSON.stringify(application.screenshot_urls),
        JSON.stringify(application.metadata),
        application.ats_application_id,
        JSON.stringify(application.automation_log),
        application.updated_at,
      ];

      const result = await this.pool.query(query, values);

      if (result.rowCount === 0) {
        throw new ApplicationError('Application not found for update', 'NOT_FOUND', { 
          applicationId: application.id 
        });
      }

      logger.debug('Application updated successfully', { 
        applicationId: application.id,
        status: application.status 
      });
    } catch (error) {
      logger.error('Failed to update application', { 
        applicationId: application.id,
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to update application', 'DATABASE_ERROR', { 
        applicationId: application.id, 
        error 
      });
    }
  }

  async getApplicationsByUserId(userId: string, limit = 50, offset = 0): Promise<Application[]> {
    try {
      const query = `
        SELECT 
          id, user_id, job_id, status, application_url, confirmation_number,
          cover_letter_text, resume_version, applied_at, last_updated,
          retry_count, error_message, screenshot_urls, metadata,
          ats_application_id, automation_log, created_at, updated_at
        FROM applications 
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const result = await this.pool.query(query, [userId, limit, offset]);
      return result.rows.map(row => this.mapApplicationRow(row));
    } catch (error) {
      logger.error('Failed to get applications by user ID', { 
        userId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get user applications', 'DATABASE_ERROR', { userId, error });
    }
  }

  async getApplicationsByStatus(status: string, limit = 100, offset = 0): Promise<Application[]> {
    try {
      const query = `
        SELECT 
          id, user_id, job_id, status, application_url, confirmation_number,
          cover_letter_text, resume_version, applied_at, last_updated,
          retry_count, error_message, screenshot_urls, metadata,
          ats_application_id, automation_log, created_at, updated_at
        FROM applications 
        WHERE status = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const result = await this.pool.query(query, [status, limit, offset]);
      return result.rows.map(row => this.mapApplicationRow(row));
    } catch (error) {
      logger.error('Failed to get applications by status', { 
        status, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get applications by status', 'DATABASE_ERROR', { status, error });
    }
  }

  async getApplicationStats(userId?: string): Promise<{
    total: number;
    successful: number;
    failed: number;
    pending: number;
    in_progress: number;
  }> {
    try {
      let query = `
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN status IN ('submitted', 'acknowledged') THEN 1 END) as successful,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
          COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress
        FROM applications
      `;

      const values: any[] = [];
      
      if (userId) {
        query += ' WHERE user_id = $1';
        values.push(userId);
      }

      const result = await this.pool.query(query, values);
      const row = result.rows[0];

      return {
        total: parseInt(row.total),
        successful: parseInt(row.successful),
        failed: parseInt(row.failed),
        pending: parseInt(row.pending),
        in_progress: parseInt(row.in_progress),
      };
    } catch (error) {
      logger.error('Failed to get application stats', { 
        userId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get application stats', 'DATABASE_ERROR', { userId, error });
    }
  }

  async saveRecipe(recipe: ApplicationRecipe): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');

      const query = `
        INSERT INTO application_recipes (
          id, name, ats_platform, company, url_pattern, description,
          version, steps, field_mappings, prerequisites, success_indicators,
          failure_indicators, estimated_duration, retry_strategy,
          created_at, updated_at, is_active
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
        )
        ON CONFLICT (id) DO UPDATE SET
          name = $2,
          ats_platform = $3,
          company = $4,
          url_pattern = $5,
          description = $6,
          version = $7,
          steps = $8,
          field_mappings = $9,
          prerequisites = $10,
          success_indicators = $11,
          failure_indicators = $12,
          estimated_duration = $13,
          retry_strategy = $14,
          updated_at = $16,
          is_active = $17
      `;

      const values = [
        recipe.id,
        recipe.name,
        recipe.ats_platform,
        recipe.company,
        recipe.url_pattern,
        recipe.description,
        recipe.version,
        JSON.stringify(recipe.steps),
        JSON.stringify(recipe.field_mappings),
        JSON.stringify(recipe.prerequisites),
        JSON.stringify(recipe.success_indicators),
        JSON.stringify(recipe.failure_indicators),
        recipe.estimated_duration,
        JSON.stringify(recipe.retry_strategy),
        recipe.created_at,
        recipe.updated_at,
        recipe.is_active,
      ];

      await client.query(query, values);
      await client.query('COMMIT');

      logger.debug('Recipe saved successfully', { recipeId: recipe.id });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to save recipe', { 
        recipeId: recipe.id,
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to save recipe', 'DATABASE_ERROR', { 
        recipeId: recipe.id, 
        error 
      });
    } finally {
      client.release();
    }
  }

  async getRecipeById(recipeId: string): Promise<ApplicationRecipe | null> {
    try {
      const query = `
        SELECT 
          id, name, ats_platform, company, url_pattern, description,
          version, steps, field_mappings, prerequisites, success_indicators,
          failure_indicators, estimated_duration, retry_strategy,
          created_at, updated_at, is_active
        FROM application_recipes
        WHERE id = $1
      `;

      const result = await this.pool.query(query, [recipeId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapRecipeRow(result.rows[0]);
    } catch (error) {
      logger.error('Failed to get recipe by ID', { 
        recipeId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get recipe', 'DATABASE_ERROR', { recipeId, error });
    }
  }

  async getRecipesByPlatform(atsPlatform: string): Promise<ApplicationRecipe[]> {
    try {
      const query = `
        SELECT 
          id, name, ats_platform, company, url_pattern, description,
          version, steps, field_mappings, prerequisites, success_indicators,
          failure_indicators, estimated_duration, retry_strategy,
          created_at, updated_at, is_active
        FROM application_recipes
        WHERE ats_platform = $1 AND is_active = true
        ORDER BY created_at DESC
      `;

      const result = await this.pool.query(query, [atsPlatform]);
      return result.rows.map(row => this.mapRecipeRow(row));
    } catch (error) {
      logger.error('Failed to get recipes by platform', { 
        atsPlatform, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new ApplicationError('Failed to get recipes', 'DATABASE_ERROR', { atsPlatform, error });
    }
  }

  async cleanup(): Promise<void> {
    try {
      await this.pool.end();
      this.isInitialized = false;
      logger.info('Database service cleanup completed');
    } catch (error) {
      logger.error('Error during database cleanup', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  private mapJobRow(row: any): Job {
    return {
      id: row.id,
      external_id: row.external_id,
      source: row.source,
      url: row.url,
      title: row.title,
      company: row.company,
      location: row.location,
      country: row.country,
      is_remote: row.is_remote,
      employment_type: row.employment_type,
      experience_level: row.experience_level,
      salary_min: row.salary_min,
      salary_max: row.salary_max,
      currency: row.currency,
      description: row.description,
      requirements: row.requirements,
      skills: row.skills || [],
      ats_platform: row.ats_platform,
      apply_url: row.apply_url,
      posted_at: row.posted_at?.toISOString(),
      expires_at: row.expires_at?.toISOString(),
      is_active: row.is_active,
      ranking_score: row.ranking_score,
      similarity_score: row.similarity_score,
      application_method: row.application_method,
      created_at: row.created_at?.toISOString(),
      updated_at: row.updated_at?.toISOString(),
    };
  }

  private mapUserRow(row: any): UserProfile {
    return {
      id: row.id,
      name: row.name,
      email: row.email,
      phone: row.phone,
      location: row.location,
      country: row.country,
      resume_url: row.resume_url,
      cover_letter_template: row.cover_letter_template,
      skills: row.skills || [],
      experience_years: row.experience_years,
      current_title: row.current_title,
      linkedin_url: row.linkedin_url,
      github_url: row.github_url,
      portfolio_url: row.portfolio_url,
      preferences: row.preferences || {},
      credentials: row.credentials || {},
      created_at: row.created_at?.toISOString(),
      updated_at: row.updated_at?.toISOString(),
    };
  }

  private mapApplicationRow(row: any): Application {
    return {
      id: row.id,
      user_id: row.user_id,
      job_id: row.job_id,
      status: row.status,
      application_url: row.application_url,
      confirmation_number: row.confirmation_number,
      cover_letter_text: row.cover_letter_text,
      resume_version: row.resume_version,
      applied_at: row.applied_at?.toISOString(),
      last_updated: row.last_updated?.toISOString(),
      retry_count: row.retry_count,
      error_message: row.error_message,
      screenshot_urls: row.screenshot_urls ? JSON.parse(row.screenshot_urls) : [],
      metadata: row.metadata ? JSON.parse(row.metadata) : {},
      ats_application_id: row.ats_application_id,
      automation_log: row.automation_log ? JSON.parse(row.automation_log) : [],
      created_at: row.created_at?.toISOString(),
      updated_at: row.updated_at?.toISOString(),
    };
  }

  private mapRecipeRow(row: any): ApplicationRecipe {
    return {
      id: row.id,
      name: row.name,
      ats_platform: row.ats_platform,
      company: row.company,
      url_pattern: row.url_pattern,
      description: row.description,
      version: row.version,
      steps: JSON.parse(row.steps),
      field_mappings: JSON.parse(row.field_mappings),
      prerequisites: JSON.parse(row.prerequisites),
      success_indicators: JSON.parse(row.success_indicators),
      failure_indicators: JSON.parse(row.failure_indicators),
      estimated_duration: row.estimated_duration,
      retry_strategy: JSON.parse(row.retry_strategy),
      created_at: row.created_at?.toISOString(),
      updated_at: row.updated_at?.toISOString(),
      is_active: row.is_active,
    };
  }
}