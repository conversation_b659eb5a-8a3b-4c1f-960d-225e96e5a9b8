import { Queue, Worker, Job as <PERSON><PERSON><PERSON> } from 'bullmq';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';

import config from '@/config';
import { logger } from '@/utils/logger';
import { BrowserManager } from '@/automation/browser-manager';
import { RecipeEngine } from '@/recipes/recipe-engine';
import { MetricsService } from '@/services/metrics-service';
import { DatabaseService } from '@/services/database-service';
import { NotificationService } from '@/services/notification-service';
import {
  QueueJobData,
  ApplicationRequest,
  Application,
  ApplicationStatus,
  Job,
  UserProfile,
  ApplicationError,
  RateLimitError,
} from '@/models/types';

interface ProcessingContext {
  jobData: QueueJobData;
  bullJob: BullJob;
  startTime: number;
  applicationId?: string;
}

export class ApplicationProcessor {
  private queue: Queue;
  private worker: Worker;
  private redis: Redis;
  private browserManager: BrowserManager;
  private recipeEngine: RecipeEngine;
  private metrics: MetricsService;
  private database: DatabaseService;
  private notifications: NotificationService;
  private isProcessing = false;
  private rateLimitMap: Map<string, number> = new Map();

  constructor(
    browserManager: BrowserManager,
    recipeEngine: RecipeEngine,
    metrics: MetricsService,
    database: DatabaseService,
    notifications: NotificationService
  ) {
    this.browserManager = browserManager;
    this.recipeEngine = recipeEngine;
    this.metrics = metrics;
    this.database = database;
    this.notifications = notifications;

    // Initialize Redis connection
    this.redis = new Redis(config.redisUrl, {
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 1000,
      lazyConnect: true,
    });

    // Initialize queue and worker
    this.queue = new Queue(config.redisQueueName, {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: config.maxRetryAttempts,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
      },
    });

    this.worker = new Worker(
      config.redisQueueName,
      this.processJob.bind(this),
      {
        connection: this.redis,
        concurrency: config.concurrentBrowsers,
        limiter: {
          max: config.maxApplicationsPerHour,
          duration: 60 * 60 * 1000, // 1 hour
        },
      }
    );
  }

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing application processor');

      // Setup event handlers
      this.setupEventHandlers();

      // Start rate limit cleanup interval
      this.startRateLimitCleanup();

      logger.info('Application processor initialized successfully', {
        queueName: config.redisQueueName,
        concurrency: config.concurrentBrowsers,
        maxApplicationsPerHour: config.maxApplicationsPerHour,
      });
    } catch (error) {
      logger.error('Failed to initialize application processor', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async addApplicationToQueue(request: ApplicationRequest): Promise<string> {
    try {
      // Validate request
      if (!request.user_id || !request.job_id) {
        throw new ApplicationError('Invalid application request', 'INVALID_REQUEST');
      }

      // Get job and user details
      const [job, user] = await Promise.all([
        this.database.getJobById(request.job_id),
        this.database.getUserById(request.user_id),
      ]);

      if (!job) {
        throw new ApplicationError(`Job not found: ${request.job_id}`, 'JOB_NOT_FOUND');
      }

      if (!user) {
        throw new ApplicationError(`User not found: ${request.user_id}`, 'USER_NOT_FOUND');
      }

      // Check if application already exists
      const existingApplication = await this.database.getApplicationByUserAndJob(
        request.user_id,
        request.job_id
      );

      if (existingApplication && existingApplication.status !== 'failed') {
        throw new ApplicationError(
          'Application already exists for this job',
          'DUPLICATE_APPLICATION',
          { applicationId: existingApplication.id }
        );
      }

      // Check rate limits
      await this.checkRateLimit(user.id, job.ats_platform || 'unknown');

      // Create queue job data
      const jobData: QueueJobData = {
        application_request: request,
        job,
        user,
        priority: this.getPriorityValue(request.priority),
        retry_count: 0,
        scheduled_at: request.scheduled_at,
        metadata: request.metadata,
      };

      // Add to queue
      const bullJob = await this.queue.add(
        'process-application',
        jobData,
        {
          priority: jobData.priority,
          delay: request.scheduled_at ? 
            new Date(request.scheduled_at).getTime() - Date.now() : 
            undefined,
          jobId: uuidv4(),
        }
      );

      logger.info('Application added to queue', {
        jobId: bullJob.id,
        userId: request.user_id,
        jobTitle: job.title,
        company: job.company,
        priority: request.priority,
      });

      return bullJob.id!;

    } catch (error) {
      logger.error('Failed to add application to queue', {
        userId: request.user_id,
        jobId: request.job_id,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      this.queue.getWaiting(),
      this.queue.getActive(),
      this.queue.getCompleted(),
      this.queue.getFailed(),
      this.queue.getDelayed(),
    ]);

    const stats = {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    };

    // Update metrics
    this.metrics.recordQueueMetrics(
      config.redisQueueName,
      stats.waiting,
      stats.active,
      stats.completed,
      stats.failed
    );

    return stats;
  }

  async pauseProcessing(): Promise<void> {
    await this.worker.pause();
    this.isProcessing = false;
    logger.info('Application processing paused');
  }

  async resumeProcessing(): Promise<void> {
    await this.worker.resume();
    this.isProcessing = true;
    logger.info('Application processing resumed');
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down application processor');

    try {
      await this.worker.close();
      await this.queue.close();
      await this.redis.disconnect();
      
      logger.info('Application processor shutdown completed');
    } catch (error) {
      logger.error('Error during application processor shutdown', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  private async processJob(bullJob: BullJob<QueueJobData>): Promise<Application> {
    const context: ProcessingContext = {
      jobData: bullJob.data,
      bullJob,
      startTime: Date.now(),
    };

    try {
      logger.info('Starting job application processing', {
        bullJobId: bullJob.id,
        userId: context.jobData.user.id,
        jobId: context.jobData.job.id,
        jobTitle: context.jobData.job.title,
        company: context.jobData.job.company,
        attempt: bullJob.attemptsMade + 1,
      });

      // Update job progress
      await bullJob.updateProgress(10);

      // Check rate limits
      await this.checkRateLimit(
        context.jobData.user.id,
        context.jobData.job.ats_platform || 'unknown'
      );

      await bullJob.updateProgress(20);

      // Create application record
      const application = await this.createApplicationRecord(context);
      context.applicationId = application.id;

      await bullJob.updateProgress(30);

      // Execute application recipe
      const completedApplication = await this.recipeEngine.executeRecipe(
        context.jobData.job,
        context.jobData.user,
        context.jobData.recipe_id
      );

      await bullJob.updateProgress(80);

      // Update application in database
      await this.database.updateApplication(completedApplication);

      await bullJob.updateProgress(90);

      // Send notifications
      await this.sendNotifications(completedApplication, 'success');

      await bullJob.updateProgress(100);

      const duration = Date.now() - context.startTime;
      logger.info('Job application completed successfully', {
        bullJobId: bullJob.id,
        applicationId: completedApplication.id,
        userId: context.jobData.user.id,
        jobId: context.jobData.job.id,
        status: completedApplication.status,
        duration: `${duration}ms`,
      });

      return completedApplication;

    } catch (error) {
      const duration = Date.now() - context.startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isRetryable = error instanceof ApplicationError ? error.retryable : false;

      logger.error('Job application processing failed', {
        bullJobId: bullJob.id,
        applicationId: context.applicationId,
        userId: context.jobData.user.id,
        jobId: context.jobData.job.id,
        error: errorMessage,
        attempt: bullJob.attemptsMade + 1,
        maxAttempts: bullJob.opts.attempts,
        duration: `${duration}ms`,
        retryable: isRetryable,
      });

      // Update application record with error
      if (context.applicationId) {
        try {
          const failedApplication: Application = {
            id: context.applicationId,
            user_id: context.jobData.user.id,
            job_id: context.jobData.job.id,
            status: 'failed',
            error_message: errorMessage,
            retry_count: bullJob.attemptsMade + 1,
            last_updated: new Date().toISOString(),
            automation_log: [],
            screenshot_urls: [],
            metadata: { error: errorMessage, duration },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          await this.database.updateApplication(failedApplication);
          await this.sendNotifications(failedApplication, 'failure');
        } catch (updateError) {
          logger.error('Failed to update application with error', {
            applicationId: context.applicationId,
            error: updateError instanceof Error ? updateError.message : String(updateError),
          });
        }
      }

      // Handle rate limiting
      if (error instanceof RateLimitError) {
        const delay = this.calculateRateLimitDelay(context.jobData.job.ats_platform || 'unknown');
        throw new Error(`Rate limited. Retry after ${delay}ms`);
      }

      // Don't retry if it's not a retryable error
      if (error instanceof ApplicationError && !error.retryable) {
        throw new Error(`Non-retryable error: ${errorMessage}`);
      }

      throw error;
    }
  }

  private async createApplicationRecord(context: ProcessingContext): Promise<Application> {
    const application: Application = {
      id: uuidv4(),
      user_id: context.jobData.user.id,
      job_id: context.jobData.job.id,
      status: 'pending',
      last_updated: new Date().toISOString(),
      retry_count: context.bullJob.attemptsMade,
      automation_log: [],
      screenshot_urls: [],
      metadata: {
        bullJobId: context.bullJob.id,
        ...context.jobData.metadata,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    await this.database.createApplication(application);
    
    logger.debug('Application record created', {
      applicationId: application.id,
      userId: application.user_id,
      jobId: application.job_id,
    });

    return application;
  }

  private async checkRateLimit(userId: string, atsPlatform: string): Promise<void> {
    const now = Date.now();
    const hourKey = `${atsPlatform}:${Math.floor(now / (60 * 60 * 1000))}`;
    const userHourKey = `${userId}:${hourKey}`;

    // Check platform rate limit
    const platformCount = this.rateLimitMap.get(hourKey) || 0;
    if (platformCount >= config.maxApplicationsPerHour) {
      this.metrics.recordRateLimit(atsPlatform);
      throw new RateLimitError(
        `Rate limit exceeded for platform ${atsPlatform}: ${platformCount}/${config.maxApplicationsPerHour} per hour`,
        { platform: atsPlatform, count: platformCount, limit: config.maxApplicationsPerHour }
      );
    }

    // Check user rate limit (prevent abuse)
    const userCount = this.rateLimitMap.get(userHourKey) || 0;
    const userLimit = Math.min(50, config.maxApplicationsPerHour); // Max 50 applications per user per hour
    if (userCount >= userLimit) {
      throw new RateLimitError(
        `User rate limit exceeded: ${userCount}/${userLimit} per hour`,
        { userId, count: userCount, limit: userLimit }
      );
    }

    // Update counters
    this.rateLimitMap.set(hourKey, platformCount + 1);
    this.rateLimitMap.set(userHourKey, userCount + 1);
  }

  private calculateRateLimitDelay(atsPlatform: string): number {
    // Base delay with some randomization to avoid thundering herd
    const baseDelay = config.applicationDelayMs;
    const jitter = Math.random() * 1000; // 0-1 second jitter
    
    // Increase delay for specific platforms known to be strict
    const platformMultipliers: Record<string, number> = {
      linkedin: 3,
      indeed: 2,
      workday: 1.5,
      greenhouse: 1.2,
      lever: 1.2,
    };

    const multiplier = platformMultipliers[atsPlatform] || 1;
    return Math.floor(baseDelay * multiplier + jitter);
  }

  private getPriorityValue(priority: string): number {
    const priorityMap = {
      low: 1,
      normal: 5,
      high: 10,
    };
    return priorityMap[priority as keyof typeof priorityMap] || 5;
  }

  private async sendNotifications(application: Application, type: 'success' | 'failure'): Promise<void> {
    try {
      await this.notifications.sendApplicationNotification(application, type);
    } catch (error) {
      logger.warn('Failed to send notification', {
        applicationId: application.id,
        type,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private setupEventHandlers(): void {
    // Worker event handlers
    this.worker.on('completed', (job) => {
      logger.debug('Job completed', { jobId: job.id });
      this.metrics.incrementCounter('queue_jobs_completed');
    });

    this.worker.on('failed', (job, err) => {
      logger.error('Job failed', { 
        jobId: job?.id, 
        error: err.message,
        attempts: job?.attemptsMade,
        maxAttempts: job?.opts?.attempts,
      });
      this.metrics.incrementCounter('queue_jobs_failed');
    });

    this.worker.on('active', (job) => {
      logger.debug('Job started', { jobId: job.id });
      this.metrics.incrementCounter('queue_jobs_active');
    });

    this.worker.on('stalled', (jobId) => {
      logger.warn('Job stalled', { jobId });
      this.metrics.incrementCounter('queue_jobs_stalled');
    });

    this.worker.on('error', (error) => {
      logger.error('Worker error', { error: error.message });
      this.metrics.recordError('WORKER_ERROR');
    });

    // Queue event handlers
    this.queue.on('error', (error) => {
      logger.error('Queue error', { error: error.message });
      this.metrics.recordError('QUEUE_ERROR');
    });

    // Redis connection handlers
    this.redis.on('connect', () => {
      logger.info('Redis connected');
    });

    this.redis.on('error', (error) => {
      logger.error('Redis error', { error: error.message });
      this.metrics.recordError('REDIS_ERROR');
    });

    this.redis.on('close', () => {
      logger.warn('Redis connection closed');
    });
  }

  private startRateLimitCleanup(): void {
    // Clean up old rate limit entries every hour
    setInterval(() => {
      const now = Date.now();
      const cutoffHour = Math.floor(now / (60 * 60 * 1000)) - 1;

      for (const [key] of this.rateLimitMap) {
        const keyParts = key.split(':');
        const hour = parseInt(keyParts[keyParts.length - 1]);
        
        if (hour < cutoffHour) {
          this.rateLimitMap.delete(key);
        }
      }

      logger.debug('Rate limit cleanup completed', { 
        remainingEntries: this.rateLimitMap.size 
      });
    }, 60 * 60 * 1000); // Every hour
  }
}