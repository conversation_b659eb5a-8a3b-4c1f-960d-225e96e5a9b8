import dotenv from 'dotenv';
import { z } from 'zod';

dotenv.config();

const configSchema = z.object({
  // Server Configuration
  port: z.coerce.number().min(1000).max(65535).default(3003),
  nodeEnv: z.enum(['development', 'staging', 'production']).default('development'),
  serviceName: z.string().default('karmsakha-stagehand-worker'),

  // Database Configuration
  databaseUrl: z.string().url(),

  // Redis Configuration
  redisUrl: z.string().url().default('redis://localhost:6379'),
  redisQueueName: z.string().default('job-applications'),

  // Browser Automation Configuration
  headlessMode: z.coerce.boolean().default(true),
  browserTimeout: z.coerce.number().min(10000).max(300000).default(60000),
  concurrentBrowsers: z.coerce.number().min(1).max(20).default(5),
  maxRetryAttempts: z.coerce.number().min(1).max(10).default(3),
  screenshotOnError: z.coerce.boolean().default(true),
  userDataDir: z.string().default('/tmp/chrome-user-data'),
  stealthMode: z.coerce.boolean().default(true),

  // Rate Limiting
  maxApplicationsPerHour: z.coerce.number().min(10).max(1000).default(100),
  applicationDelayMs: z.coerce.number().min(500).max(10000).default(1000),
  batchSize: z.coerce.number().min(1).max(50).default(10),

  // External Services
  ingestorServiceUrl: z.string().url().default('http://localhost:3001'),
  rankerServiceUrl: z.string().url().default('http://localhost:3002'),
  gatewayServiceUrl: z.string().url().default('http://localhost:3000'),

  // Security
  jwtSecret: z.string().min(32),
  apiKey: z.string().min(16),

  // ATS Configuration
  ats: z.object({
    greenhouse: z.object({
      apiKey: z.string().optional(),
    }),
    lever: z.object({
      apiKey: z.string().optional(),
    }),
    workday: z.object({
      tenant: z.string().optional(),
      username: z.string().optional(),
      password: z.string().optional(),
    }),
    bambooHr: z.object({
      apiKey: z.string().optional(),
    }),
    icims: z.object({
      customerId: z.string().optional(),
    }),
  }).default({}),

  // Proxy Configuration
  proxy: z.object({
    enabled: z.coerce.boolean().default(false),
    host: z.string().optional(),
    port: z.coerce.number().optional(),
    username: z.string().optional(),
    password: z.string().optional(),
  }).default({}),

  // Monitoring
  metricsPort: z.coerce.number().min(1000).max(65535).default(9003),
  logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  enableDetailedLogging: z.coerce.boolean().default(false),

  // File Storage
  storage: z.object({
    resumePath: z.string().default('/app/storage/resumes'),
    coverLetterPath: z.string().default('/app/storage/cover-letters'),
    screenshotPath: z.string().default('/app/storage/screenshots'),
  }).default({}),

  // Application Templates
  templates: z.object({
    defaultCoverLetter: z.string().default('default'),
    resumeFormat: z.enum(['pdf', 'docx']).default('pdf'),
  }).default({}),

  // Notifications
  notifications: z.object({
    webhookUrl: z.string().url().optional(),
    slackWebhookUrl: z.string().url().optional(),
    emailEnabled: z.coerce.boolean().default(false),
  }).default({}),

  // Health Check
  healthCheckInterval: z.coerce.number().min(5000).max(300000).default(30000),
});

type Config = z.infer<typeof configSchema>;

const parseConfig = (): Config => {
  const rawConfig = {
    port: process.env.PORT,
    nodeEnv: process.env.NODE_ENV,
    serviceName: process.env.SERVICE_NAME,
    databaseUrl: process.env.DATABASE_URL,
    redisUrl: process.env.REDIS_URL,
    redisQueueName: process.env.REDIS_QUEUE_NAME,
    headlessMode: process.env.HEADLESS_MODE,
    browserTimeout: process.env.BROWSER_TIMEOUT,
    concurrentBrowsers: process.env.CONCURRENT_BROWSERS,
    maxRetryAttempts: process.env.MAX_RETRY_ATTEMPTS,
    screenshotOnError: process.env.SCREENSHOT_ON_ERROR,
    userDataDir: process.env.USER_DATA_DIR,
    stealthMode: process.env.STEALTH_MODE,
    maxApplicationsPerHour: process.env.MAX_APPLICATIONS_PER_HOUR,
    applicationDelayMs: process.env.APPLICATION_DELAY_MS,
    batchSize: process.env.BATCH_SIZE,
    ingestorServiceUrl: process.env.INGESTOR_SERVICE_URL,
    rankerServiceUrl: process.env.RANKER_SERVICE_URL,
    gatewayServiceUrl: process.env.GATEWAY_SERVICE_URL,
    jwtSecret: process.env.JWT_SECRET,
    apiKey: process.env.API_KEY,
    ats: {
      greenhouse: {
        apiKey: process.env.GREENHOUSE_API_KEY,
      },
      lever: {
        apiKey: process.env.LEVER_API_KEY,
      },
      workday: {
        tenant: process.env.WORKDAY_TENANT,
        username: process.env.WORKDAY_USERNAME,
        password: process.env.WORKDAY_PASSWORD,
      },
      bambooHr: {
        apiKey: process.env.BAMBOOHR_API_KEY,
      },
      icims: {
        customerId: process.env.ICIMS_CUSTOMER_ID,
      },
    },
    proxy: {
      enabled: process.env.USE_PROXY,
      host: process.env.PROXY_HOST,
      port: process.env.PROXY_PORT,
      username: process.env.PROXY_USERNAME,
      password: process.env.PROXY_PASSWORD,
    },
    metricsPort: process.env.METRICS_PORT,
    logLevel: process.env.LOG_LEVEL,
    enableDetailedLogging: process.env.ENABLE_DETAILED_LOGGING,
    storage: {
      resumePath: process.env.RESUME_STORAGE_PATH,
      coverLetterPath: process.env.COVER_LETTER_STORAGE_PATH,
      screenshotPath: process.env.SCREENSHOT_STORAGE_PATH,
    },
    templates: {
      defaultCoverLetter: process.env.DEFAULT_COVER_LETTER_TEMPLATE,
      resumeFormat: process.env.RESUME_FORMAT,
    },
    notifications: {
      webhookUrl: process.env.NOTIFICATION_WEBHOOK_URL,
      slackWebhookUrl: process.env.SLACK_WEBHOOK_URL,
      emailEnabled: process.env.EMAIL_NOTIFICATION_ENABLED,
    },
    healthCheckInterval: process.env.HEALTH_CHECK_INTERVAL,
  };

  try {
    return configSchema.parse(rawConfig);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(
        (err) => `${err.path.join('.')}: ${err.message}`
      );
      throw new Error(`Configuration validation failed:\n${errorMessages.join('\n')}`);
    }
    throw error;
  }
};

export const config = parseConfig();

export type { Config };

export default config;