{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/automation/*": ["automation/*"], "@/recipes/*": ["recipes/*"], "@/services/*": ["services/*"], "@/models/*": ["models/*"], "@/utils/*": ["utils/*"], "@/config/*": ["config/*"], "@/middleware/*": ["middleware/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}