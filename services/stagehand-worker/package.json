{"name": "<PERSON><PERSON><PERSON><PERSON>-stagehand-worker", "version": "1.0.0", "description": "Automated job application worker with browser automation and ATS platform support", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "typecheck": "tsc --noEmit"}, "keywords": ["job-automation", "puppeteer", "ats", "browser-automation", "stagehand"], "dependencies": {"express": "^4.18.2", "puppeteer": "^21.5.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-adblocker": "^2.13.6", "bullmq": "^4.15.2", "redis": "^4.6.10", "winston": "^3.11.0", "axios": "^1.6.2", "dotenv": "^16.3.1", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "cheerio": "^1.0.0-rc.12", "uuid": "^9.0.1", "zod": "^3.22.4", "node-cron": "^3.0.3", "prom-client": "^15.0.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.16", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.4.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "author": "Karmsakha Team", "license": "MIT"}