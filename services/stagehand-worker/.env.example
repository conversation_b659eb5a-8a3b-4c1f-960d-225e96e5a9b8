# Server Configuration
PORT=3003
NODE_ENV=development
SERVICE_NAME=karmsakha-stagehand-worker

# Database Configuration
DATABASE_URL=postgresql://karmsakha_user:secure_password@localhost:5432/karmsakha_db

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_QUEUE_NAME=job-applications

# Browser Automation Configuration
HEADLESS_MODE=true
BROWSER_TIMEOUT=60000
CONCURRENT_BROWSERS=5
MAX_RETRY_ATTEMPTS=3
SCREENSHOT_ON_ERROR=true
USER_DATA_DIR=/tmp/chrome-user-data
STEALTH_MODE=true

# Rate Limiting (Applications per hour)
MAX_APPLICATIONS_PER_HOUR=100
APPLICATION_DELAY_MS=1000
BATCH_SIZE=10

# External Services
INGESTOR_SERVICE_URL=http://localhost:3001
RANKER_SERVICE_URL=http://localhost:3002
GATEWAY_SERVICE_URL=http://localhost:3000

# Security
JWT_SECRET=your-jwt-secret-key
API_KEY=your-api-key

# ATS Configuration
GREENHOUSE_API_KEY=
LEVER_API_KEY=
WORKDAY_TENANT=
WORKDAY_USERNAME=
WORKDAY_PASSWORD=
BAMBOOHR_API_KEY=
ICIMS_CUSTOMER_ID=

# Proxy Configuration (Optional)
USE_PROXY=false
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# Monitoring
METRICS_PORT=9003
LOG_LEVEL=info
ENABLE_DETAILED_LOGGING=false

# File Storage
RESUME_STORAGE_PATH=/app/storage/resumes
COVER_LETTER_STORAGE_PATH=/app/storage/cover-letters
SCREENSHOT_STORAGE_PATH=/app/storage/screenshots

# Application Templates
DEFAULT_COVER_LETTER_TEMPLATE=default
RESUME_FORMAT=pdf

# Notification Configuration
NOTIFICATION_WEBHOOK_URL=
SLACK_WEBHOOK_URL=
EMAIL_NOTIFICATION_ENABLED=false

# Health Check
HEALTH_CHECK_INTERVAL=30000