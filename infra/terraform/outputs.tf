# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.vpc.public_subnet_ids
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = module.vpc.private_subnet_ids
}

# EKS Outputs
output "cluster_id" {
  description = "EKS cluster ID"
  value       = module.eks.cluster_id
}

output "cluster_arn" {
  description = "EKS cluster ARN"
  value       = module.eks.cluster_arn
}

output "cluster_endpoint" {
  description = "EKS cluster endpoint"
  value       = module.eks.cluster_endpoint
}

output "cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = module.eks.cluster_security_group_id
}

output "cluster_ca_certificate" {
  description = "Base64 encoded certificate data for the cluster"
  value       = module.eks.cluster_ca_certificate
  sensitive   = true
}

output "node_group_arns" {
  description = "ARNs of the EKS node groups"
  value       = module.eks.node_group_arns
}

# RDS Outputs
output "rds_endpoint" {
  description = "RDS instance endpoint"
  value       = module.rds.endpoint
}

output "rds_port" {
  description = "RDS instance port"
  value       = module.rds.port
}

output "rds_database_name" {
  description = "Name of the database"
  value       = module.rds.database_name
}

output "rds_username" {
  description = "Database username"
  value       = module.rds.username
  sensitive   = true
}

output "rds_password_secret_arn" {
  description = "ARN of the secret containing the database password"
  value       = module.rds.password_secret_arn
}

# Redis Outputs
output "redis_endpoint" {
  description = "Redis cluster endpoint"
  value       = module.redis.endpoint
}

output "redis_port" {
  description = "Redis cluster port"
  value       = module.redis.port
}

output "redis_security_group_id" {
  description = "Security group ID for Redis cluster"
  value       = module.redis.security_group_id
}

# S3 Outputs
output "audit_logs_bucket_name" {
  description = "Name of the S3 bucket for audit logs"
  value       = module.s3.audit_logs_bucket_name
}

output "audit_logs_bucket_arn" {
  description = "ARN of the S3 bucket for audit logs"
  value       = module.s3.audit_logs_bucket_arn
}

output "application_assets_bucket_name" {
  description = "Name of the S3 bucket for application assets"
  value       = module.s3.application_assets_bucket_name
}

# Connection strings for applications
output "database_url" {
  description = "Database connection URL"
  value       = "postgresql://${module.rds.username}:${module.rds.password}@${module.rds.endpoint}:${module.rds.port}/${module.rds.database_name}"
  sensitive   = true
}

output "redis_url" {
  description = "Redis connection URL"
  value       = "redis://${module.redis.endpoint}:${module.redis.port}"
}

# Kubernetes configuration
output "kubectl_config" {
  description = "kubectl config for accessing the cluster"
  value = {
    cluster_name     = module.eks.cluster_id
    cluster_endpoint = module.eks.cluster_endpoint
    cluster_ca       = module.eks.cluster_ca_certificate
    region          = var.aws_region
  }
  sensitive = true
}

# Cost tracking
output "estimated_monthly_cost" {
  description = "Estimated monthly cost in USD"
  value = {
    eks_cluster      = 73.0  # $0.10/hour * 24 * 30
    worker_nodes     = 150.0 # t3.medium spot * 3 nodes
    rds_instance     = 120.0 # db.t3.large
    redis_cache      = 15.0  # cache.t3.micro
    storage          = 25.0  # EBS + S3
    networking       = 20.0  # ALB + NAT Gateway
    monitoring       = 10.0  # CloudWatch
    total_usd        = 413.0
    total_inr        = 34275.0 # ~₹83 per USD
  }
}

# Security information
output "security_groups" {
  description = "Security group IDs for reference"
  value = {
    eks_cluster = module.eks.cluster_security_group_id
    rds         = module.rds.security_group_id
    redis       = module.redis.security_group_id
  }
}

# Load balancer information
output "load_balancer_dns" {
  description = "DNS name of the load balancer (available after deployment)"
  value       = "Will be available after Helm chart deployment"
}

# Namespace information
output "kubernetes_namespaces" {
  description = "Created Kubernetes namespaces"
  value = {
    application = kubernetes_namespace.karmsakha.metadata[0].name
    monitoring  = kubernetes_namespace.monitoring.metadata[0].name
  }
}