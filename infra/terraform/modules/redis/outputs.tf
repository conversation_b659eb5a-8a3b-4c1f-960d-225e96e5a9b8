output "endpoint" {
  description = "Redis cluster endpoint"
  value       = var.enable_replication ? aws_elasticache_replication_group.main[0].primary_endpoint_address : aws_elasticache_cluster.main.cache_nodes[0].address
}

output "port" {
  description = "Redis cluster port"
  value       = var.enable_replication ? aws_elasticache_replication_group.main[0].port : aws_elasticache_cluster.main.cache_nodes[0].port
}

output "cluster_id" {
  description = "Redis cluster ID"
  value       = aws_elasticache_cluster.main.cluster_id
}

output "security_group_id" {
  description = "Security group ID for Redis cluster"
  value       = aws_security_group.redis.id
}

output "subnet_group_name" {
  description = "Name of the ElastiCache subnet group"
  value       = aws_elasticache_subnet_group.main.name
}

output "auth_token_secret_arn" {
  description = "ARN of the secret containing the Redis auth token"
  value       = var.auth_token_enabled ? aws_secretsmanager_secret.redis_auth_token[0].arn : null
}

output "replication_group_id" {
  description = "Redis replication group ID (if enabled)"
  value       = var.enable_replication ? aws_elasticache_replication_group.main[0].replication_group_id : null
}

output "reader_endpoint_address" {
  description = "Redis reader endpoint address (if replication enabled)"
  value       = var.enable_replication ? aws_elasticache_replication_group.main[0].reader_endpoint_address : null
}

output "log_group_name" {
  description = "CloudWatch log group name for Redis logs"
  value       = aws_cloudwatch_log_group.redis_slow_log.name
}