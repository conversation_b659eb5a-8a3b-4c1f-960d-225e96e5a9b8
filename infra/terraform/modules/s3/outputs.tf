output "audit_logs_bucket_name" {
  description = "Name of the S3 bucket for audit logs"
  value       = aws_s3_bucket.audit_logs.id
}

output "audit_logs_bucket_arn" {
  description = "ARN of the S3 bucket for audit logs"
  value       = aws_s3_bucket.audit_logs.arn
}

output "application_assets_bucket_name" {
  description = "Name of the S3 bucket for application assets"
  value       = aws_s3_bucket.application_assets.id
}

output "application_assets_bucket_arn" {
  description = "ARN of the S3 bucket for application assets"
  value       = aws_s3_bucket.application_assets.arn
}

output "access_logs_bucket_name" {
  description = "Name of the S3 bucket for access logs"
  value       = aws_s3_bucket.access_logs.id
}

output "audit_service_role_arn" {
  description = "ARN of the IAM role for audit service"
  value       = aws_iam_role.audit_service_role.arn
}

output "kms_key_id" {
  description = "KMS key ID for audit logs encryption"
  value       = aws_kms_key.s3_audit_logs.key_id
}

output "kms_key_arn" {
  description = "KMS key ARN for audit logs encryption"
  value       = aws_kms_key.s3_audit_logs.arn
}

output "audit_logs_bucket_domain_name" {
  description = "Domain name of the audit logs bucket"
  value       = aws_s3_bucket.audit_logs.bucket_domain_name
}

output "application_assets_bucket_domain_name" {
  description = "Domain name of the application assets bucket"
  value       = aws_s3_bucket.application_assets.bucket_domain_name
}