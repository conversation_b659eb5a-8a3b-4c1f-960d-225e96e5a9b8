variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "audit_log_retention_days" {
  description = "Number of days to retain audit logs"
  type        = number
  default     = 2555  # ~7 years for compliance
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}