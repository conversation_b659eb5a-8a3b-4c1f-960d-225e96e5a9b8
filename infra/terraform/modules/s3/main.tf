# S3 Bucket for Audit Logs (DPDP Act 2023 Compliance)
resource "aws_s3_bucket" "audit_logs" {
  bucket = "${var.project_name}-${var.environment}-audit-logs-${random_string.bucket_suffix.result}"

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-audit-logs"
    Purpose     = "DPDP Act 2023 Compliance"
    DataClass   = "Confidential"
  })
}

# S3 Bucket for Application Assets
resource "aws_s3_bucket" "application_assets" {
  bucket = "${var.project_name}-${var.environment}-assets-${random_string.bucket_suffix.result}"

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-assets"
    Purpose     = "Application Assets"
    DataClass   = "Public"
  })
}

# Random string for bucket name uniqueness
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket Versioning - Audit Logs
resource "aws_s3_bucket_versioning" "audit_logs" {
  bucket = aws_s3_bucket.audit_logs.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Versioning - Application Assets
resource "aws_s3_bucket_versioning" "application_assets" {
  bucket = aws_s3_bucket.application_assets.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Encryption - Audit Logs
resource "aws_s3_bucket_server_side_encryption_configuration" "audit_logs" {
  bucket = aws_s3_bucket.audit_logs.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.s3_audit_logs.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# S3 Bucket Encryption - Application Assets
resource "aws_s3_bucket_server_side_encryption_configuration" "application_assets" {
  bucket = aws_s3_bucket.application_assets.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# KMS Key for Audit Logs Encryption
resource "aws_kms_key" "s3_audit_logs" {
  description             = "KMS key for S3 audit logs encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow S3 Service"
        Effect = "Allow"
        Principal = {
          Service = "s3.amazonaws.com"
        }
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = var.tags
}

resource "aws_kms_alias" "s3_audit_logs" {
  name          = "alias/${var.project_name}-${var.environment}-s3-audit-logs"
  target_key_id = aws_kms_key.s3_audit_logs.key_id
}

data "aws_caller_identity" "current" {}

# S3 Bucket Public Access Block - Audit Logs
resource "aws_s3_bucket_public_access_block" "audit_logs" {
  bucket = aws_s3_bucket.audit_logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Public Access Block - Application Assets
resource "aws_s3_bucket_public_access_block" "application_assets" {
  bucket = aws_s3_bucket.application_assets.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

# S3 Bucket Policy - Audit Logs (Restrict Access)
resource "aws_s3_bucket_policy" "audit_logs" {
  bucket = aws_s3_bucket.audit_logs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "DenyInsecureConnections"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.audit_logs.arn,
          "${aws_s3_bucket.audit_logs.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      },
      {
        Sid    = "AllowApplicationAccess"
        Effect = "Allow"
        Principal = {
          AWS = [
            aws_iam_role.audit_service_role.arn
          ]
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.audit_logs.arn,
          "${aws_s3_bucket.audit_logs.arn}/*"
        ]
      }
    ]
  })
}

# S3 Bucket Policy - Application Assets (Public Read)
resource "aws_s3_bucket_policy" "application_assets" {
  bucket = aws_s3_bucket.application_assets.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.application_assets.arn}/*"
      }
    ]
  })
}

# IAM Role for Audit Service
resource "aws_iam_role" "audit_service_role" {
  name = "${var.project_name}-${var.environment}-audit-service-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for Audit Service
resource "aws_iam_role_policy" "audit_service_policy" {
  name = "${var.project_name}-${var.environment}-audit-service-policy"
  role = aws_iam_role.audit_service_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObjectVersion"
        ]
        Resource = [
          aws_s3_bucket.audit_logs.arn,
          "${aws_s3_bucket.audit_logs.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = aws_kms_key.s3_audit_logs.arn
      }
    ]
  })
}

# S3 Bucket Lifecycle Configuration - Audit Logs
resource "aws_s3_bucket_lifecycle_configuration" "audit_logs" {
  bucket = aws_s3_bucket.audit_logs.id

  rule {
    id     = "audit_log_lifecycle"
    status = "Enabled"

    # Transition to Intelligent Tiering after 30 days
    transition {
      days          = 30
      storage_class = "INTELLIGENT_TIERING"
    }

    # Transition to Glacier after 90 days
    transition {
      days          = 90
      storage_class = "GLACIER"
    }

    # Transition to Deep Archive after 365 days
    transition {
      days          = 365
      storage_class = "DEEP_ARCHIVE"
    }

    # Delete after retention period
    expiration {
      days = var.audit_log_retention_days
    }

    # Clean up incomplete multipart uploads
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }

    # Clean up old versions
    noncurrent_version_expiration {
      noncurrent_days = 90
    }
  }
}

# S3 Bucket Lifecycle Configuration - Application Assets
resource "aws_s3_bucket_lifecycle_configuration" "application_assets" {
  bucket = aws_s3_bucket.application_assets.id

  rule {
    id     = "assets_lifecycle"
    status = "Enabled"

    # Clean up incomplete multipart uploads
    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }

    # Keep only latest version of assets
    noncurrent_version_expiration {
      noncurrent_days = 30
    }
  }
}

# S3 Bucket Logging - Audit Logs
resource "aws_s3_bucket_logging" "audit_logs" {
  bucket = aws_s3_bucket.audit_logs.id

  target_bucket = aws_s3_bucket.access_logs.id
  target_prefix = "audit-logs-access/"
}

# S3 Bucket for Access Logs
resource "aws_s3_bucket" "access_logs" {
  bucket = "${var.project_name}-${var.environment}-access-logs-${random_string.bucket_suffix.result}"

  tags = merge(var.tags, {
    Name        = "${var.project_name}-${var.environment}-access-logs"
    Purpose     = "S3 Access Logs"
  })
}

resource "aws_s3_bucket_public_access_block" "access_logs" {
  bucket = aws_s3_bucket.access_logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# CloudWatch Metrics for S3 buckets
resource "aws_s3_bucket_metric" "audit_logs_metrics" {
  bucket = aws_s3_bucket.audit_logs.id
  name   = "EntireBucket"
}

# CloudWatch Alarms for audit log bucket
resource "aws_cloudwatch_metric_alarm" "audit_logs_size" {
  alarm_name          = "${var.project_name}-${var.environment}-audit-logs-size"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "BucketSizeBytes"
  namespace           = "AWS/S3"
  period              = "86400"  # 24 hours
  statistic           = "Average"
  threshold           = "10737418240"  # 10 GB
  alarm_description   = "This metric monitors audit logs bucket size"

  dimensions = {
    BucketName  = aws_s3_bucket.audit_logs.id
    StorageType = "StandardStorage"
  }

  tags = var.tags
}

# EventBridge rule for S3 events (for real-time processing)
resource "aws_cloudwatch_event_rule" "s3_audit_events" {
  name        = "${var.project_name}-${var.environment}-s3-audit-events"
  description = "Capture S3 events for audit logs bucket"

  event_pattern = jsonencode({
    source      = ["aws.s3"]
    detail-type = ["AWS API Call via CloudTrail"]
    detail = {
      eventSource = ["s3.amazonaws.com"]
      requestParameters = {
        bucketName = [aws_s3_bucket.audit_logs.id]
      }
    }
  })

  tags = var.tags
}