# Karmsakha Audit Service Helm Chart Values

# Global settings
global:
  imageRegistry: ""
  imageRepository: karmsakha
  imageTag: "latest"
  imagePullPolicy: IfNotPresent
  environment: production

# Service configuration
service:
  name: karmsakha-audit
  type: ClusterIP
  port: 3005
  targetPort: 3005
  annotations: {}

# Deployment configuration
deployment:
  replicaCount: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

# Image configuration
image:
  repository: karmsakha/audit
  tag: ""
  pullPolicy: IfNotPresent

# Environment variables
env:
  NODE_ENV: production
  PORT: 3005
  HOST: "0.0.0.0"
  LOG_LEVEL: info
  
  # DPDP Compliance settings
  DATA_RETENTION_DAYS: 2555
  CONSENT_EXPIRY_DAYS: 365
  AUDIT_LOG_RETENTION_DAYS: 2555
  BREACH_NOTIFICATION_TIMEOUT_HOURS: 72
  
  # Security settings
  RATE_LIMIT_WINDOW_MS: 900000
  RATE_LIMIT_MAX_REQUESTS: 100
  METRICS_ENABLED: true

# Secrets configuration
secrets:
  # Database
  databaseUrl: ""
  
  # Redis
  redisUrl: ""
  
  # JWT
  jwtSecret: ""
  
  # Email configuration
  smtpHost: ""
  smtpPort: "587"
  smtpUser: ""
  smtpPassword: ""
  fromEmail: "<EMAIL>"
  
  # Compliance contacts
  complianceOfficerEmail: "<EMAIL>"
  dpoEmail: "<EMAIL>"
  
  # Encryption
  encryptionKey: ""

# ConfigMap configuration
configMap:
  # External service URLs
  gatewayUrl: "http://karmsakha-gateway:3000"
  ingestorUrl: "http://karmsakha-ingestor:3001"
  rankerUrl: "http://karmsakha-ranker:3002"
  workerUrl: "http://karmsakha-worker:3003"
  
  # Report configuration
  reportStoragePath: "/app/reports"
  reportRetentionDays: "1095"

# Resources
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

# Health checks
healthCheck:
  enabled: true
  livenessProbe:
    httpGet:
      path: /health/live
      port: 3005
    initialDelaySeconds: 30
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /health/ready
      port: 3005
    initialDelaySeconds: 15
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# Scaling configuration
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  runAsGroup: 1001
  fsGroup: 1001
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: false
  capabilities:
    drop:
      - ALL

# Pod security context
podSecurityContext:
  fsGroup: 1001

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod annotations and labels
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "3005"
  prometheus.io/path: "/metrics"

podLabels:
  app.kubernetes.io/component: audit
  app.kubernetes.io/part-of: karmsakha-platform

# Node selection
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - karmsakha-audit
        topologyKey: kubernetes.io/hostname

# Persistence
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi
  mountPath: /app/reports
  annotations: {}

# Network policies
networkPolicy:
  enabled: true
  ingress:
    - from:
      - namespaceSelector:
          matchLabels:
            name: karmsakha
      ports:
      - protocol: TCP
        port: 3005
  egress:
    - to: []
      ports:
      - protocol: TCP
        port: 5432  # PostgreSQL
      - protocol: TCP
        port: 6379  # Redis
      - protocol: TCP
        port: 587   # SMTP
      - protocol: TCP
        port: 80    # HTTP
      - protocol: TCP
        port: 443   # HTTPS

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Service monitor for Prometheus
serviceMonitor:
  enabled: true
  namespace: monitoring
  interval: 30s
  scrapeTimeout: 10s

# Background jobs
jobs:
  # Consent expiry check job
  consentExpiryCheck:
    enabled: true
    schedule: "0 2 * * *"  # Daily at 2 AM
    restartPolicy: OnFailure
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

  # Audit log cleanup job
  auditLogCleanup:
    enabled: true
    schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM
    restartPolicy: OnFailure
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi

  # Compliance report generation
  complianceReport:
    enabled: true
    schedule: "0 1 1 * *"  # Monthly on 1st at 1 AM
    restartPolicy: OnFailure
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi

# RBAC
rbac:
  create: true
  rules:
    - apiGroups: [""]
      resources: ["configmaps", "secrets"]
      verbs: ["get", "list", "watch"]
    - apiGroups: ["batch"]
      resources: ["jobs", "cronjobs"]
      verbs: ["get", "list", "watch", "create", "update", "patch"]

# Additional environment-specific overrides
dev:
  replicaCount: 1
  autoscaling:
    enabled: false
  persistence:
    enabled: false
  networkPolicy:
    enabled: false

staging:
  replicaCount: 2
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

production:
  replicaCount: 3
  persistence:
    size: 50Gi
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 1Gi