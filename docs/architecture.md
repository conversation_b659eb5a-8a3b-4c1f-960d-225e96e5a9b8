# KarmSakha AutoPilot v3 - System Architecture

## Overview

Multi-tenant job platform engineered for Indian market with global remote opportunities. Ingests 50K+ Indian jobs daily, matches against user résumés using vector similarity, and auto-applies through browser automation while maintaining DPDP Act 2023 compliance.

## Core Components

### 1. Job Ingestion Layer
**Service**: `/services/ingestor/`  
**Technology**: Node.js 20 + TypeScript + BullMQ + Bright Data proxies

#### Data Sources
- **Shine.com**: Apify actor `saswave/shine-jobs-scraper`
- **Naukri.com**: Playwright + JSON-LD extraction
- **LinkedIn**: JobSpy (`speedyapply/JobSpy`) + API wrapper (`VishwaGauravIn/linkedin-jobs-api`)
- **ATS Systems**: Greenhouse, Lever, Workday, Ashby APIs
- **Remote Platforms**: RemoteOK JSON API, WeWorkRemotely RSS

#### Processing Pipeline
```
Sources → Bright Data Proxy → Standardization → Deduplication → Vector Embedding → PostgreSQL
```

### 2. Vector Ranking Engine
**Service**: `/services/ranker/`  
**Technology**: Python 3.12 + FastAPI + pgvector + OpenAI embeddings

#### Matching Algorithm
1. **Embedding Generation**: OpenAI `text-embedding-3-small` for jobs + résumés
2. **Vector Storage**: pgvector extension in PostgreSQL
3. **Similarity Search**: Cosine similarity with threshold ≥0.85
4. **Ranking Factors**:
   - Skills match (40%)
   - Experience level (25%)
   - Location preference (20%)
   - Salary range (15%)

### 3. Application Worker
**Service**: `/services/worker/`  
**Technology**: Node.js 20 + Puppeteer + Stagehand recipes

#### Automation Capabilities
- **ATS Integration**: Pre-built recipes for major platforms
- **CAPTCHA Bypass**: Audio CAPTCHA → Whisper transcription
- **OTP Handling**: Twilio SMS integration
- **Form Automation**: Intelligent field detection and filling
- **Rate Limiting**: Max 100 applications/hour/worker

### 4. GraphQL Gateway
**Service**: `/gateway/`  
**Technology**: Node.js 20 + Apollo Server + JWT auth

#### API Endpoints
- `jobs(filters, pagination)` → Ranked job listings
- `applyToJob(jobId, userId)` → Queue application
- `applicationStatus(applicationId)` → Track progress
- `userProfile(userId)` → Manage résumé/preferences

### 5. Admin Dashboard
**Service**: `/frontend/`  
**Technology**: React 18 + TypeScript + Tailwind CSS + GraphQL

#### Features
- Real-time job ingestion monitoring
- Application success rate analytics
- User engagement metrics
- DPDP compliance dashboard

### 6. Compliance & Audit
**Service**: `/services/audit/`  
**Technology**: Node.js 20 + AWS S3 + Lambda

#### DPDP Act 2023 Compliance
- **Data Minimization**: Collect only necessary fields
- **Retention Policies**: Auto-delete after user-specified days
- **Audit Trails**: Immutable JSON logs in S3
- **Consent Management**: Granular permissions tracking

## Data Architecture

### Database Schema

```sql
-- Core job storage
CREATE TABLE jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source TEXT NOT NULL,
  external_id TEXT NOT NULL,
  url TEXT UNIQUE NOT NULL,
  title TEXT NOT NULL,
  company TEXT NOT NULL,
  location TEXT,
  country TEXT DEFAULT 'IN',
  is_remote BOOLEAN DEFAULT FALSE,
  salary_min NUMERIC,
  salary_max NUMERIC,
  currency TEXT DEFAULT 'INR',
  description TEXT,
  requirements TEXT,
  posted_at TIMESTAMP NOT NULL,
  expires_at TIMESTAMP,
  embedding vector(1536), -- OpenAI embedding dimension
  raw_data JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(source, external_id)
);

-- User profiles and résumés
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  phone TEXT,
  location TEXT,
  country TEXT DEFAULT 'IN',
  resume_text TEXT,
  resume_embedding vector(1536),
  preferences JSONB, -- salary range, locations, remote preference
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Application tracking
CREATE TABLE applications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  job_id UUID REFERENCES jobs(id),
  status TEXT DEFAULT 'queued', -- queued, processing, applied, failed
  applied_at TIMESTAMP,
  response_data JSONB,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, job_id)
);

-- Audit logs for DPDP compliance
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  action TEXT NOT NULL, -- view, apply, export, delete
  resource_type TEXT NOT NULL, -- job, profile, application
  resource_id UUID,
  metadata JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_jobs_embedding ON jobs USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX idx_jobs_country_remote ON jobs(country, is_remote);
CREATE INDEX idx_jobs_posted_at ON jobs(posted_at DESC);
CREATE INDEX idx_applications_user_status ON applications(user_id, status);
```

### Message Queue Architecture

```javascript
// BullMQ job types
const jobTypes = {
  SCRAPE_JOBS: 'scrape:jobs',
  GENERATE_EMBEDDING: 'embedding:generate',
  RANK_JOBS: 'ranking:process',
  APPLY_TO_JOB: 'application:submit',
  AUDIT_LOG: 'audit:log',
  CLEANUP_DATA: 'cleanup:expired'
};

// Queue configuration
const queues = {
  ingestion: { concurrency: 5, rateLimiter: { max: 100, duration: 60000 } },
  ranking: { concurrency: 10, rateLimiter: { max: 1000, duration: 60000 } },
  application: { concurrency: 3, rateLimiter: { max: 100, duration: 3600000 } }, // 100/hour
  audit: { concurrency: 1, rateLimiter: { max: 1000, duration: 60000 } }
};
```

## Infrastructure Architecture

### AWS Components

```
┌─────────────────┐    ┌─────────────────┐
│   CloudFront    │    │   Route 53      │
│   (CDN)         │    │   (DNS)         │
└─────────┬───────┘    └─────────────────┘
          │
┌─────────▼───────┐    ┌─────────────────┐
│   ALB           │    │   EKS Cluster   │
│   (Load Balancer)│────│   (Kubernetes)  │
└─────────────────┘    └─────────┬───────┘
                                 │
┌─────────────────┐    ┌─────────▼───────┐
│   RDS PostgreSQL│    │   ElastiCache   │
│   + pgvector    │    │   Redis         │
└─────────────────┘    └─────────────────┘

┌─────────────────┐    ┌─────────────────┐
│   S3 Buckets    │    │   CloudWatch    │
│   (Audit Logs)  │    │   (Monitoring)  │
└─────────────────┘    └─────────────────┘
```

### Kubernetes Deployment

```yaml
# Resource allocation per service
resources:
  ingestor:
    replicas: 3
    cpu: "500m"
    memory: "1Gi"
  
  ranker:
    replicas: 5
    cpu: "1000m"
    memory: "2Gi"
  
  worker:
    replicas: 2
    cpu: "2000m"
    memory: "4Gi"
  
  gateway:
    replicas: 3
    cpu: "500m"
    memory: "1Gi"
  
  frontend:
    replicas: 2
    cpu: "200m"
    memory: "512Mi"
```

## Security Architecture

### Authentication & Authorization
- **JWT Tokens**: 15-minute access + 7-day refresh
- **Multi-tenant**: Tenant isolation via database row-level security
- **API Rate Limiting**: Redis-based sliding window
- **Input Validation**: Joi schemas + SQL injection prevention

### Data Protection
- **Encryption at Rest**: AES-256 for PII fields
- **Encryption in Transit**: TLS 1.3 for all communications
- **Key Management**: AWS KMS with rotation
- **Secrets**: AWS Secrets Manager + Kubernetes secrets

### DPDP Act 2023 Compliance
- **Lawful Basis**: Consent + Legitimate Interest
- **Data Minimization**: Collect only necessary fields
- **Purpose Limitation**: Use data only for job matching
- **Retention**: Configurable deletion (30-365 days)
- **Transparency**: Clear privacy policy + consent forms

## Performance Requirements

### Throughput Targets
- **Job Ingestion**: 50,000 Indian + 5,000 remote jobs per day
- **Ranking API**: <500ms response time for job matching
- **Application Rate**: 100 applications/hour per worker
- **Concurrent Users**: 10,000 MAU with 500 peak concurrent

### Scalability Strategy
- **Horizontal Scaling**: Kubernetes HPA based on CPU/memory
- **Database Scaling**: Read replicas + connection pooling
- **Cache Strategy**: Redis for sessions, job rankings
- **CDN**: CloudFront for static assets

## Cost Optimization

### Infrastructure Costs (₹15,000/month target)
- **EKS Cluster**: ₹5,000 (3 t3.medium nodes)
- **RDS PostgreSQL**: ₹4,000 (db.t3.large)
- **ElastiCache Redis**: ₹2,000 (cache.t3.micro)
- **S3 + CloudFront**: ₹1,500
- **Monitoring**: ₹1,500 (CloudWatch + Grafana)
- **Networking**: ₹1,000 (NAT Gateway + ALB)

### Cost Optimization Techniques
- **Spot Instances**: 60% cost reduction for worker nodes
- **Reserved Instances**: 40% discount for predictable workloads
- **Auto Scaling**: Scale down during low traffic
- **Data Lifecycle**: S3 Intelligent Tiering for audit logs

## Monitoring & Observability

### Metrics Collection
- **Application Metrics**: Prometheus + Grafana
- **Infrastructure Metrics**: CloudWatch
- **Error Tracking**: Sentry
- **Logs**: ELK Stack (Elasticsearch, Logstash, Kibana)

### Key Performance Indicators
- **System Health**: Uptime, response times, error rates
- **Business Metrics**: Jobs ingested, matches made, applications sent
- **User Engagement**: DAU, session duration, conversion rates
- **Cost Analytics**: Cost per application, infrastructure efficiency

## Disaster Recovery

### Backup Strategy
- **Database**: Daily automated backups with 30-day retention
- **Configuration**: GitOps with ArgoCD for reproducible deployments
- **Secrets**: Encrypted backups in AWS Secrets Manager
- **Application Data**: S3 cross-region replication

### Recovery Procedures
- **RTO**: 2 hours for full service restoration
- **RPO**: 1 hour maximum data loss
- **Failover**: Automated DNS failover to backup region
- **Testing**: Monthly disaster recovery drills

## Compliance & Governance

### Data Governance
- **Data Classification**: Public, Internal, Confidential, Restricted
- **Access Controls**: Role-based permissions with audit trails
- **Data Quality**: Validation rules + monitoring dashboards
- **Retention Policies**: Automated cleanup based on legal requirements

### Regulatory Compliance
- **DPDP Act 2023**: Indian data protection law compliance
- **ISO 27001**: Information security management
- **SOC 2**: Security and availability controls
- **GDPR**: For EU users (future expansion)

## Future Enhancements

### Phase 2 (Months 3-6)
- **Mobile App**: React Native for iOS/Android
- **Advanced AI**: Fine-tuned LLMs for job matching
- **International Expansion**: Support for US/UK markets
- **Enterprise Features**: Team management, bulk operations

### Phase 3 (Months 6-12)
- **Machine Learning**: Custom ranking algorithms
- **Real-time Processing**: Streaming job updates
- **Advanced Analytics**: Predictive job market insights
- **API Marketplace**: Third-party integrations

---

*This architecture supports the success criteria of 50K+ jobs/day ingestion, 100+ applications/hour, and ₹15K/month operational costs while maintaining DPDP Act 2023 compliance.*