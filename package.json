{"name": "karmsakha-autopilot", "version": "1.0.0", "description": "Production-ready multi-tenant job platform with AI-powered matching and automated applications", "private": true, "workspaces": ["services/*", "gateway", "frontend"], "scripts": {"build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "type-check": "npm run type-check --workspaces", "dev": "concurrently \"npm run dev --workspace=gateway\" \"npm run dev --workspace=frontend\"", "e2e": "playwright test", "docker:build": "./scripts/build-images.sh", "k8s:deploy": "helm upgrade --install karmsakha ./k8s/charts/", "tf:plan": "cd infra/terraform && terraform plan", "tf:apply": "cd infra/terraform && terraform apply"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0", "@playwright/test": "^1.40.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}