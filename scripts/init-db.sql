-- Initialize KarmSakha Database
-- This script sets up the database schema with pgvector extension for vector similarity search

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Create database user for application
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'karmsakha_app') THEN
        CREATE USER karmsakha_app WITH PASSWORD 'app_password_change_in_prod';
    END IF;
END
$$;

-- Grant necessary permissions
GRANT CONNECT ON DATABASE karmsakha TO karmsakha_app;
GRANT USAGE ON SCHEMA public TO karmsakha_app;
GRANT CREATE ON SCHEMA public TO karmsakha_app;

-- Create jobs table with vector embeddings
CREATE TABLE IF NOT EXISTS jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source TEXT NOT NULL,
    external_id TEXT NOT NULL,
    url TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    company TEXT NOT NULL,
    location TEXT,
    country TEXT DEFAULT 'IN',
    is_remote BOOLEAN DEFAULT FALSE,
    employment_type TEXT DEFAULT 'full-time', -- full-time, part-time, contract, internship
    experience_level TEXT, -- entry, mid, senior, executive
    salary_min NUMERIC,
    salary_max NUMERIC,
    currency TEXT DEFAULT 'INR',
    description TEXT,
    requirements TEXT,
    skills TEXT[], -- Array of required skills
    posted_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    raw_data JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(source, external_id)
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    phone TEXT,
    location TEXT,
    country TEXT DEFAULT 'IN',
    resume_text TEXT,
    resume_embedding vector(1536),
    skills TEXT[], -- Array of user skills
    experience_years INTEGER,
    current_title TEXT,
    preferred_salary_min NUMERIC,
    preferred_salary_max NUMERIC,
    preferred_currency TEXT DEFAULT 'INR',
    preferred_locations TEXT[], -- Array of preferred locations
    remote_preference BOOLEAN DEFAULT FALSE,
    preferences JSONB, -- Additional preferences as JSON
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create applications table
CREATE TABLE IF NOT EXISTS applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'queued', -- queued, processing, applied, failed, rejected, interview, hired
    similarity_score NUMERIC, -- Cosine similarity score
    applied_at TIMESTAMP,
    response_data JSONB, -- Application response from job portal
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    metadata JSONB, -- Additional application metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, job_id)
);

-- Create audit_logs table for DPDP compliance
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action TEXT NOT NULL, -- view, apply, export, delete, update, login, logout
    resource_type TEXT NOT NULL, -- job, profile, application, system
    resource_id UUID,
    details JSONB, -- Additional details about the action
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    result TEXT, -- success, failure, partial
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create companies table for enhanced job data
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    website TEXT,
    size_category TEXT, -- startup, small, medium, large, enterprise
    industry TEXT,
    headquarters TEXT,
    logo_url TEXT,
    glassdoor_rating NUMERIC,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create job_categories table
CREATE TABLE IF NOT EXISTS job_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    parent_id UUID REFERENCES job_categories(id),
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create job_job_categories junction table
CREATE TABLE IF NOT EXISTS job_job_categories (
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    category_id UUID REFERENCES job_categories(id) ON DELETE CASCADE,
    PRIMARY KEY (job_id, category_id)
);

-- Create user_preferences table for detailed preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preference_type TEXT NOT NULL, -- notification, job_alert, privacy
    preference_key TEXT NOT NULL,
    preference_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, preference_type, preference_key)
);

-- Create indexes for performance
-- Job search indexes
CREATE INDEX IF NOT EXISTS idx_jobs_embedding ON jobs USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_jobs_country_remote ON jobs(country, is_remote);
CREATE INDEX IF NOT EXISTS idx_jobs_posted_at ON jobs(posted_at DESC);
CREATE INDEX IF NOT EXISTS idx_jobs_active ON jobs(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs USING gin(to_tsvector('english', location));
CREATE INDEX IF NOT EXISTS idx_jobs_title_company ON jobs USING gin(to_tsvector('english', title || ' ' || company));
CREATE INDEX IF NOT EXISTS idx_jobs_skills ON jobs USING gin(skills);

-- User search indexes
CREATE INDEX IF NOT EXISTS idx_users_embedding ON users USING ivfflat (resume_embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_users_skills ON users USING gin(skills);

-- Application tracking indexes
CREATE INDEX IF NOT EXISTS idx_applications_user_status ON applications(user_id, status);
CREATE INDEX IF NOT EXISTS idx_applications_job_status ON applications(job_id, status);
CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_applications_similarity_score ON applications(similarity_score DESC);

-- Audit logs indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);

-- Company indexes
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_industry ON companies(industry);

-- Create functions for automatic updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_jobs_updated_at BEFORE UPDATE ON jobs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_applications_updated_at BEFORE UPDATE ON applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function for similarity search
CREATE OR REPLACE FUNCTION find_similar_jobs(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.8,
    max_results int DEFAULT 50
)
RETURNS TABLE (
    job_id uuid,
    title text,
    company text,
    location text,
    similarity float
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        j.id,
        j.title,
        j.company,
        j.location,
        1 - (j.embedding <=> query_embedding) as similarity
    FROM jobs j
    WHERE j.is_active = TRUE
      AND j.embedding IS NOT NULL
      AND (1 - (j.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY j.embedding <=> query_embedding
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Create function for user job matching
CREATE OR REPLACE FUNCTION get_job_recommendations(
    target_user_id uuid,
    similarity_threshold float DEFAULT 0.85,
    max_results int DEFAULT 20
)
RETURNS TABLE (
    job_id uuid,
    title text,
    company text,
    location text,
    similarity float,
    salary_min numeric,
    salary_max numeric
) AS $$
DECLARE
    user_embedding vector(1536);
BEGIN
    -- Get user's resume embedding
    SELECT resume_embedding INTO user_embedding
    FROM users
    WHERE id = target_user_id AND is_active = TRUE;

    IF user_embedding IS NULL THEN
        RAISE EXCEPTION 'User not found or has no resume embedding';
    END IF;

    RETURN QUERY
    SELECT 
        j.id,
        j.title,
        j.company,
        j.location,
        1 - (j.embedding <=> user_embedding) as similarity,
        j.salary_min,
        j.salary_max
    FROM jobs j
    LEFT JOIN applications a ON j.id = a.job_id AND a.user_id = target_user_id
    WHERE j.is_active = TRUE
      AND j.embedding IS NOT NULL
      AND a.id IS NULL -- Exclude already applied jobs
      AND (1 - (j.embedding <=> user_embedding)) >= similarity_threshold
    ORDER BY j.embedding <=> user_embedding
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Insert sample job categories
INSERT INTO job_categories (name, description) VALUES
    ('Technology', 'Software development, IT, and technology roles'),
    ('Marketing', 'Digital marketing, content, and advertising roles'),
    ('Sales', 'Sales development, account management, and business development'),
    ('Design', 'UI/UX design, graphic design, and creative roles'),
    ('Operations', 'Operations, logistics, and process management'),
    ('Finance', 'Accounting, financial analysis, and corporate finance'),
    ('Human Resources', 'Talent acquisition, HR operations, and people management'),
    ('Customer Support', 'Customer service, technical support, and success roles'),
    ('Data Science', 'Data analysis, machine learning, and analytics'),
    ('Product Management', 'Product strategy, roadmap, and product development')
ON CONFLICT (name) DO NOTHING;

-- Grant permissions to application user
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO karmsakha_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO karmsakha_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO karmsakha_app;

-- Create row level security policies (for multi-tenancy if needed in future)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Sample policy (can be customized based on tenant requirements)
CREATE POLICY user_isolation_policy ON users
    FOR ALL
    TO karmsakha_app
    USING (true); -- Simplified for now, can add tenant_id based filtering

CREATE POLICY application_isolation_policy ON applications
    FOR ALL
    TO karmsakha_app
    USING (true);

CREATE POLICY audit_isolation_policy ON audit_logs
    FOR ALL
    TO karmsakha_app
    USING (true);

-- Create materialized view for popular companies (for performance)
CREATE MATERIALIZED VIEW IF NOT EXISTS popular_companies AS
SELECT 
    c.id,
    c.name,
    c.industry,
    COUNT(j.id) as job_count,
    AVG(CASE WHEN a.status = 'applied' THEN 1.0 ELSE 0.0 END) as application_rate
FROM companies c
LEFT JOIN jobs j ON j.company = c.name
LEFT JOIN applications a ON a.job_id = j.id
WHERE j.is_active = TRUE
GROUP BY c.id, c.name, c.industry
HAVING COUNT(j.id) > 0
ORDER BY job_count DESC, application_rate DESC;

-- Create index on materialized view
CREATE INDEX IF NOT EXISTS idx_popular_companies_job_count ON popular_companies(job_count DESC);

-- Set up scheduled refresh for materialized view (requires pg_cron extension in production)
-- This would typically be handled by the application or a scheduled job

COMMIT;