# KarmSakha AutoPilot v3

Production-ready multi-tenant job platform that ingests Indian on-site jobs + remote international roles, ranks them against user résumés, and auto-applies through Stagehand-style browser agents while complying with DPDP Act 2023.

## 🎯 Success Metrics

- **Job Ingestion**: ≥50,000 Indian + ≥5,000 remote jobs/day
- **Matching**: Cosine similarity ≥0.85 vs résumé  
- **Application Rate**: ≥100 applications/hour/worker
- **Cost Target**: ₹15,000/month at 10,000 MAU

## 🏗️ Architecture

```
┌──────────────┐
│ Ingestors    │──▶ Postgres (jobs)
└──────┬───────┘
       │   vector search + hard filters
       ▼
┌──────────────┐
│   Ranker     │──▶ Redis apply_queue
└──────┬───────┘
       │
┌──────────────────┐
│ Stagehand pods   │—▶ Twilio (OTP)
└──────┬───────────┘
       │
┌──────────────┐
│   Audit S3   │
└──────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- Python 3.12+
- Docker & Docker Compose
- Terraform 1.5+
- kubectl & helm

### Development Setup

```bash
# Clone and install dependencies
git clone <repo-url>
cd karmsakha-autopilotv3
npm install

# Start development environment
docker-compose up -d postgres redis
npm run dev

# Run tests
npm test
npm run e2e
```

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# API Keys
OPENAI_API_KEY=sk-...
BRIGHT_DATA_PROXY_URL=http://...
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=...

# Security
JWT_SECRET=...
ENCRYPTION_KEY=...

# AWS (for production)
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
```

## 📦 Services

| Service | Tech Stack | Purpose |
|---------|------------|---------|
| **Ingestor** | Node.js + TypeScript + BullMQ | Multi-source job scraping |
| **Ranker** | Python + FastAPI + pgvector | AI-powered job matching |
| **Worker** | Node.js + Puppeteer + Stagehand | Automated job applications |
| **Gateway** | Node.js + GraphQL + Apollo | Unified API layer |
| **Frontend** | React + TypeScript + Tailwind | Admin dashboard |
| **Audit** | Node.js + S3 | DPDP Act compliance |

## 🔍 Job Sources

- **Indian**: Shine.com, Naukri.com, LinkedIn India
- **Remote**: RemoteOK, WeWorkRemotely, AngelList
- **ATS**: Greenhouse, Lever, Workday, SuccessFactors

## 🏭 Production Deployment

```bash
# Infrastructure
cd infra/terraform  
terraform init && terraform plan && terraform apply

# Kubernetes deployment
helm install karmsakha ./k8s/charts/

# CI/CD pipeline
git push origin main  # Triggers GitHub Actions
```

## 🧪 Testing

```bash
# Unit tests
npm test

# Integration tests  
npm run test:integration

# E2E tests
npm run e2e

# Load testing
npm run test:load
```

## 📊 Monitoring

- **Metrics**: Grafana Cloud dashboards
- **Logs**: Loki aggregation
- **Errors**: Sentry integration
- **Uptime**: AWS CloudWatch

## 🔒 Security & Compliance

- **DPDP Act 2023**: Automated data retention and deletion
- **Encryption**: AES-256 for PII data
- **Auth**: JWT with refresh tokens
- **Rate Limiting**: Redis-based throttling
- **Audit Trails**: Immutable S3 logs

## 📈 Scaling

- **Horizontal**: Kubernetes HPA based on CPU/memory
- **Vertical**: Spot instances for cost optimization  
- **Database**: Read replicas + connection pooling
- **Cache**: Redis Cluster for session storage

## 🤝 Contributing

1. Follow conventional commits
2. Write tests first (TDD)
3. Run linting: `npm run lint`
4. Type check: `npm run type-check`
5. Create PR with detailed description

## 📄 License

Proprietary - All rights reserved